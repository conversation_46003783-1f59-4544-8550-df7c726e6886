"use client";

import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { Submission as BaseSubmission } from "@/app/[locale]/(main)/project/[hashedId]/data/table/columns";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ChevronDown, ChevronRight, ArrowRight } from "lucide-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { TableInput } from "@/components/form-inputs/TableInput";
import axios from "@/lib/axios";
import { updateMultipleAnswersWithEndpoint } from "@/lib/api/submission";
import { fetchQuestionGroups } from "@/lib/api/question-groups";
import { fetchProjectById } from "@/lib/api/projects";
import { fetchFormBuilderData } from "@/lib/api/form-builder";
import { Project } from "@/types";
import {
  getVisibleQuestions,
  cleanupHiddenAnswers,
  restoreOriginalAnswers,
  validateVisibleQuestions,
  getNestedQuestions,
} from "@/lib/conditionalQuestions";
import NestedQuestionRenderer from "@/components/form-inputs/NestedQuestionRenderer";
import NestedGroupRenderer from "@/components/form-inputs/NestedGroupRenderer";
import {
  buildNestedGroups,
  createUnifiedFormItems,
  getUngroupedQuestions,
  initializeGroupExpansionState
} from "@/lib/utils/nestedGroups";

// Extend Submission type to include id in answers
interface Submission extends BaseSubmission {
  answers: Array<{
    id: number; // Changed to non-optional
    value: string | number;
    question: {
      inputType: string;
      id: number;
      label: string;
      type?: string;
    };
  }>;
}

interface EditFormProps {
  submission: Submission;
  projectId: number;
  submissionId: number;
  onClose?: () => void;
  onSave: () => void;
}

export function EditForm({
  submission,
  projectId,
  submissionId,
  onClose,
  onSave,
}: EditFormProps) {
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [originalAnswers, setOriginalAnswers] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);
  const [nestedQuestions, setNestedQuestions] = useState<
    Array<{
      question: Question;
      isVisible: boolean;
      isFollowUp: boolean;
      followUps: Array<{
        question: Question;
        isVisible: boolean;
      }>;
    }>
  >([]);
  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(
    {}
  );
  // Track which fields are being actively edited by the user
  const [userEditedFields, setUserEditedFields] = useState<Set<number>>(new Set());
  // Use refs to track previous state without causing re-renders
  const previousVisibleQuestionIdsRef = useRef<Set<number>>(new Set());
  const isInitializedRef = useRef(false);
  // Track the last answers hash to prevent infinite loops
  const lastAnswersHashRef = useRef<string>("");

  // Query keys
  const formBuilderDataQueryKey = ["formBuilderData", projectId];

  // Fetch form builder data (unified questions and groups) for projects
  const { data: formBuilderData, isLoading: isLoadingFormData, isError: isFormDataError } = useQuery({
    queryKey: formBuilderDataQueryKey,
    queryFn: () => fetchFormBuilderData({ projectId }),
    enabled: !!projectId,
  });

  // Extract questions and groups from the unified data
  const questions: Question[] = React.useMemo(() => {
    if (!formBuilderData?.items) {
      return [];
    }

    // Extract questions from the items array
    const extractedQuestions: Question[] = [];

    formBuilderData.items.forEach((item: any) => {
      if (item.type === "question") {
        extractedQuestions.push(item);
      } else if (item.type === "group" && item.questions) {
        // Add questions from groups
        item.questions.forEach((question: any) => {
          extractedQuestions.push(question);
        });
      }
    });

    return extractedQuestions;
  }, [formBuilderData]);

  // Extract question groups from the unified data
  const questionGroups: QuestionGroup[] = React.useMemo(() => {
    if (!formBuilderData?.items) {
      return [];
    }

    return formBuilderData.items
      .filter((item: any) => item.type === "group")
      .map((item: any) => ({
        ...item,
        question: item.questions || []
      }));
  }, [formBuilderData]);

  // Fetch project data to get the project name
  const { data: projectData } = useQuery<Project>({
    queryKey: ["project", projectId],
    queryFn: () => fetchProjectById({ projectId }),
    enabled: !!projectId,
  });

  // Show loading state
  if (isLoadingFormData) {
    return (
      <div className="w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700">
          Edit Submission{projectData?.name ? ` for ${projectData.name}` : ""}
        </h2>
        <div className="min-h-[60vh] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading form data...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (isFormDataError) {
    return (
      <div className="w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700">
          Edit Submission{projectData?.name ? ` for ${projectData.name}` : ""}
        </h2>
        <div className="min-h-[60vh] flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-500 mb-4">Error loading form data. Please try again.</p>
            <button
              onClick={() => queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey })}
              className="btn-primary"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Initialize answers from submission
  useEffect(() => {
    const initialAnswers: Record<string, any> = {};
    questions.forEach((question) => {
      if (question.inputType === "selectmany") {
        const selectManyAnswers = submission.answers.filter(
          (a) => a.question.id === question.id
        );
        initialAnswers[question.id] = selectManyAnswers
          .map((answer: { value: string | number }) => answer.value)
          .filter(
            (value: string | number) =>
              value != null && String(value).trim() !== ""
          );
      } else {
        const answer = submission.answers.find(
          (a) => a.question.id === question.id
        );
        initialAnswers[question.id] = answer?.value ?? "";
      }
    });
    setAnswers(initialAnswers);
    // Store a deep copy of the original answers for persistence
    setOriginalAnswers(JSON.parse(JSON.stringify(initialAnswers)));
  }, [questions, submission]);

  // Handle visibility and conditional logic
  useEffect(() => {
    if (!questions || Object.keys(originalAnswers).length === 0) {
      return;
    }

    // Create a hash of current answers to detect if this effect was triggered by our own setAnswers call
    const currentAnswersHash = JSON.stringify(answers);
    if (currentAnswersHash === lastAnswersHashRef.current) {
      return; // Skip if this is the same answers we just set
    }

    const newVisibleQuestions = getVisibleQuestions(questions, answers);
    const newVisibleQuestionIds = new Set(newVisibleQuestions.map(q => q.id));

    // Always update visible questions and nested structure
    setVisibleQuestions(newVisibleQuestions);
    setNestedQuestions(getNestedQuestions(questions, answers));

    // Initialize on first run
    if (!isInitializedRef.current) {
      previousVisibleQuestionIdsRef.current = newVisibleQuestionIds;
      isInitializedRef.current = true;
      lastAnswersHashRef.current = currentAnswersHash;
      return;
    }

    // Check if visible questions actually changed
    const visibleQuestionsChanged =
      newVisibleQuestionIds.size !== previousVisibleQuestionIdsRef.current.size ||
      [...newVisibleQuestionIds].some(id => !previousVisibleQuestionIdsRef.current.has(id));

    if (!visibleQuestionsChanged) {
      lastAnswersHashRef.current = currentAnswersHash;
      return; // No visibility changes, no need to process further
    }

    // Detect newly visible questions (conditional questions that just became visible)
    const newlyVisibleQuestionIds = new Set(
      [...newVisibleQuestionIds].filter(id => !previousVisibleQuestionIdsRef.current.has(id))
    );

    // Only restore answers for newly visible conditional questions that haven't been edited by the user
    let shouldUpdateAnswers = false;
    const restoredAnswers = { ...answers };

    if (newlyVisibleQuestionIds.size > 0) {
      newlyVisibleQuestionIds.forEach((questionId) => {
        const questionIdStr = questionId.toString();
        // Only restore if:
        // 1. The field hasn't been actively edited by the user
        // 2. The field currently has no value
        // 3. There's an original value to restore
        if (
          !userEditedFields.has(questionId) &&
          (answers[questionIdStr] === undefined || answers[questionIdStr] === "" ||
           (Array.isArray(answers[questionIdStr]) && answers[questionIdStr].length === 0)) &&
          originalAnswers[questionIdStr] !== undefined &&
          originalAnswers[questionIdStr] !== "" &&
          !(Array.isArray(originalAnswers[questionIdStr]) && originalAnswers[questionIdStr].length === 0)
        ) {
          restoredAnswers[questionIdStr] = originalAnswers[questionIdStr];
          shouldUpdateAnswers = true;
        }
      });
    }

    // Clean up answers for questions that are no longer visible
    const cleanedAnswers = cleanupHiddenAnswers(restoredAnswers, newVisibleQuestions);

    // Check if cleanup made changes
    const cleanupMadeChanges = Object.keys(cleanedAnswers).length !== Object.keys(restoredAnswers).length;

    // Update previous visible questions for next comparison
    previousVisibleQuestionIdsRef.current = newVisibleQuestionIds;

    // Only update answers if there are actual changes
    if (shouldUpdateAnswers || cleanupMadeChanges) {
      const newAnswersHash = JSON.stringify(cleanedAnswers);
      lastAnswersHashRef.current = newAnswersHash;
      setAnswers(cleanedAnswers);
    } else {
      lastAnswersHashRef.current = currentAnswersHash;
    }
  }, [questions, answers, originalAnswers]);

  // Build nested group structure - memoized to prevent recalculation
  const nestedQuestionGroups = useMemo(() => {
    return buildNestedGroups(questionGroups, questions);
  }, [questionGroups, questions]);

  // Initialize all groups (including nested ones) as expanded when questionGroups change
  useEffect(() => {
    if (nestedQuestionGroups.length > 0) {
      const initialExpandedState = initializeGroupExpansionState(nestedQuestionGroups, true);
      setExpandedGroups(initialExpandedState);
    }
  }, [nestedQuestionGroups.length]); // Only depend on length to avoid infinite loops

  // Get ungrouped questions - memoized to prevent recalculation
  const ungroupedQuestions = useMemo(() => {
    return getUngroupedQuestions(questions);
  }, [questions]);

  // Create a unified list of form items (groups and individual questions) for dynamic ordering
  const unifiedFormItems = useMemo(() => {
    return createUnifiedFormItems(nestedQuestionGroups, ungroupedQuestions);
  }, [nestedQuestionGroups, ungroupedQuestions]);

  // Toggle group expansion - memoized to prevent unnecessary re-renders
  const toggleGroupExpansion = useCallback((groupId: number) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupId]: !prev[groupId],
    }));
  }, []);

  const handleInputChange = useCallback((questionId: number, value: any) => {
    // Mark this field as user-edited to prevent automatic restoration
    setUserEditedFields((prev) => new Set(prev).add(questionId));

    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
    setErrors((prev) => ({
      ...prev,
      [questionId]: "",
    }));
  }, []);

  const validateForm = () => {
    // Only validate visible questions
    const newErrors = validateVisibleQuestions(visibleQuestions, answers);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const updateAnswersMutation = useMutation({
    mutationFn: async (answers: Record<string, any>) => {
      const formattedAnswers = questions.map((question) => {
        const answerValue = answers[question.id];
        const isSelectMany = question.inputType === "selectmany";
        const submissionAnswer = submission.answers.find(
          (sa) => sa.question.id === question.id
        );

        // For existing answers, use the answer ID
        // For new questions, we'll create a new answer
        const isNewQuestion = !submissionAnswer?.id;

        if (isNewQuestion) {
        }

        if (isSelectMany && Array.isArray(answerValue)) {
          if (answerValue.length > 0) {
            let questionOptionIds: number[] = [];
            if (question.questionOptions) {
              questionOptionIds = answerValue
                .map((val: string) => {
                  const option = question.questionOptions.find(
                    (opt) => opt.label === val
                  );
                  return option?.id;
                })
                .filter((id): id is number => id !== undefined);
            }

            const baseAnswer = {
              projectId,
              questionId: question.id, // Always include questionId
              answerType: question.inputType,
              value: answerValue.join(", "),
              questionOptionId: questionOptionIds,
              isOtherOption: false,
              formSubmissionId: submissionId,
            };

            // If it's a new question, just use the base answer
            // If it's an existing answer, add the id
            if (isNewQuestion) {
              return baseAnswer;
            } else {
              return {
                ...baseAnswer,
                id: submissionAnswer.id,
              };
            }
          }
          return null;
        } else {
          let formattedValue: string | number | boolean | undefined;
          if (
            question.inputType === "number" ||
            question.inputType === "decimal"
          ) {
            formattedValue = answerValue ? Number(answerValue) : undefined;
          } else if (
            question.inputType === "date" ||
            question.inputType === "dateandtime"
          ) {
            formattedValue = answerValue || undefined;
          } else if (question.inputType === "table") {
            // For table input type, convert the array of cell values to JSON string
            formattedValue =
              Array.isArray(answerValue) && answerValue.length > 0
                ? JSON.stringify(answerValue)
                : undefined;
          } else {
            formattedValue = answerValue ? String(answerValue) : undefined;
          }

          if (formattedValue === undefined) {
            return null;
          }

          let questionOptionId: number | undefined;
          if (
            question.inputType === "selectone" &&
            answerValue &&
            question.questionOptions
          ) {
            const option = question.questionOptions.find(
              (opt) => opt.label === answerValue
            );
            questionOptionId = option?.id;
          }

          const baseAnswer = {
            projectId,
            questionId: question.id, // Always include questionId
            answerType: question.inputType,
            value: formattedValue,
            questionOptionId,
            isOtherOption: false,
            formSubmissionId: submissionId,
          };

          // If it's a new question, just use the base answer
          // If it's an existing answer, add the id
          if (isNewQuestion) {
            return baseAnswer;
          } else {
            return {
              ...baseAnswer,
              id: submissionAnswer.id,
            };
          }
        }
      });

      // Filter out null values
      const validAnswers = formattedAnswers.filter(
        (answer) => answer !== null
      ) as any[];

      if (validAnswers.length === 0) {
        throw new Error("No valid answers with IDs to submit");
      }

      const simplifiedAnswers = validAnswers
        .map((answer) => {
          // For existing answers (with id)
          if (answer.id) {
            return {
              id: answer.id,
              questionId: answer.questionId, // Include questionId for all answers
              projectId,
              value: answer.value,
              answerType: answer.answerType,
              questionOptionId: answer.questionOptionId,
              isOtherOption: answer.isOtherOption || false,
              formSubmissionId: answer.formSubmissionId,
            };
          }
          // For new questions (with questionId)
          else if (answer.questionId) {
            return {
              questionId: answer.questionId,
              projectId,
              value: answer.value,
              answerType: answer.answerType,
              questionOptionId: answer.questionOptionId,
              isOtherOption: answer.isOtherOption || false,
              formSubmissionId: answer.formSubmissionId,
            };
          }
          return null;
        })
        .filter((answer) => answer !== null);

      try {
        // Cast the array to any to bypass TypeScript's strict checking
        // The backend can handle both formats (with id or with questionId)
        const data = await updateMultipleAnswersWithEndpoint(
          simplifiedAnswers as any,
          projectId
        );

        return data;
      } catch (error: any) {
        console.error("Error with /answers/multiple endpoint:", error);
        if (error.response) {
          console.error(
            "Error response data:",
            JSON.stringify(error.response.data, null, 2)
          );
          console.error("Error response status:", error.response.status);
          console.error("Error response headers:", error.response.headers);
        }

        const results = [];
        const failedQuestions: number[] = [];

        for (const answer of validAnswers) {
          try {
            // For existing answers with ID, use PATCH
            if (answer.id) {
              const { data } = await axios.patch(
                `/answers/${answer.id}?projectId=${projectId}`,
                {
                  id: answer.id,
                  questionId: answer.questionId, // Include questionId for all answers
                  projectId,
                  value: answer.value,
                  answerType: answer.answerType,
                  questionOptionId: answer.questionOptionId,
                  isOtherOption: answer.isOtherOption || false,
                  formSubmissionId: answer.formSubmissionId,
                }
              );
              results.push(data);
            }
            // For new questions, use POST to create a new answer
            else if (answer.questionId) {
              const { data } = await axios.post(
                `/answers?projectId=${projectId}`,
                {
                  submissionId: answer.formSubmissionId,
                  questionId: answer.questionId,
                  value: answer.value,
                  answerType: answer.answerType,
                  questionOptionId: answer.questionOptionId,
                  isOtherOption: answer.isOtherOption || false,
                }
              );
              results.push(data);
            }
          } catch (individualError: any) {
            const identifier = answer.id || answer.questionId;
            console.error(
              `Error handling answer ${identifier}:`,
              individualError
            );
            if (individualError.response) {
              console.error(
                "Individual error response data:",
                JSON.stringify(individualError.response.data, null, 2)
              );
            }
            failedQuestions.push(identifier);
          }
        }

        if (failedQuestions.length > 0) {
          throw new Error(
            `Failed to update answers with IDs: ${failedQuestions.join(", ")}`
          );
        }

        if (results.length > 0) {
          dispatch(
            showNotification({
              message:
                "Submission updated successfully using individual updates. Consider checking the bulk update endpoint.",
              type: "warning",
            })
          );
          return results;
        }

        throw error;
      }
    },
    onSuccess: () => {
      dispatch(
        showNotification({
          message:
            "Submission updated successfully. You can continue editing if needed.",
          type: "success",
        })
      );
      // Clear user-edited fields tracking after successful save
      setUserEditedFields(new Set());
      onSave();
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "Failed to update submission. Please check your input and try again.";
      dispatch(
        showNotification({
          message: errorMessage,
          type: "error",
        })
      );
      console.error("Update Error:", {
        message: errorMessage,
        status: error.response?.status,
        data: JSON.stringify(error.response?.data, null, 2),
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsSubmitting(true);
    updateAnswersMutation.mutate(answers);
  };

  // Helper function to check if a question is a follow-up question
  const isFollowUpQuestion = (questionId: number): boolean => {
    return questions.some((q) =>
      q.questionOptions?.some((option) => option.nextQuestionId === questionId)
    );
  };

  // Helper function to check if a question has follow-up questions
  const hasFollowUpQuestions = (question: Question): boolean => {
    return question.questionOptions?.some((option) => option.nextQuestionId) || false;
  };

  // Render a single question with its input and visual indicators
  const renderQuestion = (question: Question) => {
    const isFollowUp = isFollowUpQuestion(question.id);
    const hasFollowUps = hasFollowUpQuestions(question);

    return (
      <div
        key={question.id}
        className={`border rounded-md p-4 ${
          isFollowUp
            ? "border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20"
            : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
        }`}
      >
        <div className="mb-2">
          <div className="flex items-center gap-2">
            <Label className="text-base font-medium">
              {question.label}
              {question.isRequired && (
                <span className="text-red-500 ml-1">*</span>
              )}
            </Label>
            {/* Visual indicators */}
            {isFollowUp && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                <ArrowRight className="w-3 h-3 mr-1" />
                Follow-up
              </span>
            )}
            {hasFollowUps && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-700 dark:bg-accent-700/20 dark:text-accent-200">
                Has conditions
              </span>
            )}
          </div>
          {question.hint && (
            <p className={`text-sm mt-1 ${
              isFollowUp
                ? "text-primary-700 dark:text-primary-300"
                : "text-muted-foreground"
            }`}>
              {question.hint}
            </p>
          )}
          {errors[question.id] && (
            <p className="text-sm text-red-500 mt-1">
              {errors[question.id]}
            </p>
          )}
        </div>
        <div className="mt-2">{renderQuestionInput(question)}</div>
      </div>
    );
  };

  const renderQuestionInput = (question: Question) => {
    const value =
      answers[question.id] ?? (question.inputType === "selectmany" ? [] : "");

    switch (question.inputType) {
      case "text":
        if (question.hint?.includes("multiline")) {
          return (
            <Textarea
              value={value}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                handleInputChange(question.id, e.target.value)
              }
              placeholder={question.hint || "Your answer"}
              required={question.isRequired}
            />
          );
        }
        return (
          <input
            className="input-field w-full"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.hint || "Your answer"}
            required={question.isRequired}
          />
        );

      case "number":
        return (
          <input
            className="input-field w-full"
            type="number"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.hint || "Your answer"}
            required={question.isRequired}
          />
        );

      case "decimal":
        return (
          <input
            className="input-field w-full"
            type="number"
            step="any"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.hint || "Your answer"}
            required={question.isRequired}
          />
        );

      case "selectone":
        return (
          <RadioGroup
            value={value}
            onValueChange={(val: string) => handleInputChange(question.id, val)}
            required={question.isRequired}
          >
            <div className="space-y-2">
              {question.questionOptions?.map((option, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.label}
                    id={`option-${option.id}`}
                  />
                  <Label
                    htmlFor={`option-${option.id}`}
                    className="cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>
        );

      case "selectmany":
        return (
          <div className="space-y-2">
            {question.questionOptions?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`option-${option.id}`}
                  checked={(value || []).includes(option.label)}
                  onCheckedChange={(checked) => {
                    const currentValues = value || [];
                    const newValues = checked
                      ? [...currentValues, option.label]
                      : currentValues.filter((v: string) => v !== option.label);
                    handleInputChange(question.id, newValues);
                  }}
                />
                <Label
                  htmlFor={`option-${option.id}`}
                  className="cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      case "date":
        return (
          <div className="relative">
            <input
              className="input-field w-full"
              type="date"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.hint || "Select date"}
              required={question.isRequired}
            />
          </div>
        );

      case "dateandtime":
        return (
          <div className="relative">
            <input
              className="input-field w-full"
              type="time"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.hint || "Select time"}
              required={question.isRequired}
            />
          </div>
        );

      case "table":
        return (
          <TableInput
            questionId={question.id}
            value={value}
            onChange={(cellValues) =>
              handleInputChange(question.id, cellValues)
            }
            required={question.isRequired}
            tableLabel={question.label}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700">
      <h2 className="text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700">
        Edit Submission{projectData?.name ? ` for ${projectData.name}` : ""}
      </h2>
      <form onSubmit={handleSubmit} className="p-6">
        <div className="space-y-6">
          {questions.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-muted-foreground">This form has no questions yet.</p>
            </div>
          ) : (
            // Render unified form items (groups and individual questions) in order
            unifiedFormItems.map((item) => {
              if (item.type === 'group') {
                const group = item.data as QuestionGroup;
                const isExpanded = expandedGroups[group.id];

                return (
                  <NestedGroupRenderer
                    key={`group-${group.id}`}
                    group={group}
                    nestingLevel={0}
                    visibleQuestions={visibleQuestions}
                    nestedQuestions={nestedQuestions}
                    renderQuestionInput={renderQuestionInput}
                    errors={errors}
                    onToggleExpansion={toggleGroupExpansion}
                    isExpanded={isExpanded}
                    expandedGroups={expandedGroups}
                    className=""
                  />
                );
              } else {
                const question = item.data as Question;
                // Only render ungrouped questions that are visible
                if (!visibleQuestions.some((vq) => vq.id === question.id)) {
                  return null;
                }

                // Find the nested question structure for this question
                const nestedQuestion = nestedQuestions.find(
                  (nq) => nq.question.id === question.id
                );

                if (nestedQuestion) {
                  return (
                    <NestedQuestionRenderer
                      key={question.id}
                      questionGroup={nestedQuestion}
                      renderQuestionInput={renderQuestionInput}
                      errors={errors}
                      className=""
                    />
                  );
                }

                return renderQuestion(question);
              }
            })
          )}

          {questions.length > 0 && (
            <div className="mt-6 flex justify-end gap-4">
              <button
                className="btn-primary bg-neutral-500 hover:bg-neutral-600"
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                className="btn-primary"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Saving..." : "Save Changes"}
              </button>
            </div>
          )}
        </div>
      </form>
    </div>
  );
}