"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Question, QuestionGroup } from "@/types/formBuilder";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { decode } from "@/lib/encodeDecode";
import { fetchQuestions } from "@/lib/api/form-builder";
import { fetchQuestionGroups } from "@/lib/api/question-groups";
import { createAnswerSubmission, fetchProjectById } from "@/lib/api/projects";
import { Project } from "@/types";
import Spinner from "@/components/general/Spinner";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ChevronDown, ChevronRight, ArrowRight } from "lucide-react";

import { useDispatch } from "react-redux";
import { showNotification } from "@/redux/slices/notificationSlice";
import { TableInput } from "@/components/form-inputs/TableInput";
import {
  getVisibleQuestions,
  cleanupHiddenAnswers,
  validateVisibleQuestions,
  getNestedQuestions,
} from "@/lib/conditionalQuestions";
import NestedQuestionRenderer from "@/components/form-inputs/NestedQuestionRenderer";

export default function FormTestPage() {
  const dispatch = useDispatch();
  const { hashedId } = useParams();
  const hashedIdString = hashedId as string;
  const projectId = decode(hashedIdString);

  // Log URL parameters
  // console.log("Hashed ID:", hashedIdString);
  // console.log("Project ID:", projectId);

  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);
  const [nestedQuestions, setNestedQuestions] = useState<
    Array<{
      question: Question;
      isVisible: boolean;
      isFollowUp: boolean;
      followUps: Array<{
        question: Question;
        isVisible: boolean;
      }>;
    }>
  >([]);
  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(
    {}
  );

  const {
    data: questionsData,
    isLoading,
    isError,
  } = useQuery<Question[]>({
    queryKey: ["questions", projectId],
    queryFn: () => fetchQuestions({ projectId: projectId! }),
    enabled: !!projectId,
  });

  // Fetch question groups for the project
  const { data: questionGroups = [] } = useQuery<QuestionGroup[]>({
    queryKey: ["questionGroups", projectId],
    queryFn: () => fetchQuestionGroups({ projectId: projectId! }),
    enabled: !!projectId,
  });

  // Fetch project data to get the project name
  const { data: projectData } = useQuery<Project>({
    queryKey: ["project", projectId],
    queryFn: () => fetchProjectById({ projectId: projectId! }),
    enabled: !!projectId,
  });

  useEffect(() => {
    if (questionsData) {
      // Log questions data
      // console.log("Questions Data:", questionsData);
      const initialAnswers: Record<string, any> = {};
      questionsData.forEach((question) => {
        initialAnswers[question.id] =
          question.inputType === "selectmany" ? [] : "";
      });
      setAnswers(initialAnswers);
      // Log initial answers
      // console.log("Initial Answers:", initialAnswers);
    }
  }, [questionsData]);

  // Update visible questions when answers or questions change
  useEffect(() => {
    if (questionsData) {
      const newVisibleQuestions = getVisibleQuestions(questionsData, answers);
      setVisibleQuestions(newVisibleQuestions);

      // Calculate nested question structure
      const newNestedQuestions = getNestedQuestions(questionsData, answers);
      setNestedQuestions(newNestedQuestions);

      // Clean up answers for questions that are no longer visible
      const cleanedAnswers = cleanupHiddenAnswers(answers, newVisibleQuestions);
      if (Object.keys(cleanedAnswers).length !== Object.keys(answers).length) {
        setAnswers(cleanedAnswers);
      }
    }
  }, [questionsData, answers]);

  // Initialize all groups as expanded when questionGroups change
  useEffect(() => {
    if (questionGroups.length > 0) {
      const initialExpandedState: Record<number, boolean> = {};
      questionGroups.forEach((group) => {
        initialExpandedState[group.id] = true;
      });
      setExpandedGroups(initialExpandedState);
    }
  }, [questionGroups.length]); // Only depend on length to avoid infinite loops

  // Group questions by their group ID - memoized to prevent recalculation
  const groupedQuestions = useMemo(() => {
    return questionGroups.reduce(
      (acc: Record<number, Question[]>, group: QuestionGroup) => {
        acc[group.id] =
          questionsData?.filter((q) => q.questionGroupId === group.id) || [];
        return acc;
      },
      {} as Record<number, Question[]>
    );
  }, [questionGroups, questionsData]);

  // Get ungrouped questions - memoized to prevent recalculation
  const ungroupedQuestions = useMemo(() => {
    return (
      questionsData?.filter(
        (q) => q.questionGroupId === null || q.questionGroupId === undefined
      ) || []
    );
  }, [questionsData]);

  // Create a unified list of form items (groups and individual questions) for dynamic ordering
  const unifiedFormItems = useMemo(() => {
    const items: Array<{
      type: "group" | "question";
      data: QuestionGroup | Question;
      order: number;
      originalPosition?: number;
    }> = [];

    // Add question groups
    questionGroups.forEach((group: QuestionGroup) => {
      // For groups, find the minimum position of questions in the group
      const groupQuestions =
        questionsData?.filter((q) => q.questionGroupId === group.id) || [];
      const minQuestionPosition =
        groupQuestions.length > 0
          ? Math.min(...groupQuestions.map((q) => q.position))
          : group.order;

      items.push({
        type: "group",
        data: group,
        order: minQuestionPosition,
        originalPosition: minQuestionPosition,
      });
    });

    // Add ungrouped questions
    ungroupedQuestions.forEach((question: Question) => {
      items.push({
        type: "question",
        data: question,
        order: question.position,
        originalPosition: question.position,
      });
    });

    // Sort by order/position with secondary sort for consistency
    return items.sort((a, b) => {
      if (a.order === b.order) {
        return (
          (a.originalPosition || a.order) - (b.originalPosition || b.order)
        );
      }
      return a.order - b.order;
    });
  }, [questionGroups, ungroupedQuestions, questionsData]);

  // Toggle group expansion - memoized to prevent unnecessary re-renders
  const toggleGroupExpansion = useCallback((groupId: number) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupId]: !prev[groupId],
    }));
  }, []);

  const submitAnswersMutation = useMutation({
    mutationFn: async (answers: Record<string, any>) => {
      // Transform answers into the format expected by createAnswerSubmission
      // Only submit answers for visible questions
      const formattedAnswers =
        questionsData
          ?.map((question) => {
            const answerValue = answers[question.id];
            const isSelectMany = question.inputType === "selectmany";
            const isSelectOne = question.inputType === "selectone";

            // For non-select questions, skip if no value
            if (!isSelectMany && !isSelectOne) {
              if (answerValue === undefined || answerValue === null || answerValue === "") {
                return null;
              }
            }

            // For selectone, skip if no value
            if (isSelectOne && (!answerValue || answerValue.trim() === "")) {
              return null;
            }

            // For selectmany, we'll always include it even if empty (backend expects it)

            // Initialize questionOptionId
            let questionOptionId: number | number[] | undefined;

            // Handle questionOptionId for selectmany and selectone
            if (isSelectMany && Array.isArray(answerValue) && question.questionOptions) {
              const optionIds = answerValue
                .map((val: string) => {
                  const option = question.questionOptions.find(
                    (opt) => opt.label === val
                  );
                  return option?.id;
                })
                .filter((id: number | undefined) => id !== undefined) as number[];

              // For selectmany, questionOptionId MUST be an array (even if empty)
              questionOptionId = optionIds.length > 0 ? optionIds : [];
            } else if (isSelectOne && answerValue && question.questionOptions) {
              const option = question.questionOptions.find(
                (opt) => opt.label === answerValue
              );
              // For selectone, questionOptionId MUST be a single number
              questionOptionId = option?.id;

              // If we can't find the option ID, skip this answer
              if (questionOptionId === undefined) {
                console.warn(`Could not find option ID for selectone question ${question.id} with value "${answerValue}"`);
                return null;
              }
            }

            // Convert value to the correct type
            let formattedValue: string | number | boolean | undefined;
            if (isSelectMany) {
              // For selectmany, send the selected labels as a comma-separated string
              formattedValue = Array.isArray(answerValue) ? answerValue.join(", ") : "";
            } else if (question.inputType === "number" || question.inputType === "decimal") {
              formattedValue = answerValue ? Number(answerValue) : undefined;
            } else if (
              question.inputType === "date" ||
              question.inputType === "dateandtime"
            ) {
              formattedValue = answerValue || undefined;
            } else if (question.inputType === "table") {
              // For table input type, convert the array of cell values to JSON string
              formattedValue =
                Array.isArray(answerValue) && answerValue.length > 0
                  ? JSON.stringify(answerValue)
                  : undefined;
            } else {
              formattedValue = answerValue ? String(answerValue) : undefined;
            }

            // Ensure we have a valid value
            if (formattedValue === undefined || formattedValue === null) {
              return null;
            }

            // Determine questionOptionId value based on question type
            let finalQuestionOptionId: number | number[] | undefined;

            if (isSelectMany) {
              // For selectmany, questionOptionId MUST always be an array (backend requirement)
              finalQuestionOptionId = Array.isArray(questionOptionId) ? questionOptionId : [];
            } else if (isSelectOne) {
              // For selectone, questionOptionId must be a number or undefined
              finalQuestionOptionId = typeof questionOptionId === "number" ? questionOptionId : undefined;
            } else {
              // For other input types, questionOptionId should be undefined
              finalQuestionOptionId = undefined;
            }

            // Create the answer object with all required fields for backend validation
            const answer: any = {
              projectId: Number(projectId),
              questionId: question.id,
              answerType: String(question.inputType),
              value: formattedValue,
              isOtherOption: false,
            };

            // Add questionOptionId only if it's defined and valid
            if (finalQuestionOptionId !== undefined) {
              answer.questionOptionId = finalQuestionOptionId;
            }

            return answer;
          })
          .filter((answer) => answer !== null) || []; // Remove null entries

      // Validate that we have at least some answers to submit
      if (formattedAnswers.length === 0) {
        throw new Error("No valid answers to submit. Please fill out at least one field.");
      }

      // Log submission data for debugging
      console.log("Submission Data:", {
        projectId,
        formattedAnswers,
        totalQuestions: questionsData?.length,
        answeredQuestions: formattedAnswers.length,
      });

      // Additional detailed logging for all questions to debug the issue
      console.log("All Questions Debug:", formattedAnswers.map(q => {
        const qWithOptions = q as any; // Type assertion to access questionOptionId
        return {
          questionId: q.questionId,
          answerType: q.answerType,
          value: q.value,
          questionOptionId: qWithOptions.questionOptionId,
          questionOptionIdType: Array.isArray(qWithOptions.questionOptionId) ? 'array' : typeof qWithOptions.questionOptionId,
          questionOptionIdLength: Array.isArray(qWithOptions.questionOptionId) ? qWithOptions.questionOptionId.length : 'N/A',
          isOtherOption: qWithOptions.isOtherOption,
          projectId: q.projectId
        };
      }));

      // Validate payload against expected backend schema
      console.log("Backend Schema Validation Check:");
      formattedAnswers.forEach((answer, index) => {
        const qWithOptions = answer as any;
        console.log(`Answer ${index + 1}:`, {
          hasProjectId: typeof answer.projectId === 'number',
          hasQuestionId: typeof answer.questionId === 'number',
          hasAnswerType: typeof answer.answerType === 'string',
          hasValue: answer.value !== undefined,
          hasIsOtherOption: typeof qWithOptions.isOtherOption === 'boolean',
          questionOptionIdValidation: answer.answerType === 'selectmany'
            ? Array.isArray(qWithOptions.questionOptionId)
            : answer.answerType === 'selectone'
              ? (qWithOptions.questionOptionId === undefined || typeof qWithOptions.questionOptionId === 'number')
              : qWithOptions.questionOptionId === undefined,
          allRequiredFieldsPresent: !!(answer.projectId && answer.questionId && answer.answerType && qWithOptions.isOtherOption !== undefined)
        });
      });

      // Call createAnswerSubmission
      return await createAnswerSubmission(formattedAnswers);
    },
    onSuccess: () => {
      dispatch(
        showNotification({
          message: "Form submitted successfully",
          type: "success",
        })
      );
      setAnswers({}); // Reset form
      // console.log("Submission Success:", data);

      // Dispatch custom event to notify other components about the submission
      window.dispatchEvent(new Event("form-submitted"));

      // Additionally, store in localStorage to notify other tabs/windows
      localStorage.setItem("form_submitted", Date.now().toString());
    },
    onError: (error: any) => {
      dispatch(
        showNotification({
          message: "Failed to submit form. Please try again.",
          type: "error",
        })
      );
      console.error("Submission Error:", error);
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  // Update visible questions when answers or questionsData change
  useEffect(() => {
    if (questionsData) {
      const newVisibleQuestions = getVisibleQuestions(questionsData, answers);
      setVisibleQuestions(newVisibleQuestions);

      // Calculate nested question structure
      const newNestedQuestions = getNestedQuestions(questionsData, answers);
      setNestedQuestions(newNestedQuestions);

      // Clean up answers for questions that are no longer visible
      const cleanedAnswers = cleanupHiddenAnswers(answers, newVisibleQuestions);
      if (Object.keys(cleanedAnswers).length !== Object.keys(answers).length) {
        setAnswers(cleanedAnswers);
      }
    }
  }, [questionsData, answers]);

  const handleInputChange = useCallback((questionId: number, value: any) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
    setErrors((prev) => ({
      ...prev,
      [questionId]: "",
    }));
  }, []);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    // Only validate visible questions
    visibleQuestions.forEach((question) => {
      if (question.isRequired) {
        const value = answers[question.id];
        if (
          (typeof value === "string" && !value.trim()) ||
          (Array.isArray(value) && value.length === 0) ||
          value === undefined ||
          value === null
        ) {
          newErrors[question.id] = `${question.label} is required`;
          isValid = false;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    setIsSubmitting(true);
    submitAnswersMutation.mutate(answers);
  };

  // Helper function to check if a question is a follow-up question
  const isFollowUpQuestion = (questionId: number): boolean => {
    if (!questionsData) return false;
    return questionsData.some((q) =>
      q.questionOptions?.some((option) => option.nextQuestionId === questionId)
    );
  };

  // Helper function to check if a question has follow-up questions
  const hasFollowUpQuestions = (question: Question): boolean => {
    return question.questionOptions?.some((option) => option.nextQuestionId) || false;
  };

  const renderQuestionInput = (question: Question) => {
    const value =
      answers[question.id] ?? (question.inputType === "selectmany" ? [] : "");

    switch (question.inputType) {
      case "text":
        if (question.hint?.includes("multiline")) {
          return (
            <Textarea
              value={value}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                handleInputChange(question.id, e.target.value)
              }
              placeholder={question.placeholder || "Your answer"}
              required={question.isRequired}
            />
          );
        }
        return (
          <input
            className="input-field w-full"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.placeholder || "Your answer"}
            required={question.isRequired}
          />
        );

      case "number":
        return (
          <input
            className="input-field w-full"
            type="number"
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.placeholder || "Your answer"}
            required={question.isRequired}
          />
        );

      case "decimal":
        return (
          <input
            className="input-field w-full"
            type="number"
            step={"any"}
            value={value}
            onChange={(e) => handleInputChange(question.id, e.target.value)}
            placeholder={question.placeholder || "Your answer"}
            required={question.isRequired}
          />
        );

      case "selectone":
        return (
          <RadioGroup
            value={value}
            onValueChange={(val: string) => handleInputChange(question.id, val)}
            required={question.isRequired}
          >
            <div className="space-y-2">
              {question.questionOptions?.map((option) => (
                <div key={`option-${option.id}`} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.label}
                    id={`option-${question.id}-${option.id}`}
                  />
                  <Label
                    htmlFor={`option-${question.id}-${option.id}`}
                    className="cursor-pointer"
                  >
                    {option.label}
                  </Label>
                  {option.sublabel && (
                    <p className="text-sm text-neutral-700 ml-4">
                      {`(${option.sublabel})`}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </RadioGroup>
        );

      case "selectmany":
        return (
          <div className="space-y-2">
            {question.questionOptions?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`option-${option.id}`}
                  checked={(value || []).includes(option.label)}
                  onCheckedChange={(checked) => {
                    const currentValues = value || [];
                    const newValues = checked
                      ? [...currentValues, option.label]
                      : currentValues.filter((v: string) => v !== option.label);
                    handleInputChange(question.id, newValues);
                  }}
                />
                <Label
                  htmlFor={`option-${option.id}`}
                  className="cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      case "date":
        return (
          <div className="relative">
            <input
              className="input-field w-full"
              type="date"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.placeholder || "Select date"}
              required={question.isRequired}
            />
          </div>
        );

      case "dateandtime":
        return (
          <div className="relative">
            <input
              className="input-field w-full"
              type="time"
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.placeholder || "Select time"}
              required={question.isRequired}
            />
          </div>
        );

      case "table":
        return (
          <TableInput
            questionId={question.id}
            value={value}
            onChange={(cellValues) =>
              handleInputChange(question.id, cellValues)
            }
            required={question.isRequired}
            tableLabel={question.label}
          />
        );

      default:
        return null;
    }
  };

  // Render a single question with its input and visual indicators
  const renderQuestion = (question: Question) => {
    const isFollowUp = isFollowUpQuestion(question.id);
    const hasFollowUps = hasFollowUpQuestions(question);

    return (
      <div
        key={question.id}
        className={`border rounded-md p-4 ${
          isFollowUp
            ? "border-primary-200 dark:border-primary-700 bg-primary-100 dark:bg-primary-900/20"
            : "border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
        }`}
      >
        <div className="mb-2">
          <div className="flex items-center gap-2">
            <Label className="text-base font-medium">
              {question.label}
              {question.isRequired && (
                <span className="text-red-500 ml-1">*</span>
              )}
            </Label>
            {/* Visual indicators */}
            {isFollowUp && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-200 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                <ArrowRight className="w-3 h-3 mr-1" />
                Follow-up
              </span>
            )}
            {hasFollowUps && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-200 text-accent-800 dark:bg-accent-700/20 dark:text-accent-200">
                Has conditions
              </span>
            )}
          </div>
          {question.hint && (
            <p className={`text-sm mt-1 ${
              isFollowUp
                ? "text-primary-700 dark:text-primary-300"
                : "text-muted-foreground"
            }`}>
              {question.hint}
            </p>
          )}
          {errors[question.id] && (
            <p className="text-sm text-red-500 mt-1">
              {errors[question.id]}
            </p>
          )}
        </div>
        <div className="mt-2">{renderQuestionInput(question)}</div>
      </div>
    );
  };

  if (isLoading) return <Spinner />;
  if (isError || !questionsData) {
    return (
      <p className="text-sm text-red-500">
        Error loading form. Please try again.
      </p>
    );
  }

  return (
    <div className="min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6">
      <div className="w-full max-w-screen-lg bg-neutral-100 dark:bg-gray-800 rounded-md shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-2xl font-semibold p-6 border-b border-gray-200 dark:border-gray-700">
          Test Form
        </h2>
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-6">
            {!questionsData || questionsData.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  This form has no questions yet.
                </p>
              </div>
            ) : (
              // Render unified form items (groups and individual questions) with conditional logic
              unifiedFormItems.map((item) => {
                if (item.type === "group") {
                  const group = item.data as QuestionGroup;
                  const groupQuestions = groupedQuestions[group.id] || [];
                  const visibleGroupQuestions = groupQuestions.filter((q) =>
                    visibleQuestions.some((vq) => vq.id === q.id)
                  );
                  const isExpanded = expandedGroups[group.id];

                  if (visibleGroupQuestions.length === 0) return null;

                  return (
                    <div
                      key={`group-${group.id}`}
                      className="border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800"
                    >
                      {/* Group Header */}
                      <div
                        className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => toggleGroupExpansion(group.id)}
                      >
                        <div className="flex items-center space-x-2">
                          {isExpanded ? (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-gray-500" />
                          )}
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            {group.title}
                          </h3>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            ({visibleGroupQuestions.length} visible question{visibleGroupQuestions.length !== 1 ? 's' : ''})
                          </span>
                        </div>
                      </div>

                      {/* Group Content with Nested Questions */}
                      {isExpanded && (
                        <div className="p-4 space-y-4">
                          {nestedQuestions
                            .filter((nq) =>
                              groupQuestions.some(
                                (gq) => gq.id === nq.question.id
                              )
                            )
                            .map((questionGroup) => (
                              <NestedQuestionRenderer
                                key={questionGroup.question.id}
                                questionGroup={questionGroup}
                                renderQuestionInput={renderQuestionInput}
                                errors={errors}
                                className=""
                              />
                            ))}
                        </div>
                      )}
                    </div>
                  );
                } else {
                  const question = item.data as Question;
                  // Only render ungrouped questions that are visible
                  if (!visibleQuestions.some((vq) => vq.id === question.id)) {
                    return null;
                  }

                  // Find the nested question structure for this question
                  const nestedQuestion = nestedQuestions.find(
                    (nq) => nq.question.id === question.id
                  );

                  if (nestedQuestion) {
                    return (
                      <NestedQuestionRenderer
                        key={question.id}
                        questionGroup={nestedQuestion}
                        renderQuestionInput={renderQuestionInput}
                        errors={errors}
                        className=""
                      />
                    );
                  }

                  return renderQuestion(question);
                }
              })
            )}

            {/* Submit Button and Status Messages */}
            {questionsData.length > 0 && (
              <div className="mt-6 flex justify-end">
                <button
                  className="btn-primary"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit Form"}
                </button>
              </div>
            )}

            {questionsData.length === 0 && (
              <div className="text-center py-12">
                <p className="text-muted-foreground">
                  This form has no questions yet.
                </p>
              </div>
            )}

            {questionsData &&
              questionsData.length > 0 &&
              visibleQuestions.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    No questions are currently visible. Please check your form
                    configuration.
                  </p>
                </div>
              )}
          </div>
        </form>
      </div>
    </div>
  );
}
