{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/table.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\nexport interface TableColumn {\r\n  id: number;\r\n  columnName: string;\r\n  parentColumnId?: number;\r\n  childColumns?: TableColumn[];\r\n}\r\n\r\nexport interface TableRow {\r\n  id: number;\r\n  rowsName: string;\r\n}\r\n\r\nexport interface CellValue {\r\n  columnId: number;\r\n  rowsId: number;\r\n  value: string;\r\n  code?: string;\r\n}\r\n\r\nexport interface TableQuestion {\r\n  id: number;\r\n  label: string;\r\n  inputType: string;\r\n  tableColumns: TableColumn[];\r\n  tableRows: TableRow[];\r\n}\r\n\r\n// Fetch table structure (columns and rows)\r\nexport const fetchTableStructure = async (questionId: number) => {\r\n  try {\r\n    if (!questionId || isNaN(questionId)) {\r\n      console.error(\"Invalid questionId:\", questionId);\r\n      throw new Error(\"Invalid question ID provided\");\r\n    }\r\n\r\n    // First try the table-questions endpoint\r\n    try {\r\n      const response = await axios.get(`/table-questions/${questionId}`);\r\n\r\n      // Check if the response has the expected structure\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        return response.data.data.question;\r\n      } else if (response.data && response.data.data) {\r\n        return response.data.data;\r\n      } else if (response.data && response.data.success) {\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /table-questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the questions endpoint\r\n    try {\r\n      const response = await axios.get(`/questions/${questionId}`);\r\n\r\n      if (response.data && response.data.data) {\r\n        return response.data.data;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /questions/ endpoint:\", err);\r\n      // Continue to try the next endpoint\r\n    }\r\n\r\n    // If that fails, try the tables endpoint as a last resort\r\n    try {\r\n      const response = await axios.get(`/tables/${questionId}`);\r\n\r\n      if (response.data && response.data.data && response.data.data.question) {\r\n        return response.data.data.question;\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error from /tables/ endpoint:\", err);\r\n    }\r\n\r\n    // If all endpoints fail, throw an error\r\n    console.error(\"All endpoints failed to return valid data\");\r\n    throw new Error(\"Failed to fetch table structure from any endpoint\");\r\n  } catch (error) {\r\n    console.error(\"Error fetching table structure:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Save cell values\r\nexport const saveCellValues = async (\r\n  questionId: number,\r\n  cellValues: CellValue[]\r\n) => {\r\n  try {\r\n    const { data } = await axios.post(`/table-questions/cells`, {\r\n      questionId,\r\n      cellValues,\r\n    });\r\n    return data.data;\r\n  } catch (error) {\r\n    console.error(\"Error saving cell values:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Create a new table\r\n// IMPORTANT: When specifying parentColumnId for new columns, you need to use position-based indices\r\n// (1-based) that reference the position of the parent column in the array.\r\n// For example, if column B is a child of column A, and column A is the first column in the array,\r\n// then column B's parentColumnId should be 1.\r\n// This is different from updateTable, which uses actual database IDs.\r\nexport const createTable = async (\r\n  label: string,\r\n  projectId: number,\r\n  columns: { columnName: string; parentColumnId?: number }[],\r\n  rows?: { rowsName: string }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!projectId || isNaN(projectId)) {\r\n      throw new Error(\"Valid project ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => ({\r\n      columnName: col.columnName,\r\n      parentColumnId: col.parentColumnId,\r\n    }));\r\n\r\n    // Log the columns being sent to the backend\r\n\r\n    // Log the rearranged columns\r\n\r\n    // Use the table-questions endpoint which creates both a question and table structure\r\n    // Note: The axios instance is configured with baseURL that includes /api, so we don't need to add it here\r\n    const { data } = await axios.post(`/table-questions`, {\r\n      label,\r\n      projectId,\r\n      columns: cleanedColumns,\r\n      rows: rows || [],\r\n    });\r\n\r\n    if (!data || !data.success) {\r\n      throw new Error(data?.message || \"Failed to create table\");\r\n    }\r\n\r\n    return data.data;\r\n  } catch (error: any) {\r\n    console.error(\"Error creating table:\", error);\r\n\r\n    // Enhance error message with response details if available\r\n    if (error.response) {\r\n      console.error(\"Response status:\", error.response.status);\r\n      console.error(\"Response data:\", error.response.data);\r\n\r\n      // If we have a more specific error message from the server, use it\r\n      if (error.response.data && error.response.data.message) {\r\n        error.message = error.response.data.message;\r\n      }\r\n    }\r\n\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Delete a table\r\nexport const deleteTable = async (tableId: number) => {\r\n  try {\r\n    const { data } = await axios.delete(`/table-questions/${tableId}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting table:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Update an existing table\r\n// IMPORTANT: When specifying parentColumnId for existing columns, use the actual database ID of the parent column.\r\n// For new columns (without an ID), use the position (1-based index) of the parent column in the array.\r\n// For example:\r\n// - If column B is a child of existing column A with ID 123, then column B's parentColumnId should be 123.\r\n// - If column B is a child of new column A at position 1 in the array, then column B's parentColumnId should be 1.\r\nexport const updateTable = async (\r\n  tableId: number,\r\n  label: string,\r\n  columns: { id?: number; columnName: string; parentColumnId?: number }[],\r\n  rows?: { id?: number; rowsName: string }[]\r\n) => {\r\n  try {\r\n    // Validate inputs before sending to API\r\n    if (!label || !label.trim()) {\r\n      throw new Error(\"Table label is required\");\r\n    }\r\n\r\n    if (!tableId || isNaN(tableId)) {\r\n      throw new Error(\"Valid table ID is required\");\r\n    }\r\n\r\n    if (!columns || !Array.isArray(columns) || columns.length === 0) {\r\n      throw new Error(\"At least one column is required\");\r\n    }\r\n\r\n    // Rows are now optional - validate only if provided\r\n    if (rows && !Array.isArray(rows)) {\r\n      throw new Error(\"Rows must be an array if provided\");\r\n    }\r\n\r\n    // Ensure all columns have valid names\r\n    const invalidColumns = columns.filter(\r\n      (col) => !col.columnName || !col.columnName.trim()\r\n    );\r\n    if (invalidColumns.length > 0) {\r\n      throw new Error(\"All columns must have valid names\");\r\n    }\r\n\r\n    // Ensure all rows have valid names if rows are provided\r\n    if (rows) {\r\n      const invalidRows = rows.filter(\r\n        (row) => !row.rowsName || !row.rowsName.trim()\r\n      );\r\n      if (invalidRows.length > 0) {\r\n        throw new Error(\"All rows must have valid names\");\r\n      }\r\n    }\r\n\r\n    // Validate parent-child relationships\r\n    // Check for circular references or invalid parent IDs\r\n    const columnIdMap = new Map();\r\n    const columnPositionMap = new Map();\r\n\r\n    // Map columns by ID and position\r\n    columns.forEach((col, index) => {\r\n      if (col.id) {\r\n        columnIdMap.set(col.id, col);\r\n      }\r\n      // Store 1-based position\r\n      columnPositionMap.set(index + 1, col);\r\n    });\r\n\r\n    // Check each column with a parent\r\n    for (const col of columns) {\r\n      if (col.parentColumnId) {\r\n        // Ensure parentColumnId is a positive number\r\n        if (col.parentColumnId <= 0) {\r\n          throw new Error(\r\n            `Invalid parent column ID: ${col.parentColumnId}. Must be a positive number.`\r\n          );\r\n        }\r\n\r\n        // Try to find parent by ID first\r\n        let parentCol = columns.find((c) => c.id === col.parentColumnId);\r\n\r\n        // If not found by ID, try to find by position (for new columns)\r\n        if (!parentCol && col.parentColumnId <= columns.length) {\r\n          parentCol = columnPositionMap.get(col.parentColumnId);\r\n        }\r\n\r\n        // If we still can't find the parent, it's an error\r\n        if (!parentCol) {\r\n          throw new Error(\r\n            `Parent column with ID/position ${col.parentColumnId} not found in the columns array.`\r\n          );\r\n        }\r\n\r\n        // Check for circular references\r\n        // If this column has a parent, and that parent also has a parent,\r\n        // it would create a 3rd level, which we don't support\r\n        if (parentCol.parentColumnId) {\r\n          throw new Error(\r\n            \"Cannot create more than 2 levels of nested columns (parent → child → grandchild)\"\r\n          );\r\n        }\r\n      }\r\n    }\r\n\r\n    // The columns are already ordered correctly with parent-child relationships\r\n    // We just need to pass them through to the backend\r\n\r\n    // Create a clean version of the columns to send to the backend\r\n    const cleanedColumns: {\r\n      id?: number;\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = columns.map((col) => {\r\n      const cleanCol: {\r\n        id?: number;\r\n        columnName: string;\r\n        parentColumnId?: number;\r\n      } = {\r\n        columnName: col.columnName.trim(),\r\n      };\r\n\r\n      if (col.id) {\r\n        cleanCol.id = col.id;\r\n      }\r\n\r\n      if (col.parentColumnId !== undefined) {\r\n        cleanCol.parentColumnId = col.parentColumnId;\r\n      }\r\n\r\n      return cleanCol;\r\n    });\r\n\r\n    // Log the columns being sent to the backend\r\n\r\n    // Use the table-questions endpoint to update the table\r\n    try {\r\n      const { data } = await axios.patch(`/table-questions/${tableId}`, {\r\n        label: label.trim(),\r\n        columns: cleanedColumns,\r\n        rows: rows\r\n          ? rows.map((row) => ({\r\n              ...row,\r\n              rowsName: row.rowsName.trim(),\r\n            }))\r\n          : [],\r\n      });\r\n\r\n      if (!data || !data.success) {\r\n        throw new Error(data?.message || \"Failed to update table\");\r\n      }\r\n\r\n      return data.data;\r\n    } catch (apiError: any) {\r\n      console.error(\"API error updating table:\", apiError);\r\n\r\n      // Enhance error message with response details if available\r\n      if (apiError.response) {\r\n        console.error(\"Response status:\", apiError.response.status);\r\n        console.error(\"Response data:\", apiError.response.data);\r\n\r\n        // If we have a more specific error message from the server, use it\r\n        if (apiError.response.data && apiError.response.data.message) {\r\n          throw new Error(apiError.response.data.message);\r\n        }\r\n      }\r\n\r\n      // If we don't have a specific error message, throw the original error\r\n      throw apiError;\r\n    }\r\n  } catch (error: any) {\r\n    console.error(\"Error updating table:\", error);\r\n\r\n    // Rethrow the error with a clear message\r\n    if (error.message) {\r\n      throw new Error(`Failed to update table: ${error.message}`);\r\n    } else {\r\n      throw new Error(\"Failed to update table due to an unknown error\");\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AA8BO,MAAM,sBAAsB,OAAO;IACxC,IAAI;QACF,IAAI,CAAC,cAAc,MAAM,aAAa;YACpC,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,YAAY;YAEjE,mDAAmD;YACnD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC9C,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACjD,OAAO,SAAS,IAAI;YACtB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0CAA0C;QACxD,oCAAoC;QACtC;QAEA,4CAA4C;QAC5C,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY;YAE3D,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBACvC,OAAO,SAAS,IAAI,CAAC,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;QAClD,oCAAoC;QACtC;QAEA,0DAA0D;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY;YAExD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACtE,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,QAAQ;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QAEA,wCAAwC;QACxC,QAAQ,KAAK,CAAC;QACd,MAAM,IAAI,MAAM;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAC5B,YACA;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC,EAAE;YAC1D;YACA;QACF;QACA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,OACA,WACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,aAAa,MAAM,YAAY;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QAEnD,+DAA+D;QAC/D,MAAM,iBAGA,QAAQ,GAAG,CAAC,CAAC,MAAQ,CAAC;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,gBAAgB,IAAI,cAAc;YACpC,CAAC;QAED,4CAA4C;QAE5C,6BAA6B;QAE7B,qFAAqF;QACrF,0GAA0G;QAC1G,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;QAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;QACnC;QAEA,OAAO,KAAK,IAAI;IAClB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,2DAA2D;QAC3D,IAAI,MAAM,QAAQ,EAAE;YAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;YACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;YAEnD,mEAAmE;YACnE,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;gBACtD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C;QACF;QAEA,MAAM;IACR;AACF;AAGO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,SAAS;QACjE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAQO,MAAM,cAAc,OACzB,SACA,OACA,SACA;IAEA,IAAI;QACF,wCAAwC;QACxC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,MAAM,UAAU;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,KAAK,GAAG;YAC/D,MAAM,IAAI,MAAM;QAClB;QAEA,oDAAoD;QACpD,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;YAChC,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI;QAElD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,wDAAwD;QACxD,IAAI,MAAM;YACR,MAAM,cAAc,KAAK,MAAM,CAC7B,CAAC,MAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YAE9C,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,sCAAsC;QACtC,sDAAsD;QACtD,MAAM,cAAc,IAAI;QACxB,MAAM,oBAAoB,IAAI;QAE9B,iCAAiC;QACjC,QAAQ,OAAO,CAAC,CAAC,KAAK;YACpB,IAAI,IAAI,EAAE,EAAE;gBACV,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAC1B;YACA,yBAAyB;YACzB,kBAAkB,GAAG,CAAC,QAAQ,GAAG;QACnC;QAEA,kCAAkC;QAClC,KAAK,MAAM,OAAO,QAAS;YACzB,IAAI,IAAI,cAAc,EAAE;gBACtB,6CAA6C;gBAC7C,IAAI,IAAI,cAAc,IAAI,GAAG;oBAC3B,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,IAAI,cAAc,CAAC,4BAA4B,CAAC;gBAEjF;gBAEA,iCAAiC;gBACjC,IAAI,YAAY,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,cAAc;gBAE/D,gEAAgE;gBAChE,IAAI,CAAC,aAAa,IAAI,cAAc,IAAI,QAAQ,MAAM,EAAE;oBACtD,YAAY,kBAAkB,GAAG,CAAC,IAAI,cAAc;gBACtD;gBAEA,mDAAmD;gBACnD,IAAI,CAAC,WAAW;oBACd,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,IAAI,cAAc,CAAC,gCAAgC,CAAC;gBAE1F;gBAEA,gCAAgC;gBAChC,kEAAkE;gBAClE,sDAAsD;gBACtD,IAAI,UAAU,cAAc,EAAE;oBAC5B,MAAM,IAAI,MACR;gBAEJ;YACF;QACF;QAEA,4EAA4E;QAC5E,mDAAmD;QAEnD,+DAA+D;QAC/D,MAAM,iBAIA,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,WAIF;gBACF,YAAY,IAAI,UAAU,CAAC,IAAI;YACjC;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,SAAS,EAAE,GAAG,IAAI,EAAE;YACtB;YAEA,IAAI,IAAI,cAAc,KAAK,WAAW;gBACpC,SAAS,cAAc,GAAG,IAAI,cAAc;YAC9C;YAEA,OAAO;QACT;QAEA,4CAA4C;QAE5C,uDAAuD;QACvD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE;gBAChE,OAAO,MAAM,IAAI;gBACjB,SAAS;gBACT,MAAM,OACF,KAAK,GAAG,CAAC,CAAC,MAAQ,CAAC;wBACjB,GAAG,GAAG;wBACN,UAAU,IAAI,QAAQ,CAAC,IAAI;oBAC7B,CAAC,KACD,EAAE;YACR;YAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,OAAO,EAAE;gBAC1B,MAAM,IAAI,MAAM,MAAM,WAAW;YACnC;YAEA,OAAO,KAAK,IAAI;QAClB,EAAE,OAAO,UAAe;YACtB,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,2DAA2D;YAC3D,IAAI,SAAS,QAAQ,EAAE;gBACrB,QAAQ,KAAK,CAAC,oBAAoB,SAAS,QAAQ,CAAC,MAAM;gBAC1D,QAAQ,KAAK,CAAC,kBAAkB,SAAS,QAAQ,CAAC,IAAI;gBAEtD,mEAAmE;gBACnE,IAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC5D,MAAM,IAAI,MAAM,SAAS,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAChD;YACF;YAEA,sEAAsE;YACtE,MAAM;QACR;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,yCAAyC;QACzC,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;QAC5D,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-inputs/TableInput.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow as UITableRow,\r\n} from \"@/components/ui/table\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  fetchTableStructure,\r\n  TableColumn,\r\n  TableRow as TableRowType,\r\n  CellValue,\r\n} from \"../../lib/api/table\";\r\n\r\ninterface TableInputProps {\r\n  questionId: number;\r\n  value: string | CellValue[];\r\n  onChange: (value: CellValue[]) => void;\r\n  required?: boolean;\r\n  tableLabel?: string;\r\n}\r\n\r\nexport function TableInput({\r\n  questionId,\r\n  value,\r\n  onChange,\r\n  required = false,\r\n  tableLabel,\r\n}: TableInputProps) {\r\n  // All state hooks at the top of the component\r\n  const [columns, setColumns] = useState<TableColumn[]>([]);\r\n  const [rows, setRows] = useState<TableRowType[]>([]);\r\n  const [cellValues, setCellValues] = useState<Record<string, string>>({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [tableInfo, setTableInfo] = useState<{ label?: string }>({});\r\n\r\n  // Process columns to create a flat structure with parent-child relationships\r\n  const processColumns = (tableData: any) => {\r\n    if (!tableData || !tableData.tableColumns) return [];\r\n\r\n    const flattenedColumns: TableColumn[] = [];\r\n    const parentColumns = tableData.tableColumns.filter(\r\n      (col: TableColumn) =>\r\n        col.parentColumnId === null || col.parentColumnId === undefined\r\n    );\r\n\r\n    // Process each parent column and its children\r\n    parentColumns.forEach((parentCol: TableColumn) => {\r\n      // Add the parent column\r\n      flattenedColumns.push(parentCol);\r\n\r\n      // Add child columns if they exist\r\n      if (parentCol.childColumns && parentCol.childColumns.length > 0) {\r\n        parentCol.childColumns.forEach((childCol: any) => {\r\n          flattenedColumns.push({\r\n            id: childCol.id,\r\n            columnName: childCol.columnName,\r\n            parentColumnId: childCol.parentColumnId,\r\n          });\r\n        });\r\n      }\r\n    });\r\n\r\n    return flattenedColumns;\r\n  };\r\n\r\n  // IMPORTANT: All hooks must be called unconditionally and in the same order every render\r\n  // Group columns by parent-child relationships - always called, never conditional\r\n  const groupedColumns = React.useMemo(() => {\r\n    // Default empty values for when columns are not loaded yet\r\n    if (columns.length === 0) {\r\n      return {\r\n        parentColumns: [],\r\n        columnMap: new Map<number, TableColumn[]>(),\r\n        hasChildColumns: false,\r\n      };\r\n    }\r\n\r\n    // Get all parent columns (those without a parentColumnId)\r\n    const parentColumns = columns.filter(\r\n      (col) => col.parentColumnId === undefined || col.parentColumnId === null\r\n    );\r\n\r\n    // Create a map of parent columns to their child columns\r\n    const columnMap = new Map<number, TableColumn[]>();\r\n\r\n    parentColumns.forEach((parentCol) => {\r\n      // Find all child columns for this parent\r\n      const childColumns = columns.filter(\r\n        (col) => col.parentColumnId === parentCol.id\r\n      );\r\n      columnMap.set(parentCol.id, childColumns);\r\n    });\r\n\r\n    // Check if any parent has child columns\r\n    const hasChildColumns = parentColumns.some(\r\n      (p) => (columnMap.get(p.id) || []).length > 0\r\n    );\r\n\r\n    return { parentColumns, columnMap, hasChildColumns };\r\n  }, [columns]);\r\n\r\n  // Fetch table structure (columns and rows) on component mount or when questionId changes\r\n  useEffect(() => {\r\n    const loadTableStructure = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        const tableData = await fetchTableStructure(questionId);\r\n\r\n        if (tableData) {\r\n          // Check if tableColumns and tableRows exist\r\n          if (!tableData.tableColumns || !tableData.tableRows) {\r\n            console.error(\r\n              \"Missing tableColumns or tableRows in response:\",\r\n              tableData\r\n            );\r\n          }\r\n\r\n          // Process columns to handle parent-child relationships\r\n          const processedColumns = processColumns(tableData);\r\n          setColumns(processedColumns);\r\n          setRows(tableData.tableRows || []);\r\n\r\n          // Store the table label if available\r\n          if (tableData.label) {\r\n            setTableInfo({ label: tableData.label });\r\n          }\r\n\r\n         \r\n        } else {\r\n          console.error(\"No table data returned\");\r\n          setError(\"Failed to load table structure\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching table structure:\", err);\r\n        setError(\"Failed to load table structure\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadTableStructure();\r\n  }, [questionId]); // Only reload when questionId changes, not when value changes\r\n\r\n  // Handle value changes separately without reloading the table structure\r\n  useEffect(() => {\r\n    // Don't process if we're still loading the table structure\r\n    if (loading) return;\r\n\r\n    // Initialize cell values from existing data if available\r\n    const initialCellValues: Record<string, string> = {};\r\n\r\n    // If value is a string, try to parse it as JSON\r\n    let cellData: CellValue[] = [];\r\n    if (typeof value === \"string\") {\r\n      // Only attempt to parse if the string is not empty\r\n      if (value && value.trim() !== \"\") {\r\n        try {\r\n          cellData = JSON.parse(value);\r\n        } catch (e) {\r\n          console.error(\"Error parsing cell data:\", e);\r\n          cellData = [];\r\n        }\r\n      } else {\r\n        console.error(\"Empty string value, using empty array\");\r\n      }\r\n    } else if (Array.isArray(value)) {\r\n      cellData = value;\r\n    }\r\n\r\n    // Convert cell data to a map for easier access\r\n    cellData.forEach((cell) => {\r\n      initialCellValues[`${cell.columnId}_${cell.rowsId}`] = cell.value;\r\n    });\r\n\r\n    // Check if the value indicates a form reset (empty array, empty string, or undefined)\r\n    const isFormReset =\r\n      !value ||\r\n      (typeof value === \"string\" && value.trim() === \"\") ||\r\n      (Array.isArray(value) && value.length === 0);\r\n\r\n    if (isFormReset) {\r\n      // Clear all cell values when form is reset\r\n      setCellValues({});\r\n    } else if (Object.keys(initialCellValues).length > 0) {\r\n      // Only update cell values if we have new data and we're not in the middle of editing\r\n      setCellValues((prev) => {\r\n        // Merge with existing values to avoid losing user input\r\n        return { ...initialCellValues, ...prev };\r\n      });\r\n    }\r\n  }, [value, loading]);\r\n\r\n  // Handle cell value change\r\n  const handleCellChange = (\r\n    columnId: number,\r\n    rowId: number,\r\n    newValue: string\r\n  ) => {\r\n    const cellKey = `${columnId}_${rowId}`;\r\n\r\n    // Update the cell values state\r\n    setCellValues((prev) => ({\r\n      ...prev,\r\n      [cellKey]: newValue,\r\n    }));\r\n\r\n    // Use a setTimeout to ensure we're working with the latest state\r\n    // This prevents the race condition where the state update hasn't completed yet\r\n    setTimeout(() => {\r\n      // Get the current state of cellValues after the update\r\n      const currentCellValues = { ...cellValues, [cellKey]: newValue };\r\n\r\n      // Convert the updated cell values to the format expected by the onChange handler\r\n      const updatedCellValues: CellValue[] = [];\r\n\r\n      // Convert all cell values to the expected format\r\n      Object.entries(currentCellValues).forEach(([key, value]) => {\r\n        if (value.trim() !== \"\") {\r\n          const [colId, rowId] = key.split(\"_\").map(Number);\r\n          updatedCellValues.push({\r\n            columnId: colId,\r\n            rowsId: rowId,\r\n            value,\r\n          });\r\n        }\r\n      });\r\n\r\n      // Call the onChange handler with all cell values\r\n      onChange(updatedCellValues);\r\n    }, 0);\r\n  };\r\n\r\n  // Calculate this once, outside of any conditional rendering\r\n  // Only show error when there are no columns - having no rows is valid\r\n  const hasNoColumns = columns.length === 0;\r\n\r\n  // Render the hierarchical table\r\n  // Use a single return statement with conditional rendering inside\r\n  return (\r\n    <div className=\"overflow-x-auto\">\r\n      {loading ? (\r\n        <div className=\"py-4 text-center\">Loading table...</div>\r\n      ) : error ? (\r\n        <div className=\"py-4 text-center text-red-500\">{error}</div>\r\n      ) : hasNoColumns ? (\r\n        <div className=\"py-4 text-center text-amber-600\">\r\n          This table has no columns defined. Please configure the table question\r\n          first.\r\n        </div>\r\n      ) : (\r\n        <Table className=\"border-collapse\">\r\n          <TableHeader>\r\n            {/* First row: Parent column headers starting from leftmost position */}\r\n            <UITableRow>\r\n              {groupedColumns.parentColumns.map((parentCol) => {\r\n                const childColumns =\r\n                  groupedColumns.columnMap.get(parentCol.id) || [];\r\n                // If this parent has children, it spans multiple columns\r\n                const colSpan = childColumns.length || 1;\r\n\r\n                return (\r\n                  <TableHead\r\n                    key={parentCol.id}\r\n                    colSpan={colSpan}\r\n                    className=\"text-center border bg-blue-50 font-medium\"\r\n                  >\r\n                    {parentCol.columnName}\r\n                  </TableHead>\r\n                );\r\n              })}\r\n            </UITableRow>\r\n\r\n            {/* Second row: Child column headers (only if there are child columns) */}\r\n            {groupedColumns.hasChildColumns && (\r\n              <UITableRow>\r\n                {groupedColumns.parentColumns.map((parentCol) => {\r\n                  const childColumns =\r\n                    groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                  // If this parent has no children, render an empty cell to maintain alignment\r\n                  if (childColumns.length === 0) {\r\n                    return (\r\n                      <TableHead\r\n                        key={`empty-${parentCol.id}`}\r\n                        className=\"border bg-blue-50/50 text-sm\"\r\n                      >\r\n                        {/* Empty cell to maintain column alignment */}\r\n                      </TableHead>\r\n                    );\r\n                  }\r\n\r\n                  // Otherwise, render each child column\r\n                  return childColumns.map((childCol) => (\r\n                    <TableHead\r\n                      key={childCol.id}\r\n                      className=\"border bg-blue-50/50 text-sm\"\r\n                    >\r\n                      {childCol.columnName}\r\n                    </TableHead>\r\n                  ));\r\n                })}\r\n              </UITableRow>\r\n            )}\r\n          </TableHeader>\r\n\r\n          <TableBody>\r\n            {rows.length > 0 ? (\r\n              rows.map((row, rowIndex) => (\r\n                <UITableRow\r\n                  key={row.id}\r\n                  className={rowIndex % 2 === 0 ? \"bg-white\" : \"bg-gray-50\"}\r\n                >\r\n                  {/* Render cells for each parent column starting from leftmost position */}\r\n                  {groupedColumns.parentColumns.map((parentCol) => {\r\n                    const childColumns =\r\n                      groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                    // If this parent has no children, render a single cell\r\n                    if (childColumns.length === 0) {\r\n                      return (\r\n                        <TableCell\r\n                          key={`cell-${parentCol.id}-${row.id}`}\r\n                          className=\"border p-1\"\r\n                        >\r\n                          <Input\r\n                            value={\r\n                              cellValues[`${parentCol.id}_${row.id}`] || \"\"\r\n                            }\r\n                            onChange={(e) =>\r\n                              handleCellChange(\r\n                                parentCol.id,\r\n                                row.id,\r\n                                e.target.value\r\n                              )\r\n                            }\r\n                            className=\"w-full\"\r\n                            required={required}\r\n                            placeholder=\"Enter value\"\r\n                          />\r\n                        </TableCell>\r\n                      );\r\n                    }\r\n\r\n                    // Otherwise, render cells for each child column\r\n                    return childColumns.map((childCol) => (\r\n                      <TableCell\r\n                        key={`cell-${childCol.id}-${row.id}`}\r\n                        className=\"border p-1\"\r\n                      >\r\n                        <Input\r\n                          value={cellValues[`${childCol.id}_${row.id}`] || \"\"}\r\n                          onChange={(e) =>\r\n                            handleCellChange(\r\n                              childCol.id,\r\n                              row.id,\r\n                              e.target.value\r\n                            )\r\n                          }\r\n                          className=\"w-full\"\r\n                          required={required}\r\n                          placeholder=\"Enter value\"\r\n                        />\r\n                      </TableCell>\r\n                    ));\r\n                  })}\r\n                </UITableRow>\r\n              ))\r\n            ) : (\r\n              // When no rows exist, show a single row with input fields under columns\r\n              <UITableRow>\r\n                {/* Render input cells for each parent column starting from leftmost position */}\r\n                {groupedColumns.parentColumns.map((parentCol) => {\r\n                  const childColumns =\r\n                    groupedColumns.columnMap.get(parentCol.id) || [];\r\n\r\n                  // If this parent has no children, render a single cell\r\n                  if (childColumns.length === 0) {\r\n                    return (\r\n                      <TableCell\r\n                        key={`cell-${parentCol.id}-no-row`}\r\n                        className=\"border p-1\"\r\n                      >\r\n                        <Input\r\n                          value={cellValues[`${parentCol.id}_no_row`] || \"\"}\r\n                          onChange={(e) =>\r\n                            handleCellChange(\r\n                              parentCol.id,\r\n                              \"no_row\" as any,\r\n                              e.target.value\r\n                            )\r\n                          }\r\n                          className=\"w-full\"\r\n                          required={required}\r\n                          placeholder=\"Enter value\"\r\n                        />\r\n                      </TableCell>\r\n                    );\r\n                  }\r\n\r\n                  // Otherwise, render cells for each child column\r\n                  return childColumns.map((childCol) => (\r\n                    <TableCell\r\n                      key={`cell-${childCol.id}-no-row`}\r\n                      className=\"border p-1\"\r\n                    >\r\n                      <Input\r\n                        value={cellValues[`${childCol.id}_no_row`] || \"\"}\r\n                        onChange={(e) =>\r\n                          handleCellChange(\r\n                            childCol.id,\r\n                            \"no_row\" as any,\r\n                            e.target.value\r\n                          )\r\n                        }\r\n                        className=\"w-full\"\r\n                        required={required}\r\n                        placeholder=\"Enter value\"\r\n                      />\r\n                    </TableCell>\r\n                  ));\r\n                })}\r\n              </UITableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;;;AAZA;;;;;AA2BO,SAAS,WAAW,EACzB,UAAU,EACV,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,EACM;;IAChB,8CAA8C;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,CAAC;IAEhE,6EAA6E;IAC7E,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,aAAa,CAAC,UAAU,YAAY,EAAE,OAAO,EAAE;QAEpD,MAAM,mBAAkC,EAAE;QAC1C,MAAM,gBAAgB,UAAU,YAAY,CAAC,MAAM,CACjD,CAAC,MACC,IAAI,cAAc,KAAK,QAAQ,IAAI,cAAc,KAAK;QAG1D,8CAA8C;QAC9C,cAAc,OAAO,CAAC,CAAC;YACrB,wBAAwB;YACxB,iBAAiB,IAAI,CAAC;YAEtB,kCAAkC;YAClC,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC/D,UAAU,YAAY,CAAC,OAAO,CAAC,CAAC;oBAC9B,iBAAiB,IAAI,CAAC;wBACpB,IAAI,SAAS,EAAE;wBACf,YAAY,SAAS,UAAU;wBAC/B,gBAAgB,SAAS,cAAc;oBACzC;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,yFAAyF;IACzF,iFAAiF;IACjF,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,OAAO;8CAAC;YACnC,2DAA2D;YAC3D,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,OAAO;oBACL,eAAe,EAAE;oBACjB,WAAW,IAAI;oBACf,iBAAiB;gBACnB;YACF;YAEA,0DAA0D;YAC1D,MAAM,gBAAgB,QAAQ,MAAM;oEAClC,CAAC,MAAQ,IAAI,cAAc,KAAK,aAAa,IAAI,cAAc,KAAK;;YAGtE,wDAAwD;YACxD,MAAM,YAAY,IAAI;YAEtB,cAAc,OAAO;sDAAC,CAAC;oBACrB,yCAAyC;oBACzC,MAAM,eAAe,QAAQ,MAAM;2EACjC,CAAC,MAAQ,IAAI,cAAc,KAAK,UAAU,EAAE;;oBAE9C,UAAU,GAAG,CAAC,UAAU,EAAE,EAAE;gBAC9B;;YAEA,wCAAwC;YACxC,MAAM,kBAAkB,cAAc,IAAI;sEACxC,CAAC,IAAM,CAAC,UAAU,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG;;YAG9C,OAAO;gBAAE;gBAAe;gBAAW;YAAgB;QACrD;6CAAG;QAAC;KAAQ;IAEZ,yFAAyF;IACzF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;2DAAqB;oBACzB,IAAI;wBACF,WAAW;wBAEX,MAAM,YAAY,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;wBAE5C,IAAI,WAAW;4BACb,4CAA4C;4BAC5C,IAAI,CAAC,UAAU,YAAY,IAAI,CAAC,UAAU,SAAS,EAAE;gCACnD,QAAQ,KAAK,CACX,kDACA;4BAEJ;4BAEA,uDAAuD;4BACvD,MAAM,mBAAmB,eAAe;4BACxC,WAAW;4BACX,QAAQ,UAAU,SAAS,IAAI,EAAE;4BAEjC,qCAAqC;4BACrC,IAAI,UAAU,KAAK,EAAE;gCACnB,aAAa;oCAAE,OAAO,UAAU,KAAK;gCAAC;4BACxC;wBAGF,OAAO;4BACL,QAAQ,KAAK,CAAC;4BACd,SAAS;wBACX;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;+BAAG;QAAC;KAAW,GAAG,8DAA8D;IAEhF,wEAAwE;IACxE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,2DAA2D;YAC3D,IAAI,SAAS;YAEb,yDAAyD;YACzD,MAAM,oBAA4C,CAAC;YAEnD,gDAAgD;YAChD,IAAI,WAAwB,EAAE;YAC9B,IAAI,OAAO,UAAU,UAAU;gBAC7B,mDAAmD;gBACnD,IAAI,SAAS,MAAM,IAAI,OAAO,IAAI;oBAChC,IAAI;wBACF,WAAW,KAAK,KAAK,CAAC;oBACxB,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,WAAW,EAAE;oBACf;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC;gBAChB;YACF,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;gBAC/B,WAAW;YACb;YAEA,+CAA+C;YAC/C,SAAS,OAAO;wCAAC,CAAC;oBAChB,iBAAiB,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,CAAC,GAAG,KAAK,KAAK;gBACnE;;YAEA,sFAAsF;YACtF,MAAM,cACJ,CAAC,SACA,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,MAC9C,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK;YAE5C,IAAI,aAAa;gBACf,2CAA2C;gBAC3C,cAAc,CAAC;YACjB,OAAO,IAAI,OAAO,IAAI,CAAC,mBAAmB,MAAM,GAAG,GAAG;gBACpD,qFAAqF;gBACrF;4CAAc,CAAC;wBACb,wDAAwD;wBACxD,OAAO;4BAAE,GAAG,iBAAiB;4BAAE,GAAG,IAAI;wBAAC;oBACzC;;YACF;QACF;+BAAG;QAAC;QAAO;KAAQ;IAEnB,2BAA2B;IAC3B,MAAM,mBAAmB,CACvB,UACA,OACA;QAEA,MAAM,UAAU,GAAG,SAAS,CAAC,EAAE,OAAO;QAEtC,+BAA+B;QAC/B,cAAc,CAAC,OAAS,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;QAED,iEAAiE;QACjE,+EAA+E;QAC/E,WAAW;YACT,uDAAuD;YACvD,MAAM,oBAAoB;gBAAE,GAAG,UAAU;gBAAE,CAAC,QAAQ,EAAE;YAAS;YAE/D,iFAAiF;YACjF,MAAM,oBAAiC,EAAE;YAEzC,iDAAiD;YACjD,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBACrD,IAAI,MAAM,IAAI,OAAO,IAAI;oBACvB,MAAM,CAAC,OAAO,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;oBAC1C,kBAAkB,IAAI,CAAC;wBACrB,UAAU;wBACV,QAAQ;wBACR;oBACF;gBACF;YACF;YAEA,iDAAiD;YACjD,SAAS;QACX,GAAG;IACL;IAEA,4DAA4D;IAC5D,sEAAsE;IACtE,MAAM,eAAe,QAAQ,MAAM,KAAK;IAExC,gCAAgC;IAChC,kEAAkE;IAClE,qBACE,6LAAC;QAAI,WAAU;kBACZ,wBACC,6LAAC;YAAI,WAAU;sBAAmB;;;;;mBAChC,sBACF,6LAAC;YAAI,WAAU;sBAAiC;;;;;mBAC9C,6BACF,6LAAC;YAAI,WAAU;sBAAkC;;;;;iCAKjD,6LAAC,6HAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,6LAAC,6HAAA,CAAA,cAAW;;sCAEV,6LAAC,6HAAA,CAAA,WAAU;sCACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAClD,yDAAyD;gCACzD,MAAM,UAAU,aAAa,MAAM,IAAI;gCAEvC,qBACE,6LAAC,6HAAA,CAAA,YAAS;oCAER,SAAS;oCACT,WAAU;8CAET,UAAU,UAAU;mCAJhB,UAAU,EAAE;;;;;4BAOvB;;;;;;wBAID,eAAe,eAAe,kBAC7B,6LAAC,6HAAA,CAAA,WAAU;sCACR,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAElD,6EAA6E;gCAC7E,IAAI,aAAa,MAAM,KAAK,GAAG;oCAC7B,qBACE,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;uCADL,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;;;;;gCAMlC;gCAEA,sCAAsC;gCACtC,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAET,SAAS,UAAU;uCAHf,SAAS,EAAE;;;;;4BAMtB;;;;;;;;;;;;8BAKN,6LAAC,6HAAA,CAAA,YAAS;8BACP,KAAK,MAAM,GAAG,IACb,KAAK,GAAG,CAAC,CAAC,KAAK,yBACb,6LAAC,6HAAA,CAAA,WAAU;4BAET,WAAW,WAAW,MAAM,IAAI,aAAa;sCAG5C,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;gCACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;gCAElD,uDAAuD;gCACvD,IAAI,aAAa,MAAM,KAAK,GAAG;oCAC7B,qBACE,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CACJ,OACE,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;4CAE7C,UAAU,CAAC,IACT,iBACE,UAAU,EAAE,EACZ,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;4CAGlB,WAAU;4CACV,UAAU;4CACV,aAAY;;;;;;uCAhBT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;gCAoB3C;gCAEA,gDAAgD;gCAChD,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;wCAER,WAAU;kDAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CACJ,OAAO,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI;4CACjD,UAAU,CAAC,IACT,iBACE,SAAS,EAAE,EACX,IAAI,EAAE,EACN,EAAE,MAAM,CAAC,KAAK;4CAGlB,WAAU;4CACV,UAAU;4CACV,aAAY;;;;;;uCAdT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;;;;;4BAkB1C;2BAvDK,IAAI,EAAE;;;;oCA2Df,wEAAwE;kCACxE,6LAAC,6HAAA,CAAA,WAAU;kCAER,eAAe,aAAa,CAAC,GAAG,CAAC,CAAC;4BACjC,MAAM,eACJ,eAAe,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE;4BAElD,uDAAuD;4BACvD,IAAI,aAAa,MAAM,KAAK,GAAG;gCAC7B,qBACE,6LAAC,6HAAA,CAAA,YAAS;oCAER,WAAU;8CAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;wCACJ,OAAO,UAAU,CAAC,GAAG,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCAC/C,UAAU,CAAC,IACT,iBACE,UAAU,EAAE,EACZ,UACA,EAAE,MAAM,CAAC,KAAK;wCAGlB,WAAU;wCACV,UAAU;wCACV,aAAY;;;;;;mCAdT,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC;;;;;4BAkBxC;4BAEA,gDAAgD;4BAChD,OAAO,aAAa,GAAG,CAAC,CAAC,yBACvB,6LAAC,6HAAA,CAAA,YAAS;oCAER,WAAU;8CAEV,cAAA,6LAAC,6HAAA,CAAA,QAAK;wCACJ,OAAO,UAAU,CAAC,GAAG,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI;wCAC9C,UAAU,CAAC,IACT,iBACE,SAAS,EAAE,EACX,UACA,EAAE,MAAM,CAAC,KAAK;wCAGlB,WAAU;wCACV,UAAU;wCACV,aAAY;;;;;;mCAdT,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC;;;;;wBAkBvC;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAzZgB;KAAA", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/conditionalQuestions.ts"], "sourcesContent": ["import { Question } from \"@/types/formBuilder\";\r\n\r\n/**\r\n * Utility functions for handling conditional questions logic\r\n */\r\n\r\n/**\r\n * Get the next question ID based on the selected option\r\n */\r\nexport const getNextQuestionId = (\r\n  question: Question,\r\n  selectedValue: string | string[]\r\n): number | null => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  // For selectone, selectedValue is a string\r\n  if (question.inputType === \"selectone\" && typeof selectedValue === \"string\") {\r\n    const selectedOption = question.questionOptions.find(\r\n      (option) => option.label === selectedValue\r\n    );\r\n    return selectedOption?.nextQuestionId || null;\r\n  }\r\n\r\n  // For selectmany, selectedValue is an array - return the first next question found\r\n  if (question.inputType === \"selectmany\" && Array.isArray(selectedValue)) {\r\n    for (const value of selectedValue) {\r\n      const selectedOption = question.questionOptions.find(\r\n        (option) => option.label === value\r\n      );\r\n      if (selectedOption?.nextQuestionId) {\r\n        return selectedOption.nextQuestionId;\r\n      }\r\n    }\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n/**\r\n * Get all possible next question IDs for a question (for dependency tracking)\r\n */\r\nexport const getAllNextQuestionIds = (question: Question): number[] => {\r\n  if (!question.questionOptions || question.questionOptions.length === 0) {\r\n    return [];\r\n  }\r\n\r\n  return question.questionOptions\r\n    .map((option) => option.nextQuestionId)\r\n    .filter((id): id is number => id !== null && id !== undefined);\r\n};\r\n\r\n/**\r\n * Determine which questions should be visible based on current answers\r\n */\r\nexport const getVisibleQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Question[] => {\r\n  const visibleQuestionIds = new Set<number>();\r\n  const conditionalQuestionIds = new Set<number>();\r\n\r\n  // First, collect all questions that are conditional (have a parent question)\r\n  allQuestions.forEach((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    nextQuestionIds.forEach((id) => conditionalQuestionIds.add(id));\r\n  });\r\n\r\n  // Start with all non-conditional questions (questions that are not triggered by other questions)\r\n  allQuestions.forEach((question) => {\r\n    if (!conditionalQuestionIds.has(question.id)) {\r\n      visibleQuestionIds.add(question.id);\r\n    }\r\n  });\r\n\r\n  // Process answers to determine which conditional questions should be visible\r\n  Object.entries(answers).forEach(([questionIdStr, answer]) => {\r\n    const questionId = parseInt(questionIdStr);\r\n    const question = allQuestions.find((q) => q.id === questionId);\r\n\r\n    if (question && answer) {\r\n      const nextQuestionId = getNextQuestionId(question, answer);\r\n      if (nextQuestionId) {\r\n        visibleQuestionIds.add(nextQuestionId);\r\n      }\r\n    }\r\n  });\r\n\r\n  // Return questions in their original order, filtered by visibility\r\n  return allQuestions.filter((question) => visibleQuestionIds.has(question.id));\r\n};\r\n\r\n/**\r\n * Check if a question should be visible based on current answers\r\n */\r\nexport const isQuestionVisible = (\r\n  question: Question,\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): boolean => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  return visibleQuestions.some((q) => q.id === question.id);\r\n};\r\n\r\n/**\r\n * Get questions that depend on a specific question\r\n */\r\nexport const getDependentQuestions = (\r\n  parentQuestionId: number,\r\n  allQuestions: Question[]\r\n): Question[] => {\r\n  const parentQuestion = allQuestions.find((q) => q.id === parentQuestionId);\r\n  if (!parentQuestion) return [];\r\n\r\n  const nextQuestionIds = getAllNextQuestionIds(parentQuestion);\r\n  return allQuestions.filter((q) => nextQuestionIds.includes(q.id));\r\n};\r\n\r\n/**\r\n * Check if a question is a follow-up question (has a parent question)\r\n */\r\nexport const isFollowUpQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): boolean => {\r\n  return allQuestions.some((question) => {\r\n    const nextQuestionIds = getAllNextQuestionIds(question);\r\n    return nextQuestionIds.includes(questionId);\r\n  });\r\n};\r\n\r\n/**\r\n * Get the parent question for a follow-up question\r\n */\r\nexport const getParentQuestion = (\r\n  questionId: number,\r\n  allQuestions: Question[]\r\n): Question | null => {\r\n  return (\r\n    allQuestions.find((question) => {\r\n      const nextQuestionIds = getAllNextQuestionIds(question);\r\n      return nextQuestionIds.includes(questionId);\r\n    }) || null\r\n  );\r\n};\r\n\r\n/**\r\n * Get questions in nested structure for rendering\r\n * Returns questions grouped by parent-child relationships, maintaining order\r\n */\r\nexport const getNestedQuestions = (\r\n  allQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Array<{\r\n  question: Question;\r\n  isVisible: boolean;\r\n  isFollowUp: boolean;\r\n  parentQuestion?: Question;\r\n  followUps: Array<{\r\n    question: Question;\r\n    isVisible: boolean;\r\n  }>;\r\n}> => {\r\n  const visibleQuestions = getVisibleQuestions(allQuestions, answers);\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n\r\n  // Get all parent questions (questions that are not follow-ups themselves)\r\n  const parentQuestions = allQuestions.filter(\r\n    (question) => !isFollowUpQuestion(question.id, allQuestions)\r\n  );\r\n\r\n  // Sort parent questions by position to maintain order\r\n  const sortedParentQuestions = parentQuestions.sort(\r\n    (a, b) => a.position - b.position\r\n  );\r\n\r\n  return sortedParentQuestions\r\n    .map((parentQuestion) => {\r\n      const followUpQuestions = getDependentQuestions(\r\n        parentQuestion.id,\r\n        allQuestions\r\n      );\r\n      const sortedFollowUps = followUpQuestions.sort(\r\n        (a, b) => a.position - b.position\r\n      );\r\n\r\n      return {\r\n        question: parentQuestion,\r\n        isVisible: visibleQuestionIds.has(parentQuestion.id),\r\n        isFollowUp: false,\r\n        followUps: sortedFollowUps.map((followUp) => ({\r\n          question: followUp,\r\n          isVisible: visibleQuestionIds.has(followUp.id),\r\n        })),\r\n      };\r\n    })\r\n    .filter(\r\n      (group) => group.isVisible || group.followUps.some((f) => f.isVisible)\r\n    );\r\n};\r\n\r\n/**\r\n * Clean up answers for questions that are no longer visible\r\n */\r\nexport const cleanupHiddenAnswers = (\r\n  answers: Record<string, any>,\r\n  visibleQuestions: Question[]\r\n): Record<string, any> => {\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n  const cleanedAnswers: Record<string, any> = {};\r\n\r\n  Object.entries(answers).forEach(([questionId, answer]) => {\r\n    if (visibleQuestionIds.has(parseInt(questionId))) {\r\n      cleanedAnswers[questionId] = answer;\r\n    }\r\n  });\r\n\r\n  return cleanedAnswers;\r\n};\r\n\r\n/**\r\n * Clean up answers for questions that are no longer visible while preserving original data\r\n * This version maintains original submitted values for conditional questions\r\n */\r\nexport const cleanupHiddenAnswersWithPersistence = (\r\n  currentAnswers: Record<string, any>,\r\n  visibleQuestions: Question[],\r\n  originalAnswers: Record<string, any>\r\n): Record<string, any> => {\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n  const cleanedAnswers: Record<string, any> = {};\r\n\r\n  Object.entries(currentAnswers).forEach(([questionId, answer]) => {\r\n    if (visibleQuestionIds.has(parseInt(questionId))) {\r\n      cleanedAnswers[questionId] = answer;\r\n    }\r\n  });\r\n\r\n  return cleanedAnswers;\r\n};\r\n\r\n/**\r\n * Restore original answers for questions that have become visible again\r\n * This helps maintain data persistence when toggling conditional questions\r\n */\r\nexport const restoreOriginalAnswers = (\r\n  currentAnswers: Record<string, any>,\r\n  visibleQuestions: Question[],\r\n  originalAnswers: Record<string, any>\r\n): Record<string, any> => {\r\n  const restoredAnswers = { ...currentAnswers };\r\n  const visibleQuestionIds = new Set(visibleQuestions.map((q) => q.id));\r\n\r\n  // For each visible question, if it doesn't have a current answer but has an original answer, restore it\r\n  visibleQuestionIds.forEach((questionId) => {\r\n    const questionIdStr = questionId.toString();\r\n    const hasCurrentAnswer = currentAnswers[questionIdStr] !== undefined &&\r\n                            currentAnswers[questionIdStr] !== \"\" &&\r\n                            !(Array.isArray(currentAnswers[questionIdStr]) && currentAnswers[questionIdStr].length === 0);\r\n    const hasOriginalAnswer = originalAnswers[questionIdStr] !== undefined &&\r\n                             originalAnswers[questionIdStr] !== \"\" &&\r\n                             !(Array.isArray(originalAnswers[questionIdStr]) && originalAnswers[questionIdStr].length === 0);\r\n\r\n    // If the question is visible but has no current answer, restore the original answer\r\n    if (!hasCurrentAnswer && hasOriginalAnswer) {\r\n      restoredAnswers[questionIdStr] = originalAnswers[questionIdStr];\r\n    }\r\n  });\r\n\r\n  return restoredAnswers;\r\n};\r\n\r\n/**\r\n * Validate that all visible required questions have answers\r\n */\r\nexport const validateVisibleQuestions = (\r\n  visibleQuestions: Question[],\r\n  answers: Record<string, any>\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  visibleQuestions.forEach((question) => {\r\n    if (question.isRequired) {\r\n      const value = answers[question.id];\r\n      if (\r\n        (typeof value === \"string\" && !value.trim()) ||\r\n        (Array.isArray(value) && value.length === 0) ||\r\n        value === undefined ||\r\n        value === null\r\n      ) {\r\n        errors[question.id] = `${question.label} is required`;\r\n      }\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AASO,MAAM,oBAAoB,CAC/B,UACA;IAEA,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO;IACT;IAEA,2CAA2C;IAC3C,IAAI,SAAS,SAAS,KAAK,eAAe,OAAO,kBAAkB,UAAU;QAC3E,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;QAE/B,OAAO,gBAAgB,kBAAkB;IAC3C;IAEA,mFAAmF;IACnF,IAAI,SAAS,SAAS,KAAK,gBAAgB,MAAM,OAAO,CAAC,gBAAgB;QACvE,KAAK,MAAM,SAAS,cAAe;YACjC,MAAM,iBAAiB,SAAS,eAAe,CAAC,IAAI,CAClD,CAAC,SAAW,OAAO,KAAK,KAAK;YAE/B,IAAI,gBAAgB,gBAAgB;gBAClC,OAAO,eAAe,cAAc;YACtC;QACF;IACF;IAEA,OAAO;AACT;AAKO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,KAAK,GAAG;QACtE,OAAO,EAAE;IACX;IAEA,OAAO,SAAS,eAAe,CAC5B,GAAG,CAAC,CAAC,SAAW,OAAO,cAAc,EACrC,MAAM,CAAC,CAAC,KAAqB,OAAO,QAAQ,OAAO;AACxD;AAKO,MAAM,sBAAsB,CACjC,cACA;IAEA,MAAM,qBAAqB,IAAI;IAC/B,MAAM,yBAAyB,IAAI;IAEnC,6EAA6E;IAC7E,aAAa,OAAO,CAAC,CAAC;QACpB,MAAM,kBAAkB,sBAAsB;QAC9C,gBAAgB,OAAO,CAAC,CAAC,KAAO,uBAAuB,GAAG,CAAC;IAC7D;IAEA,iGAAiG;IACjG,aAAa,OAAO,CAAC,CAAC;QACpB,IAAI,CAAC,uBAAuB,GAAG,CAAC,SAAS,EAAE,GAAG;YAC5C,mBAAmB,GAAG,CAAC,SAAS,EAAE;QACpC;IACF;IAEA,6EAA6E;IAC7E,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,eAAe,OAAO;QACtD,MAAM,aAAa,SAAS;QAC5B,MAAM,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QAEnD,IAAI,YAAY,QAAQ;YACtB,MAAM,iBAAiB,kBAAkB,UAAU;YACnD,IAAI,gBAAgB;gBAClB,mBAAmB,GAAG,CAAC;YACzB;QACF;IACF;IAEA,mEAAmE;IACnE,OAAO,aAAa,MAAM,CAAC,CAAC,WAAa,mBAAmB,GAAG,CAAC,SAAS,EAAE;AAC7E;AAKO,MAAM,oBAAoB,CAC/B,UACA,cACA;IAEA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,OAAO,iBAAiB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;AAC1D;AAKO,MAAM,wBAAwB,CACnC,kBACA;IAEA,MAAM,iBAAiB,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACzD,IAAI,CAAC,gBAAgB,OAAO,EAAE;IAE9B,MAAM,kBAAkB,sBAAsB;IAC9C,OAAO,aAAa,MAAM,CAAC,CAAC,IAAM,gBAAgB,QAAQ,CAAC,EAAE,EAAE;AACjE;AAKO,MAAM,qBAAqB,CAChC,YACA;IAEA,OAAO,aAAa,IAAI,CAAC,CAAC;QACxB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC;AACF;AAKO,MAAM,oBAAoB,CAC/B,YACA;IAEA,OACE,aAAa,IAAI,CAAC,CAAC;QACjB,MAAM,kBAAkB,sBAAsB;QAC9C,OAAO,gBAAgB,QAAQ,CAAC;IAClC,MAAM;AAEV;AAMO,MAAM,qBAAqB,CAChC,cACA;IAWA,MAAM,mBAAmB,oBAAoB,cAAc;IAC3D,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IAEnE,0EAA0E;IAC1E,MAAM,kBAAkB,aAAa,MAAM,CACzC,CAAC,WAAa,CAAC,mBAAmB,SAAS,EAAE,EAAE;IAGjD,sDAAsD;IACtD,MAAM,wBAAwB,gBAAgB,IAAI,CAChD,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAGnC,OAAO,sBACJ,GAAG,CAAC,CAAC;QACJ,MAAM,oBAAoB,sBACxB,eAAe,EAAE,EACjB;QAEF,MAAM,kBAAkB,kBAAkB,IAAI,CAC5C,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAGnC,OAAO;YACL,UAAU;YACV,WAAW,mBAAmB,GAAG,CAAC,eAAe,EAAE;YACnD,YAAY;YACZ,WAAW,gBAAgB,GAAG,CAAC,CAAC,WAAa,CAAC;oBAC5C,UAAU;oBACV,WAAW,mBAAmB,GAAG,CAAC,SAAS,EAAE;gBAC/C,CAAC;QACH;IACF,GACC,MAAM,CACL,CAAC,QAAU,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,SAAS;AAE3E;AAKO,MAAM,uBAAuB,CAClC,SACA;IAEA,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IACnE,MAAM,iBAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;QACnD,IAAI,mBAAmB,GAAG,CAAC,SAAS,cAAc;YAChD,cAAc,CAAC,WAAW,GAAG;QAC/B;IACF;IAEA,OAAO;AACT;AAMO,MAAM,sCAAsC,CACjD,gBACA,kBACA;IAEA,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IACnE,MAAM,iBAAsC,CAAC;IAE7C,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO;QAC1D,IAAI,mBAAmB,GAAG,CAAC,SAAS,cAAc;YAChD,cAAc,CAAC,WAAW,GAAG;QAC/B;IACF;IAEA,OAAO;AACT;AAMO,MAAM,yBAAyB,CACpC,gBACA,kBACA;IAEA,MAAM,kBAAkB;QAAE,GAAG,cAAc;IAAC;IAC5C,MAAM,qBAAqB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;IAEnE,wGAAwG;IACxG,mBAAmB,OAAO,CAAC,CAAC;QAC1B,MAAM,gBAAgB,WAAW,QAAQ;QACzC,MAAM,mBAAmB,cAAc,CAAC,cAAc,KAAK,aACnC,cAAc,CAAC,cAAc,KAAK,MAClC,CAAC,CAAC,MAAM,OAAO,CAAC,cAAc,CAAC,cAAc,KAAK,cAAc,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;QACpH,MAAM,oBAAoB,eAAe,CAAC,cAAc,KAAK,aACpC,eAAe,CAAC,cAAc,KAAK,MACnC,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,CAAC,cAAc,KAAK,eAAe,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;QAEvH,oFAAoF;QACpF,IAAI,CAAC,oBAAoB,mBAAmB;YAC1C,eAAe,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc;QACjE;IACF;IAEA,OAAO;AACT;AAKO,MAAM,2BAA2B,CACtC,kBACA;IAEA,MAAM,SAAiC,CAAC;IAExC,iBAAiB,OAAO,CAAC,CAAC;QACxB,IAAI,SAAS,UAAU,EAAE;YACvB,MAAM,QAAQ,OAAO,CAAC,SAAS,EAAE,CAAC;YAClC,IACE,AAAC,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,MACxC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,KAC1C,UAAU,aACV,UAAU,MACV;gBACA,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,GAAG,SAAS,KAAK,CAAC,YAAY,CAAC;YACvD;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-inputs/NestedQuestionRenderer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { Label } from \"@/components/ui/label\";\r\n\r\ninterface NestedQuestionRendererProps {\r\n  questionGroup: {\r\n    question: Question;\r\n    isVisible: boolean;\r\n    isFollowUp: boolean;\r\n    followUps: Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n    }>;\r\n  };\r\n  renderQuestionInput: (question: Question) => React.ReactNode;\r\n  errors: Record<number, string>;\r\n  className?: string;\r\n}\r\n\r\nconst NestedQuestionRenderer: React.FC<NestedQuestionRendererProps> = ({\r\n  questionGroup,\r\n  renderQuestionInput,\r\n  errors,\r\n  className = \"\",\r\n}) => {\r\n  const { question: parentQuestion, isVisible: isParentVisible, followUps } = questionGroup;\r\n  \r\n  // Don't render anything if neither parent nor any follow-ups are visible\r\n  if (!isParentVisible && !followUps.some(f => f.isVisible)) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className={`${className}`}>\r\n      {/* Parent Question */}\r\n      {isParentVisible && (\r\n        <div className=\"border border-neutral-500 dark:border-neutral-700 rounded-md p-4 bg-neutral-100 dark:bg-neutral-800\">\r\n          <div className=\"mb-2\">\r\n            <Label className=\"text-base font-medium\">\r\n              {parentQuestion.label}\r\n              {parentQuestion.isRequired && (\r\n                <span className=\"text-red-500 ml-1\">*</span>\r\n              )}\r\n            </Label>\r\n            {parentQuestion.hint && (\r\n              <p className=\"text-sm text-muted-foreground mt-1\">\r\n                {parentQuestion.hint}\r\n              </p>\r\n            )}\r\n            {errors[parentQuestion.id] && (\r\n              <p className=\"text-sm text-red-500 mt-1\">\r\n                {errors[parentQuestion.id]}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div className=\"mt-2\">{renderQuestionInput(parentQuestion)}</div>\r\n          \r\n          {/* Follow-up Questions */}\r\n          {followUps.some(f => f.isVisible) && (\r\n            <div className=\"mt-4 ml-4 space-y-3 border-l-2 border-primary-200 dark:border-primary-700 pl-4\">\r\n              {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n                isFollowUpVisible && (\r\n                  <div\r\n                    key={followUpQuestion.id}\r\n                    className=\"border border-neutral-100 dark:border-neutral-600 rounded-md p-3 bg-primary-50 dark:bg-primary-900/20\"\r\n                  >\r\n                    <div className=\"mb-2\">\r\n                      <Label className=\"text-sm font-medium text-primary-900 dark:text-primary-100\">\r\n                        {followUpQuestion.label}\r\n                        {followUpQuestion.isRequired && (\r\n                          <span className=\"text-red-500 ml-1\">*</span>\r\n                        )}\r\n                      </Label>\r\n                      {followUpQuestion.hint && (\r\n                        <p className=\"text-xs text-primary-700 dark:text-primary-300 mt-1\">\r\n                          {followUpQuestion.hint}\r\n                        </p>\r\n                      )}\r\n                      {errors[followUpQuestion.id] && (\r\n                        <p className=\"text-xs text-red-500 mt-1\">\r\n                          {errors[followUpQuestion.id]}\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n                  </div>\r\n                )\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Render follow-ups without parent if parent is not visible but follow-ups are */}\r\n      {!isParentVisible && followUps.some(f => f.isVisible) && (\r\n        <div className=\"space-y-3\">\r\n          {followUps.map(({ question: followUpQuestion, isVisible: isFollowUpVisible }) => (\r\n            isFollowUpVisible && (\r\n              <div\r\n                key={followUpQuestion.id}\r\n                className=\"border border-neutral-200 dark:border-neutral-700 rounded-md p-4 bg-white dark:bg-neutral-800\"\r\n              >\r\n                <div className=\"mb-2\">\r\n                  <Label className=\"text-base font-medium\">\r\n                    {followUpQuestion.label}\r\n                    {followUpQuestion.isRequired && (\r\n                      <span className=\"text-red-500 ml-1\">*</span>\r\n                    )}\r\n                  </Label>\r\n                  {followUpQuestion.hint && (\r\n                    <p className=\"text-sm text-muted-foreground mt-1\">\r\n                      {followUpQuestion.hint}\r\n                    </p>\r\n                  )}\r\n                  {errors[followUpQuestion.id] && (\r\n                    <p className=\"text-sm text-red-500 mt-1\">\r\n                      {errors[followUpQuestion.id]}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n                <div className=\"mt-2\">{renderQuestionInput(followUpQuestion)}</div>\r\n              </div>\r\n            )\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NestedQuestionRenderer;\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAqBA,MAAM,yBAAgE,CAAC,EACrE,aAAa,EACb,mBAAmB,EACnB,MAAM,EACN,YAAY,EAAE,EACf;IACC,MAAM,EAAE,UAAU,cAAc,EAAE,WAAW,eAAe,EAAE,SAAS,EAAE,GAAG;IAE5E,yEAAyE;IACzE,IAAI,CAAC,mBAAmB,CAAC,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG;QACzD,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,WAAW;;YAE3B,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;;oCACd,eAAe,KAAK;oCACpB,eAAe,UAAU,kBACxB,6LAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;4BAGvC,eAAe,IAAI,kBAClB,6LAAC;gCAAE,WAAU;0CACV,eAAe,IAAI;;;;;;4BAGvB,MAAM,CAAC,eAAe,EAAE,CAAC,kBACxB,6LAAC;gCAAE,WAAU;0CACV,MAAM,CAAC,eAAe,EAAE,CAAC;;;;;;;;;;;;kCAIhC,6LAAC;wBAAI,WAAU;kCAAQ,oBAAoB;;;;;;oBAG1C,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAC9B,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;;oDACd,iBAAiB,KAAK;oDACtB,iBAAiB,UAAU,kBAC1B,6LAAC;wDAAK,WAAU;kEAAoB;;;;;;;;;;;;4CAGvC,iBAAiB,IAAI,kBACpB,6LAAC;gDAAE,WAAU;0DACV,iBAAiB,IAAI;;;;;;4CAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,6LAAC;gDAAE,WAAU;0DACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;kDAIlC,6LAAC;wCAAI,WAAU;kDAAQ,oBAAoB;;;;;;;+BArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;YA+BrC,CAAC,mBAAmB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,mBAClD,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,EAAE,UAAU,gBAAgB,EAAE,WAAW,iBAAiB,EAAE,GAC1E,mCACE,6LAAC;wBAEC,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;;4CACd,iBAAiB,KAAK;4CACtB,iBAAiB,UAAU,kBAC1B,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;;;;;;;oCAGvC,iBAAiB,IAAI,kBACpB,6LAAC;wCAAE,WAAU;kDACV,iBAAiB,IAAI;;;;;;oCAGzB,MAAM,CAAC,iBAAiB,EAAE,CAAC,kBAC1B,6LAAC;wCAAE,WAAU;kDACV,MAAM,CAAC,iBAAiB,EAAE,CAAC;;;;;;;;;;;;0CAIlC,6LAAC;gCAAI,WAAU;0CAAQ,oBAAoB;;;;;;;uBArBtC,iBAAiB,EAAE;;;;;;;;;;;;;;;;AA6BxC;KA7GM;uCA+GS", "debugId": null}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/utils/nestedGroups.ts"], "sourcesContent": ["import { Question, QuestionGroup } from \"@/types/formBuilder\";\n\n/**\n * Build nested group structure from flat array of groups and questions\n * This function creates a hierarchical structure where subgroups are nested within their parent groups\n */\nexport const buildNestedGroups = (\n  groups: QuestionGroup[],\n  questions: Question[]\n): QuestionGroup[] => {\n  const groupMap = new Map<number, QuestionGroup>();\n  \n  // Create a map of all groups with their subGroups and questions initialized\n  groups.forEach(group => {\n    // Get questions for this group\n    const groupQuestions = questions\n      .filter((q) => q.questionGroupId === group.id)\n      .sort((a, b) => a.position - b.position);\n    \n    groupMap.set(group.id, { \n      ...group, \n      subGroups: [],\n      question: groupQuestions\n    });\n  });\n\n  // Build the nested structure\n  const topLevelGroups: QuestionGroup[] = [];\n  groups.forEach(group => {\n    const groupWithSubGroups = groupMap.get(group.id)!;\n    \n    if (group.parentGroupId) {\n      // This is a child group, add it to its parent's subGroups\n      const parentGroup = groupMap.get(group.parentGroupId);\n      if (parentGroup) {\n        parentGroup.subGroups = parentGroup.subGroups || [];\n        parentGroup.subGroups.push(groupWithSubGroups);\n      }\n    } else {\n      // This is a top-level group\n      topLevelGroups.push(groupWithSubGroups);\n    }\n  });\n\n  return topLevelGroups;\n};\n\n/**\n * Create unified form items (groups and ungrouped questions) for rendering\n * This maintains the same ordering logic as the form builder\n */\nexport const createUnifiedFormItems = (\n  nestedGroups: QuestionGroup[],\n  ungroupedQuestions: Question[]\n): Array<{\n  type: 'group' | 'question';\n  data: QuestionGroup | Question;\n  order: number;\n  originalPosition?: number;\n}> => {\n  const items: Array<{\n    type: 'group' | 'question';\n    data: QuestionGroup | Question;\n    order: number;\n    originalPosition?: number;\n  }> = [];\n\n  // Add question groups\n  nestedGroups.forEach((group: QuestionGroup) => {\n    // For groups, find the minimum position of questions in the group (including subgroups)\n    const getAllGroupQuestions = (g: QuestionGroup): Question[] => {\n      const directQuestions = g.question || [];\n      const subGroupQuestions = (g.subGroups || []).flatMap(getAllGroupQuestions);\n      return [...directQuestions, ...subGroupQuestions];\n    };\n\n    const allGroupQuestions = getAllGroupQuestions(group);\n    const minQuestionPosition = allGroupQuestions.length > 0\n      ? Math.min(...allGroupQuestions.map(q => q.position))\n      : group.order;\n\n    items.push({\n      type: 'group',\n      data: group,\n      order: minQuestionPosition,\n      originalPosition: minQuestionPosition\n    });\n  });\n\n  // Add ungrouped questions\n  ungroupedQuestions.forEach((question: Question) => {\n    items.push({\n      type: 'question',\n      data: question,\n      order: question.position,\n      originalPosition: question.position\n    });\n  });\n\n  // Sort by order/position with secondary sort for consistency\n  return items.sort((a, b) => {\n    if (a.order === b.order) {\n      return (a.originalPosition || a.order) - (b.originalPosition || b.order);\n    }\n    return a.order - b.order;\n  });\n};\n\n/**\n * Get ungrouped questions (questions not belonging to any group)\n */\nexport const getUngroupedQuestions = (questions: Question[]): Question[] => {\n  return questions.filter(\n    (q) => q.questionGroupId === null || q.questionGroupId === undefined\n  );\n};\n\n/**\n * Get all group IDs from nested group structure (including subgroups)\n */\nexport const getAllGroupIds = (groups: QuestionGroup[]): number[] => {\n  const ids: number[] = [];\n  groups.forEach(group => {\n    ids.push(group.id);\n    if (group.subGroups && group.subGroups.length > 0) {\n      ids.push(...getAllGroupIds(group.subGroups));\n    }\n  });\n  return ids;\n};\n\n/**\n * Initialize expansion state for all groups (including nested ones)\n */\nexport const initializeGroupExpansionState = (\n  nestedGroups: QuestionGroup[],\n  defaultExpanded: boolean = true\n): Record<number, boolean> => {\n  const initialExpandedState: Record<number, boolean> = {};\n  const allGroupIds = getAllGroupIds(nestedGroups);\n  allGroupIds.forEach((groupId) => {\n    initialExpandedState[groupId] = defaultExpanded;\n  });\n  return initialExpandedState;\n};\n"], "names": [], "mappings": ";;;;;;;AAMO,MAAM,oBAAoB,CAC/B,QACA;IAEA,MAAM,WAAW,IAAI;IAErB,4EAA4E;IAC5E,OAAO,OAAO,CAAC,CAAA;QACb,+BAA+B;QAC/B,MAAM,iBAAiB,UACpB,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE,EAC5C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QAEzC,SAAS,GAAG,CAAC,MAAM,EAAE,EAAE;YACrB,GAAG,KAAK;YACR,WAAW,EAAE;YACb,UAAU;QACZ;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAkC,EAAE;IAC1C,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,qBAAqB,SAAS,GAAG,CAAC,MAAM,EAAE;QAEhD,IAAI,MAAM,aAAa,EAAE;YACvB,0DAA0D;YAC1D,MAAM,cAAc,SAAS,GAAG,CAAC,MAAM,aAAa;YACpD,IAAI,aAAa;gBACf,YAAY,SAAS,GAAG,YAAY,SAAS,IAAI,EAAE;gBACnD,YAAY,SAAS,CAAC,IAAI,CAAC;YAC7B;QACF,OAAO;YACL,4BAA4B;YAC5B,eAAe,IAAI,CAAC;QACtB;IACF;IAEA,OAAO;AACT;AAMO,MAAM,yBAAyB,CACpC,cACA;IAOA,MAAM,QAKD,EAAE;IAEP,sBAAsB;IACtB,aAAa,OAAO,CAAC,CAAC;QACpB,wFAAwF;QACxF,MAAM,uBAAuB,CAAC;YAC5B,MAAM,kBAAkB,EAAE,QAAQ,IAAI,EAAE;YACxC,MAAM,oBAAoB,CAAC,EAAE,SAAS,IAAI,EAAE,EAAE,OAAO,CAAC;YACtD,OAAO;mBAAI;mBAAoB;aAAkB;QACnD;QAEA,MAAM,oBAAoB,qBAAqB;QAC/C,MAAM,sBAAsB,kBAAkB,MAAM,GAAG,IACnD,KAAK,GAAG,IAAI,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KACjD,MAAM,KAAK;QAEf,MAAM,IAAI,CAAC;YACT,MAAM;YACN,MAAM;YACN,OAAO;YACP,kBAAkB;QACpB;IACF;IAEA,0BAA0B;IAC1B,mBAAmB,OAAO,CAAC,CAAC;QAC1B,MAAM,IAAI,CAAC;YACT,MAAM;YACN,MAAM;YACN,OAAO,SAAS,QAAQ;YACxB,kBAAkB,SAAS,QAAQ;QACrC;IACF;IAEA,6DAA6D;IAC7D,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG;QACpB,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;YACvB,OAAO,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK;QACzE;QACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;IAC1B;AACF;AAKO,MAAM,wBAAwB,CAAC;IACpC,OAAO,UAAU,MAAM,CACrB,CAAC,IAAM,EAAE,eAAe,KAAK,QAAQ,EAAE,eAAe,KAAK;AAE/D;AAKO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,MAAgB,EAAE;IACxB,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,IAAI,CAAC,MAAM,EAAE;QACjB,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,GAAG;YACjD,IAAI,IAAI,IAAI,eAAe,MAAM,SAAS;QAC5C;IACF;IACA,OAAO;AACT;AAKO,MAAM,gCAAgC,CAC3C,cACA,kBAA2B,IAAI;IAE/B,MAAM,uBAAgD,CAAC;IACvD,MAAM,cAAc,eAAe;IACnC,YAAY,OAAO,CAAC,CAAC;QACnB,oBAAoB,CAAC,QAAQ,GAAG;IAClC;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/form-builder.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\nimport { ContextType } from \"@/types\";\r\n\r\nconst getQuestionsEndPoint = (contextType: ContextType) => {\r\n  if (contextType === \"project\") return \"/questions\";\r\n  else if (contextType === \"template\") return \"/template-questions\";\r\n  else if (contextType === \"questionBlock\") return \"/question-blocks\";\r\n  throw new Error(\"Unsupported context type\");\r\n};\r\n\r\nconst fetchQuestions = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/questions/${projectId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst fetchTemplateQuestions = async ({\r\n  templateId,\r\n}: {\r\n  templateId: number;\r\n}) => {\r\n  const { data } = await axios.get(`/template-questions/${templateId}`);\r\n  return data.questions;\r\n};\r\n\r\nconst addQuestion = async ({\r\n  contextType,\r\n  contextId,\r\n  dataToSend,\r\n  position,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint?: string;\r\n    placeholder?: string;\r\n    inputType: string;\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n    file?: File;\r\n  };\r\n  position?: number;\r\n}) => {\r\n  const url =\r\n    contextType === \"questionBlock\"\r\n      ? `${getQuestionsEndPoint(contextType)}`\r\n      : `${getQuestionsEndPoint(contextType)}/${contextId}`;\r\n\r\n  // Validate required fields\r\n  if (!dataToSend.label || !dataToSend.inputType) {\r\n    throw new Error(\"Label and inputType are required\");\r\n  }\r\n\r\n  // Check if this input type requires options\r\n  const needsOptions = [\"selectone\", \"selectmany\"].includes(\r\n    dataToSend.inputType\r\n  );\r\n  const hasFile = dataToSend.file instanceof File;\r\n  const hasOptions =\r\n    Array.isArray(dataToSend.questionOptions) &&\r\n    dataToSend.questionOptions.length > 0;\r\n\r\n  // Validate options based on input type and upload method\r\n  if (needsOptions && !hasFile && !hasOptions) {\r\n    throw new Error(\"Options are required for select input types\");\r\n  }\r\n\r\n  if (hasFile) {\r\n    const formData = new FormData();\r\n\r\n    // Add basic question data\r\n    formData.append(\"label\", dataToSend.label);\r\n    // Convert boolean to string in a way backend can parse\r\n    formData.append(\"isRequired\", dataToSend.isRequired ? \"true\" : \"false\");\r\n    formData.append(\"inputType\", dataToSend.inputType);\r\n    if (dataToSend.hint) formData.append(\"hint\", dataToSend.hint);\r\n    if (dataToSend.placeholder)\r\n      formData.append(\"placeholder\", dataToSend.placeholder);\r\n    // Convert number to string\r\n    formData.append(\"position\", String(position || 1));\r\n\r\n    // Add file with the correct field name\r\n    formData.append(\"file\", dataToSend.file as File);\r\n\r\n    // Important: Do NOT include questionOptions when uploading a file\r\n    // They will be parsed from the file on the server\r\n\r\n    try {\r\n      const { data } = await axios.post(url, formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"Upload error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to upload question with file: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  } else {\r\n    // Regular JSON request (no file)\r\n    try {\r\n      const { data } = await axios.post(url, {\r\n        label: dataToSend.label,\r\n        isRequired: dataToSend.isRequired,\r\n        hint: dataToSend.hint,\r\n        placeholder: dataToSend.placeholder,\r\n        inputType: dataToSend.inputType,\r\n        questionOptions: dataToSend.questionOptions,\r\n        position: position || 1,\r\n      });\r\n      return data;\r\n    } catch (error: any) {\r\n      console.error(\r\n        \"API error details:\",\r\n        error.response?.data || error.message\r\n      );\r\n      throw new Error(\r\n        `Failed to add question: ${\r\n          error.response?.data?.message || error.message\r\n        }`\r\n      );\r\n    }\r\n  }\r\n};\r\nconst deleteQuestion = async ({\r\n  contextType,\r\n  id,\r\n  projectId,\r\n}: {\r\n  contextType: ContextType;\r\n  id: number;\r\n  projectId: number;\r\n}) => {\r\n  const { data } = await axios.delete(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${projectId}`\r\n  );\r\n  return data;\r\n};\r\n\r\nconst duplicateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n}) => {\r\n  // For question blocks, we don't need to send the contextId in the body\r\n  // The userId is taken from the authenticated user in the backend\r\n  const requestBody =\r\n    contextType === \"questionBlock\"\r\n      ? {}\r\n      : contextType === \"project\"\r\n        ? { projectId: contextId }\r\n        : { templateId: contextId };\r\n\r\n  const { data } = await axios.post(\r\n    `${getQuestionsEndPoint(\r\n      contextType\r\n    )}/duplicate/${id}?projectId=${contextId}`,\r\n    requestBody\r\n  );\r\n\r\n  return data;\r\n};\r\n\r\nconst updateQuestion = async ({\r\n  id,\r\n  contextType,\r\n  dataToSend,\r\n  contextId,\r\n}: {\r\n  id: number;\r\n  contextType: ContextType;\r\n  dataToSend: {\r\n    label: string;\r\n    isRequired: boolean;\r\n    hint: string;\r\n    placeholder: string;\r\n    position?: number; // Optional position field to preserve question order\r\n    questionOptions?: {\r\n      label: string;\r\n      sublabel?: string;\r\n      code: string;\r\n      nextQuestionId?: number | null;\r\n    }[];\r\n  };\r\n  contextId: number;\r\n}) => {\r\n  const { data } = await axios.patch(\r\n    `${getQuestionsEndPoint(contextType)}/${id}?projectId=${contextId}`,\r\n    dataToSend\r\n  );\r\n  return data;\r\n};\r\n\r\nconst fetchQuestionBlockQuestions = async () => {\r\n  try {\r\n    const response = await axios.get(`/question-blocks`);\r\n    return response.data.questions || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching question block questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst updateQuestionPositions = async ({\r\n  contextType,\r\n  contextId,\r\n  questionPositions,\r\n}: {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  questionPositions: { id: number; position: number }[];\r\n}) => {\r\n  // Only support position updates for projects currently\r\n  if (contextType !== \"project\") {\r\n    throw new Error(\r\n      \"Question position updates are only supported for projects\"\r\n    );\r\n  }\r\n\r\n  const url = `${getQuestionsEndPoint(\r\n    contextType\r\n  )}/positions?projectId=${contextId}`;\r\n  const payload = { questionPositions };\r\n\r\n  try {\r\n    const { data } = await axios.patch(url, payload);\r\n    return data;\r\n  } catch (error: any) {\r\n    console.error(\"Update failed - Full error:\", error);\r\n    console.error(\"Update failed - Error details:\", {\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      data: error.response?.data,\r\n      message: error.message,\r\n      config: {\r\n        url: error.config?.url,\r\n        method: error.config?.method,\r\n        data: error.config?.data,\r\n      },\r\n    });\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Fetch form builder data with ordered structure (groups and questions)\r\nconst fetchFormBuilderData = async ({ projectId }: { projectId: number }) => {\r\n  const { data } = await axios.get(`/projects/getalldata/${projectId}`);\r\n  return data.data;\r\n};\r\n\r\nexport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  addQuestion,\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  updateQuestion,\r\n  fetchQuestionBlockQuestions,\r\n  updateQuestionPositions,\r\n  fetchFormBuilderData,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAGA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,gBAAgB,WAAW,OAAO;SACjC,IAAI,gBAAgB,YAAY,OAAO;SACvC,IAAI,gBAAgB,iBAAiB,OAAO;IACjD,MAAM,IAAI,MAAM;AAClB;AAEA,MAAM,iBAAiB,OAAO,EAAE,SAAS,EAAyB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW;IAC1D,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,yBAAyB,OAAO,EACpC,UAAU,EAGX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY;IACpE,OAAO,KAAK,SAAS;AACvB;AAEA,MAAM,cAAc,OAAO,EACzB,WAAW,EACX,SAAS,EACT,UAAU,EACV,QAAQ,EAmBT;IACC,MAAM,MACJ,gBAAgB,kBACZ,GAAG,qBAAqB,cAAc,GACtC,GAAG,qBAAqB,aAAa,CAAC,EAAE,WAAW;IAEzD,2BAA2B;IAC3B,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,SAAS,EAAE;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,MAAM,eAAe;QAAC;QAAa;KAAa,CAAC,QAAQ,CACvD,WAAW,SAAS;IAEtB,MAAM,UAAU,WAAW,IAAI,YAAY;IAC3C,MAAM,aACJ,MAAM,OAAO,CAAC,WAAW,eAAe,KACxC,WAAW,eAAe,CAAC,MAAM,GAAG;IAEtC,yDAAyD;IACzD,IAAI,gBAAgB,CAAC,WAAW,CAAC,YAAY;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,SAAS;QACX,MAAM,WAAW,IAAI;QAErB,0BAA0B;QAC1B,SAAS,MAAM,CAAC,SAAS,WAAW,KAAK;QACzC,uDAAuD;QACvD,SAAS,MAAM,CAAC,cAAc,WAAW,UAAU,GAAG,SAAS;QAC/D,SAAS,MAAM,CAAC,aAAa,WAAW,SAAS;QACjD,IAAI,WAAW,IAAI,EAAE,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAC5D,IAAI,WAAW,WAAW,EACxB,SAAS,MAAM,CAAC,eAAe,WAAW,WAAW;QACvD,2BAA2B;QAC3B,SAAS,MAAM,CAAC,YAAY,OAAO,YAAY;QAE/C,uCAAuC;QACvC,SAAS,MAAM,CAAC,QAAQ,WAAW,IAAI;QAEvC,kEAAkE;QAClE,kDAAkD;QAElD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK,UAAU;gBAC/C,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,yBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,qCAAqC,EACpC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF,OAAO;QACL,iCAAiC;QACjC,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,KAAK;gBACrC,OAAO,WAAW,KAAK;gBACvB,YAAY,WAAW,UAAU;gBACjC,MAAM,WAAW,IAAI;gBACrB,aAAa,WAAW,WAAW;gBACnC,WAAW,WAAW,SAAS;gBAC/B,iBAAiB,WAAW,eAAe;gBAC3C,UAAU,YAAY;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,sBACA,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;YAEvC,MAAM,IAAI,MACR,CAAC,wBAAwB,EACvB,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAC9C;QAEN;IACF;AACF;AACA,MAAM,iBAAiB,OAAO,EAC5B,WAAW,EACX,EAAE,EACF,SAAS,EAKV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW;IAErE,OAAO;AACT;AAEA,MAAM,oBAAoB,OAAO,EAC/B,EAAE,EACF,WAAW,EACX,SAAS,EAKV;IACC,uEAAuE;IACvE,iEAAiE;IACjE,MAAM,cACJ,gBAAgB,kBACZ,CAAC,IACD,gBAAgB,YACd;QAAE,WAAW;IAAU,IACvB;QAAE,YAAY;IAAU;IAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,GAAG,qBACD,aACA,WAAW,EAAE,GAAG,WAAW,EAAE,WAAW,EAC1C;IAGF,OAAO;AACT;AAEA,MAAM,iBAAiB,OAAO,EAC5B,EAAE,EACF,WAAW,EACX,UAAU,EACV,SAAS,EAkBV;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAChC,GAAG,qBAAqB,aAAa,CAAC,EAAE,GAAG,WAAW,EAAE,WAAW,EACnE;IAEF,OAAO;AACT;AAEA,MAAM,8BAA8B;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,gBAAgB,CAAC;QACnD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAEA,MAAM,0BAA0B,OAAO,EACrC,WAAW,EACX,SAAS,EACT,iBAAiB,EAKlB;IACC,uDAAuD;IACvD,IAAI,gBAAgB,WAAW;QAC7B,MAAM,IAAI,MACR;IAEJ;IAEA,MAAM,MAAM,GAAG,qBACb,aACA,qBAAqB,EAAE,WAAW;IACpC,MAAM,UAAU;QAAE;IAAkB;IAEpC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,KAAK;QACxC,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,QAAQ,KAAK,CAAC,kCAAkC;YAC9C,QAAQ,MAAM,QAAQ,EAAE;YACxB,YAAY,MAAM,QAAQ,EAAE;YAC5B,MAAM,MAAM,QAAQ,EAAE;YACtB,SAAS,MAAM,OAAO;YACtB,QAAQ;gBACN,KAAK,MAAM,MAAM,EAAE;gBACnB,QAAQ,MAAM,MAAM,EAAE;gBACtB,MAAM,MAAM,MAAM,EAAE;YACtB;QACF;QACA,MAAM;IACR;AACF;AAEA,wEAAwE;AACxE,MAAM,uBAAuB,OAAO,EAAE,SAAS,EAAyB;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,WAAW;IACpE,OAAO,KAAK,IAAI;AAClB", "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/api/question-groups.ts"], "sourcesContent": ["import axios from \"@/lib/axios\";\r\n\r\n/**\r\n * Fetch all question groups for a project\r\n */\r\nexport const fetchQuestionGroups = async ({\r\n  projectId,\r\n}: {\r\n  projectId: number;\r\n}) => {\r\n  try {\r\n    // Use the project endpoint to fetch the project with its question groups\r\n    const { data } = await axios.get(`/projects/form/${projectId}`);\r\n\r\n    // Extract question groups from the project data\r\n    const questionGroups = data.data?.project?.questionGroup || [];\r\n    return questionGroups;\r\n  } catch (error) {\r\n    console.error(\r\n      \"Error fetching question groups from project endpoint:\",\r\n      error\r\n    );\r\n\r\n    // Fallback to direct question groups endpoint\r\n    try {\r\n      const { data } = await axios.post(`/question-groups`, { projectId });\r\n      return data.data?.projectGroup || [];\r\n    } catch (fallbackError) {\r\n      console.error(\"Error in fallback fetch:\", fallbackError);\r\n\r\n      // Last resort: create a dummy group for debugging\r\n      if (process.env.NODE_ENV === \"development\") {\r\n        return [];\r\n      }\r\n\r\n      return [];\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Create a new question group\r\n */\r\nexport const createQuestionGroup = async ({\r\n  title,\r\n  order,\r\n  projectId,\r\n  selectedQuestionIds,\r\n  parentGroupId,\r\n}: {\r\n  title: string;\r\n  order: number;\r\n  projectId: number;\r\n  selectedQuestionIds?: number[];\r\n  parentGroupId?: number;\r\n}) => {\r\n  try {\r\n    const { data } = await axios.post(`/question-groups`, {\r\n      title,\r\n      order,\r\n      projectId,\r\n      selectedQuestionIds: selectedQuestionIds || [],\r\n      parentGroupId,\r\n    });\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error creating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Update an existing question group\r\n */\r\nexport const updateQuestionGroup = async ({\r\n  id,\r\n  title,\r\n  order,\r\n  selectedQuestionIds,\r\n}: {\r\n  id: number;\r\n  title: string;\r\n  order: number;\r\n  selectedQuestionIds?: number[];\r\n}) => {\r\n  try {\r\n    const { data } = await axios.patch(`/question-groups`, {\r\n      id,\r\n      title,\r\n      order,\r\n      selectedQuestionIds,\r\n    });\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error updating question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group\r\n */\r\nexport const deleteQuestionGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    const { data } = await axios.delete(`/question-groups/${id}`);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Delete a question group and all its questions\r\n */\r\nexport const deleteQuestionAndGroup = async ({ id }: { id: number }) => {\r\n  try {\r\n    const { data } = await axios.delete(\r\n      `/question-groups/group/question/${id}`\r\n    );\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting question group and questions:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Remove a question from a group\r\n */\r\nexport const removeQuestionFromGroup = async ({\r\n  groupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/remove`, {\r\n    groupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n/**\r\n * Move a question from one group to another\r\n */\r\nexport const moveQuestionBetweenGroups = async ({\r\n  groupId,\r\n  newGroupId,\r\n  questionId,\r\n}: {\r\n  groupId: number;\r\n  newGroupId: number;\r\n  questionId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/question/move`, {\r\n    groupId,\r\n    newGroupId,\r\n    questionId,\r\n  });\r\n  return data;\r\n};\r\n\r\n/**\r\n * Move a group inside another group (create nested group)\r\n */\r\nexport const moveGroupInsideGroup = async ({\r\n  childGroupId,\r\n  parentGroupId,\r\n}: {\r\n  childGroupId: number;\r\n  parentGroupId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/group/add`, {\r\n    childGroupId,\r\n    ParentGroupId: parentGroupId,\r\n  });\r\n  return data;\r\n};\r\n\r\n/**\r\n * Remove a group from its parent group (make it top-level)\r\n */\r\nexport const removeGroupFromParent = async ({\r\n  groupId,\r\n}: {\r\n  groupId: number;\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/group/remove`, {\r\n    groupId,\r\n  });\r\n  return data;\r\n};\r\n\r\n/**\r\n * Update group order/positions\r\n */\r\nexport const updateGroupPositions = async ({\r\n  projectId,\r\n  groupPositions,\r\n}: {\r\n  projectId: number;\r\n  groupPositions: { id: number; order: number; parentGroupId?: number }[];\r\n}) => {\r\n  const { data } = await axios.patch(`/question-groups/positions`, {\r\n    projectId,\r\n    groupPositions,\r\n  });\r\n  return data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AA+BU;AA/BV;;AAKO,MAAM,sBAAsB,OAAO,EACxC,SAAS,EAGV;IACC,IAAI;QACF,yEAAyE;QACzE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;QAE9D,gDAAgD;QAChD,MAAM,iBAAiB,KAAK,IAAI,EAAE,SAAS,iBAAiB,EAAE;QAC9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,yDACA;QAGF,8CAA8C;QAC9C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;gBAAE;YAAU;YAClE,OAAO,KAAK,IAAI,EAAE,gBAAgB,EAAE;QACtC,EAAE,OAAO,eAAe;YACtB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,kDAAkD;YAClD,wCAA4C;gBAC1C,OAAO,EAAE;YACX;;QAGF;IACF;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,KAAK,EACL,KAAK,EACL,SAAS,EACT,mBAAmB,EACnB,aAAa,EAOd;IACC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACpD;YACA;YACA;YACA,qBAAqB,uBAAuB,EAAE;YAC9C;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EACxC,EAAE,EACF,KAAK,EACL,KAAK,EACL,mBAAmB,EAMpB;IACC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,EAAE;YACrD;YACA;YACA;YACA;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,sBAAsB,OAAO,EAAE,EAAE,EAAkB;IAC9D,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI;QAC5D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,MAAM,yBAAyB,OAAO,EAAE,EAAE,EAAkB;IACjE,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,MAAM,CACjC,CAAC,gCAAgC,EAAE,IAAI;QAEzC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;AACF;AAKO,MAAM,0BAA0B,OAAO,EAC5C,OAAO,EACP,UAAU,EAIX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;QACrE;QACA;IACF;IACA,OAAO;AACT;AAKO,MAAM,4BAA4B,OAAO,EAC9C,OAAO,EACP,UAAU,EACV,UAAU,EAKX;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAE;QACnE;QACA;QACA;IACF;IACA,OAAO;AACT;AAKO,MAAM,uBAAuB,OAAO,EACzC,YAAY,EACZ,aAAa,EAId;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAE;QAC/D;QACA,eAAe;IACjB;IACA,OAAO;AACT;AAKO,MAAM,wBAAwB,OAAO,EAC1C,OAAO,EAGR;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAE;QAClE;IACF;IACA,OAAO;AACT;AAKO,MAAM,uBAAuB,OAAO,EACzC,SAAS,EACT,cAAc,EAIf;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,+GAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,0BAA0B,CAAC,EAAE;QAC/D;QACA;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-preview/form-preview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport {\r\n  X,\r\n  ChevronLeft,\r\n  Calendar,\r\n  Clock,\r\n  ChevronDown,\r\n  ChevronRight,\r\n} from \"lucide-react\";\r\nimport { TableInput } from \"@/components/form-inputs/TableInput\";\r\nimport {\r\n  getVisibleQuestions,\r\n  cleanupHiddenAnswers,\r\n  validateVisibleQuestions,\r\n  getNestedQuestions,\r\n} from \"@/lib/conditionalQuestions\";\r\nimport NestedQuestionRenderer from \"@/components/form-inputs/NestedQuestionRenderer\";\r\nimport {\r\n  buildNestedGroups,\r\n  createUnifiedFormItems,\r\n  getUngroupedQuestions,\r\n  initializeGroupExpansionState\r\n} from \"@/lib/utils/nestedGroups\";\r\nimport { ContextType } from \"@/types\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { fetchFormBuilderData, fetchTemplateQuestions, fetchQuestionBlockQuestions } from \"@/lib/api/form-builder\";\r\nimport { fetchQuestionGroups } from \"@/lib/api/question-groups\";\r\n\r\ninterface FormPreviewProps {\r\n  contextType?: ContextType;\r\n  contextId: number;\r\n  onClose: () => void;\r\n  hashedId?: string; // Add hashedId prop to pass to the test form\r\n}\r\n\r\nexport function FormPreview({\r\n  contextType = \"project\",\r\n  contextId,\r\n  onClose,\r\n  hashedId,\r\n}: FormPreviewProps) {\r\n  const [answers, setAnswers] = useState<Record<string, any>>({});\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);\r\n  const [nestedQuestions, setNestedQuestions] = useState<\r\n    Array<{\r\n      question: Question;\r\n      isVisible: boolean;\r\n      isFollowUp: boolean;\r\n      followUps: Array<{\r\n        question: Question;\r\n        isVisible: boolean;\r\n      }>;\r\n    }>\r\n  >([]);\r\n  const [expandedGroups, setExpandedGroups] = useState<Record<number, boolean>>(\r\n    {}\r\n  );\r\n\r\n  const t = useTranslations();\r\n  const queryClient = useQueryClient();\r\n\r\n  // Query keys\r\n  const formBuilderDataQueryKey = [\"formBuilderData\", contextId];\r\n  const questionsQueryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\", contextId]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\", contextId]\r\n      : [\"questionBlockQuestions\", contextId];\r\n\r\n  // Fetch form builder data (unified questions and groups) for projects\r\n  const { data: formBuilderData, isLoading: isLoadingFormData, isError: isFormDataError } = useQuery({\r\n    queryKey: formBuilderDataQueryKey,\r\n    queryFn: () => fetchFormBuilderData({ projectId: contextId }),\r\n    enabled: contextType === \"project\",\r\n  });\r\n\r\n  // Fallback API calls for non-project contexts\r\n  const { data: fallbackQuestions = [], isLoading: isLoadingFallback, isError: isFallbackError } = useQuery<Question[]>({\r\n    queryKey: questionsQueryKey,\r\n    queryFn: () => {\r\n      if (contextType === \"template\") {\r\n        return fetchTemplateQuestions({ templateId: contextId });\r\n      } else if (contextType === \"questionBlock\") {\r\n        return fetchQuestionBlockQuestions();\r\n      }\r\n      return [];\r\n    },\r\n    enabled: contextType !== \"project\",\r\n  });\r\n\r\n  // Fallback question groups for non-project contexts\r\n  const { data: fallbackQuestionGroups = [] } = useQuery<QuestionGroup[]>({\r\n    queryKey: [\"questionGroups\", contextId],\r\n    queryFn: () => fetchQuestionGroups({ projectId: contextId }),\r\n    enabled: contextType !== \"project\",\r\n  });\r\n\r\n  // Extract questions and groups from the unified data\r\n  const questions: Question[] = React.useMemo(() => {\r\n    if (contextType === \"project\") {\r\n      if (!formBuilderData?.items) {\r\n        return [];\r\n      }\r\n\r\n      // Extract questions from the items array\r\n      const extractedQuestions: Question[] = [];\r\n\r\n      formBuilderData.items.forEach((item: any) => {\r\n        if (item.type === \"question\") {\r\n          extractedQuestions.push(item);\r\n        } else if (item.type === \"group\" && item.questions) {\r\n          // Add questions from groups\r\n          item.questions.forEach((question: any) => {\r\n            extractedQuestions.push(question);\r\n          });\r\n        }\r\n      });\r\n\r\n      return extractedQuestions;\r\n    } else {\r\n      // For non-project contexts, use fallback questions\r\n      return fallbackQuestions;\r\n    }\r\n  }, [formBuilderData, contextType, fallbackQuestions]);\r\n\r\n  // Extract question groups from the unified data\r\n  const questionGroups: QuestionGroup[] = React.useMemo(() => {\r\n    if (contextType === \"project\") {\r\n      if (!formBuilderData?.items) {\r\n        return [];\r\n      }\r\n\r\n      return formBuilderData.items\r\n        .filter((item: any) => item.type === \"group\")\r\n        .map((item: any) => ({\r\n          ...item,\r\n          question: item.questions || []\r\n        }));\r\n    } else {\r\n      // For non-project contexts, use fallback question groups\r\n      return fallbackQuestionGroups;\r\n    }\r\n  }, [formBuilderData, contextType, fallbackQuestionGroups]);\r\n\r\n  // Show loading state\r\n  const isLoading = contextType === \"project\" ? isLoadingFormData : isLoadingFallback;\r\n  const isError = contextType === \"project\" ? isFormDataError : isFallbackError;\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700\">\r\n        <div className=\"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700\">\r\n          <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\r\n            <ChevronLeft className=\"h-5 w-5\" />\r\n          </Button>\r\n          <h2 className=\"text-lg font-semibold\">{t('formPreview')}</h2>\r\n          <Button\r\n            className=\"cursor-pointer hover:bg-neutral-200\"\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={onClose}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </Button>\r\n        </div>\r\n        <div className=\"min-h-[60vh] flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4\"></div>\r\n            <p className=\"text-muted-foreground\">{t('loadingFormData')}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (isError) {\r\n    return (\r\n      <div className=\"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700\">\r\n        <div className=\"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700\">\r\n          <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\r\n            <ChevronLeft className=\"h-5 w-5\" />\r\n          </Button>\r\n          <h2 className=\"text-lg font-semibold\">{t('formPreview')}</h2>\r\n          <Button\r\n            className=\"cursor-pointer hover:bg-neutral-200\"\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={onClose}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </Button>\r\n        </div>\r\n        <div className=\"min-h-[60vh] flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <p className=\"text-red-500 mb-4\">{t('formLoadError')}</p>\r\n            <button\r\n              onClick={() => {\r\n                if (contextType === \"project\") {\r\n                  queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n                } else {\r\n                  queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n                }\r\n              }}\r\n              className=\"btn-primary\"\r\n            >\r\n              {t('retry')}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Initialize answers when questions change\r\n  useEffect(() => {\r\n    if (questions.length > 0) {\r\n      const initialAnswers: Record<string, any> = {};\r\n      questions.forEach((question) => {\r\n        initialAnswers[question.id] =\r\n          question.inputType === \"selectmany\" ? [] : \"\";\r\n      });\r\n      setAnswers(initialAnswers);\r\n    }\r\n  }, [questions.length]); // Only depend on questions.length to avoid infinite loops\r\n\r\n  // Update visible questions when answers or questions change\r\n  useEffect(() => {\r\n    if (questions.length > 0) {\r\n      const newVisibleQuestions = getVisibleQuestions(questions, answers);\r\n      setVisibleQuestions(newVisibleQuestions);\r\n\r\n      // Calculate nested question structure\r\n      const newNestedQuestions = getNestedQuestions(questions, answers);\r\n      setNestedQuestions(newNestedQuestions);\r\n    }\r\n  }, [questions, answers]);\r\n\r\n  // Separate effect for cleaning up hidden answers to prevent infinite loops\r\n  useEffect(() => {\r\n    if (visibleQuestions.length > 0 && Object.keys(answers).length > 0) {\r\n      const cleanedAnswers = cleanupHiddenAnswers(answers, visibleQuestions);\r\n      const answersChanged = Object.keys(cleanedAnswers).length !== Object.keys(answers).length ||\r\n        Object.keys(cleanedAnswers).some(key => cleanedAnswers[key] !== answers[key]);\r\n\r\n      if (answersChanged) {\r\n        setAnswers(cleanedAnswers);\r\n      }\r\n    }\r\n  }, [visibleQuestions]); // Only depend on visibleQuestions, not answers\r\n\r\n  // Build nested group structure\r\n  const nestedQuestionGroups = React.useMemo(() => {\r\n    return buildNestedGroups(questionGroups, questions);\r\n  }, [questionGroups, questions]);\r\n\r\n  // Get ungrouped questions\r\n  const ungroupedQuestions = React.useMemo(() => {\r\n    return getUngroupedQuestions(questions);\r\n  }, [questions]);\r\n\r\n  // Initialize all groups (including nested ones) as expanded\r\n  useEffect(() => {\r\n    const initialExpandedState = initializeGroupExpansionState(nestedQuestionGroups, true);\r\n    setExpandedGroups(initialExpandedState);\r\n  }, [nestedQuestionGroups]);\r\n\r\n  // Create a unified list of form items (groups and individual questions) for dynamic ordering\r\n  const unifiedFormItems = React.useMemo(() => {\r\n    // Only add question groups for projects (not templates or question blocks)\r\n    if (contextType === \"project\") {\r\n      return createUnifiedFormItems(nestedQuestionGroups, ungroupedQuestions);\r\n    } else {\r\n      // For templates and question blocks, just return all questions\r\n      return questions.map((question: Question) => ({\r\n        type: \"question\" as const,\r\n        data: question,\r\n        order: question.position,\r\n        originalPosition: question.position,\r\n      }));\r\n    }\r\n  }, [nestedQuestionGroups, ungroupedQuestions, questions, contextType]);\r\n\r\n  // Toggle group expansion\r\n  const toggleGroupExpansion = (groupId: number) => {\r\n    setExpandedGroups((prev) => ({\r\n      ...prev,\r\n      [groupId]: !prev[groupId],\r\n    }));\r\n  };\r\n\r\n  const handleInputChange = (questionId: number, value: any) => {\r\n    setAnswers((prev) => ({\r\n      ...prev,\r\n      [questionId]: value,\r\n    }));\r\n    setErrors((prev) => ({\r\n      ...prev,\r\n      [questionId]: \"\",\r\n    }));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    // Only validate visible questions\r\n    const newErrors = validateVisibleQuestions(visibleQuestions, answers);\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  // Render a single question with its input\r\n  const renderQuestion = (question: Question) => (\r\n    <div\r\n      key={question.id}\r\n      className=\"border border-neutral-500 dark:border-neutral-700 rounded-md p-4\"\r\n    >\r\n      <div className=\"mb-2\">\r\n        <Label className=\"text-base font-medium\">\r\n          {question.label}\r\n          {question.isRequired && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </Label>\r\n        {question.hint && (\r\n          <p className=\"text-sm text-muted-foreground mt-1\">{question.hint}</p>\r\n        )}\r\n        {errors[question.id] && (\r\n          <p className=\"text-sm text-red-500 mt-1\">{errors[question.id]}</p>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-2\">{renderQuestionInput(question)}</div>\r\n    </div>\r\n  );\r\n\r\n  const renderQuestionInput = (question: Question) => {\r\n    const value =\r\n      answers[question.id] ?? (question.inputType === \"selectmany\" ? [] : \"\");\r\n\r\n    switch (question.inputType) {\r\n      case \"text\":\r\n        if (question.hint?.includes(\"multiline\")) {\r\n          return (\r\n            <Textarea\r\n              value={value}\r\n              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>\r\n                handleInputChange(question.id, e.target.value)\r\n              }\r\n              placeholder={question.hint || t('yourAnswer')}\r\n              required={question.isRequired}\r\n            />\r\n          );\r\n        }\r\n        return (\r\n          <Input\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || t('yourAnswer')}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"number\":\r\n        return (\r\n          <Input\r\n            type=\"number\"\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || t('yourAnswer')}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"decimal\":\r\n        return (\r\n          <Input\r\n            type=\"number\"\r\n            step={\"any\"}\r\n            value={value}\r\n            onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n            placeholder={question.hint || t('yourAnswer')}\r\n            required={question.isRequired}\r\n          />\r\n        );\r\n\r\n      case \"selectone\":\r\n        return (\r\n          <RadioGroup\r\n            value={value}\r\n            onValueChange={(val: string) => handleInputChange(question.id, val)}\r\n            required={question.isRequired}\r\n          >\r\n            <div className=\"space-y-2\">\r\n              {question.questionOptions?.map((option) => (\r\n                <div key={`option-${option.id}`} className=\"flex items-center space-x-2\">\r\n                  <RadioGroupItem\r\n                    value={option.label}\r\n                    id={`option-${question.id}-${option.id}`}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`option-${question.id}-${option.id}`}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    {option.label}\r\n                  </Label>\r\n                  {option.sublabel && (\r\n                    <p className=\"text-sm text-neutral-700 ml-4\">\r\n                      {`(${option.sublabel})`}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </RadioGroup>\r\n        );\r\n\r\n      case \"selectmany\":\r\n        return (\r\n          <div className=\"space-y-2\">\r\n            {question.questionOptions?.map((option) => (\r\n              <div key={option.id} className=\"flex items-center space-x-2\">\r\n                <Checkbox\r\n                  className=\"w-5 h-5 border border-neutral-500\"\r\n                  id={`option-${option.id}`}\r\n                  checked={(value || []).includes(option.label)}\r\n                  onCheckedChange={(checked) => {\r\n                    const currentValues = value || [];\r\n                    const newValues = checked\r\n                      ? [...currentValues, option.label]\r\n                      : currentValues.filter((v: string) => v !== option.label);\r\n                    handleInputChange(question.id, newValues);\r\n                  }}\r\n                />\r\n                <Label\r\n                  htmlFor={`option-${option.id}`}\r\n                  className=\"cursor-pointer\"\r\n                >\r\n                  {option.label} {option.sublabel}\r\n                </Label>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        );\r\n\r\n      case \"date\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <Input\r\n              type=\"date\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.hint || t('selectDate')}\r\n              required={question.isRequired}\r\n            />\r\n            <Calendar className=\"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\r\n          </div>\r\n        );\r\n\r\n      case \"dateandtime\":\r\n        return (\r\n          <div className=\"relative\">\r\n            <Input\r\n              type=\"time\"\r\n              value={value}\r\n              onChange={(e) => handleInputChange(question.id, e.target.value)}\r\n              placeholder={question.hint || t('selectTime')}\r\n              required={question.isRequired}\r\n            />\r\n            <Clock className=\"absolute top-3 right-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\r\n          </div>\r\n        );\r\n\r\n      case \"table\":\r\n        return (\r\n          <TableInput\r\n            questionId={question.id}\r\n            value={value}\r\n            onChange={(cellValues) =>\r\n              handleInputChange(question.id, cellValues)\r\n            }\r\n            required={question.isRequired}\r\n            tableLabel={question.label}\r\n          />\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-neutral-100 dark:bg-neutral-800 rounded-md shadow-sm border border-neutral-500 dark:border-neutral-700\">\r\n      <div className=\"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700\">\r\n        <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\r\n          <ChevronLeft className=\"h-5 w-5\" />\r\n        </Button>\r\n        <h2 className=\"text-lg font-semibold\">{t('formPreview')}</h2>\r\n        <Button\r\n          className=\"cursor-pointer hover:bg-neutral-200\"\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          onClick={onClose}\r\n        >\r\n          <X className=\"h-5 w-5\" />\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"p-4 md:p-6\">\r\n        <div className=\"space-y-6\">\r\n          {/* Render unified form items (groups and individual questions) in order */}\r\n          {unifiedFormItems.map((item) => {\r\n            if (item.type === \"group\") {\r\n              const group = item.data as QuestionGroup;\r\n              const isExpanded = expandedGroups[group.id];\r\n\r\n              // Get questions for this group\r\n              const groupQuestions = questions.filter(q => q.questionGroupId === group.id);\r\n              const visibleGroupQuestions = groupQuestions.filter(gq =>\r\n                visibleQuestions.some(vq => vq.id === gq.id)\r\n              );\r\n\r\n              return (\r\n                <div\r\n                  key={`group-${group.id}`}\r\n                  className=\"border border-neutral-500 dark:border-neutral-600 rounded-lg bg-neutral-100 dark:bg-neutral-800 overflow-hidden\"\r\n                >\r\n                  {/* Group Header */}\r\n                  <div\r\n                    className=\"flex items-center justify-between p-4 border-b border-neutral-500 dark:border-neutral-700 cursor-pointer hover:bg-neutral-200 dark:hover:bg-neutral-700\"\r\n                    onClick={() => toggleGroupExpansion(group.id)}\r\n                  >\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {isExpanded ? (\r\n                        <ChevronDown className=\"h-5 w-5 text-neutral-500\" />\r\n                      ) : (\r\n                        <ChevronRight className=\"h-5 w-5 text-neutral-500\" />\r\n                      )}\r\n                      <h3 className=\"text-lg font-semibold dark:text-neutral-100\">\r\n                        {group.title}\r\n                      </h3>\r\n                      <span className=\"text-sm text-neutral-700 dark:text-neutral-400\">\r\n                        ({visibleGroupQuestions.length} {t('visibleQuestion')}\r\n                        {visibleGroupQuestions.length !== 1 ? t('s') : \"\"})\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Group Content with Nested Questions */}\r\n                  {isExpanded && (\r\n                    <div className=\"p-4 space-y-4\">\r\n                      {nestedQuestions\r\n                        .filter((nq) =>\r\n                          groupQuestions.some(\r\n                            (gq) => gq.id === nq.question.id\r\n                          )\r\n                        )\r\n                        .map((questionGroup) => (\r\n                          <NestedQuestionRenderer\r\n                            key={questionGroup.question.id}\r\n                            questionGroup={questionGroup}\r\n                            renderQuestionInput={renderQuestionInput}\r\n                            errors={errors}\r\n                            className=\"\"\r\n                          />\r\n                        ))}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              );\r\n            } else {\r\n              const question = item.data as Question;\r\n              // Only render ungrouped questions that are visible\r\n              if (!visibleQuestions.some((vq) => vq.id === question.id)) {\r\n                return null;\r\n              }\r\n\r\n              // Find the nested question structure for this question\r\n              const nestedQuestion = nestedQuestions.find(\r\n                (nq) => nq.question.id === question.id\r\n              );\r\n\r\n              if (nestedQuestion) {\r\n                return (\r\n                  <NestedQuestionRenderer\r\n                    key={question.id}\r\n                    questionGroup={nestedQuestion}\r\n                    renderQuestionInput={renderQuestionInput}\r\n                    errors={errors}\r\n                    className=\"\"\r\n                  />\r\n                );\r\n              }\r\n              return renderQuestion(question);\r\n            }\r\n          })}\r\n\r\n          {/* Empty state */}\r\n          {questions.length === 0 && (\r\n            <div className=\"text-center py-12\">\r\n              <p className=\"text-muted-foreground\">\r\n                {t('noFormQuestionsYet')}\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          {questions.length > 0 && visibleQuestions.length === 0 && (\r\n            <div className=\"text-center py-12\">\r\n              <p className=\"text-muted-foreground\">\r\n                {t('noVisibleQuestions')}\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAMA;AACA;AAOA;AACA;AAAA;AACA;AACA;;;AApCA;;;;;;;;;;;;;;;;;AA6CO,SAAS,YAAY,EAC1B,cAAc,SAAS,EACvB,SAAS,EACT,OAAO,EACP,QAAQ,EACS;;IACjB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAUnD,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,CAAC;IAGH,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,aAAa;IACb,MAAM,0BAA0B;QAAC;QAAmB;KAAU;IAC9D,MAAM,oBACJ,gBAAgB,YACZ;QAAC;QAAa;KAAU,GACxB,gBAAgB,aAChB;QAAC;QAAqB;KAAU,GAChC;QAAC;QAA0B;KAAU;IAE3C,sEAAsE;IACtE,MAAM,EAAE,MAAM,eAAe,EAAE,WAAW,iBAAiB,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACjG,UAAU;QACV,OAAO;oCAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBAAE,WAAW;gBAAU;;QAC3D,SAAS,gBAAgB;IAC3B;IAEA,8CAA8C;IAC9C,MAAM,EAAE,MAAM,oBAAoB,EAAE,EAAE,WAAW,iBAAiB,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACpH,UAAU;QACV,OAAO;oCAAE;gBACP,IAAI,gBAAgB,YAAY;oBAC9B,OAAO,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE;wBAAE,YAAY;oBAAU;gBACxD,OAAO,IAAI,gBAAgB,iBAAiB;oBAC1C,OAAO,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;gBACnC;gBACA,OAAO,EAAE;YACX;;QACA,SAAS,gBAAgB;IAC3B;IAEA,oDAAoD;IACpD,MAAM,EAAE,MAAM,yBAAyB,EAAE,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAmB;QACtE,UAAU;YAAC;YAAkB;SAAU;QACvC,OAAO;oCAAE,IAAM,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAE,WAAW;gBAAU;;QAC1D,SAAS,gBAAgB;IAC3B;IAEA,qDAAqD;IACrD,MAAM,YAAwB,6JAAA,CAAA,UAAK,CAAC,OAAO;0CAAC;YAC1C,IAAI,gBAAgB,WAAW;gBAC7B,IAAI,CAAC,iBAAiB,OAAO;oBAC3B,OAAO,EAAE;gBACX;gBAEA,yCAAyC;gBACzC,MAAM,qBAAiC,EAAE;gBAEzC,gBAAgB,KAAK,CAAC,OAAO;sDAAC,CAAC;wBAC7B,IAAI,KAAK,IAAI,KAAK,YAAY;4BAC5B,mBAAmB,IAAI,CAAC;wBAC1B,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,SAAS,EAAE;4BAClD,4BAA4B;4BAC5B,KAAK,SAAS,CAAC,OAAO;kEAAC,CAAC;oCACtB,mBAAmB,IAAI,CAAC;gCAC1B;;wBACF;oBACF;;gBAEA,OAAO;YACT,OAAO;gBACL,mDAAmD;gBACnD,OAAO;YACT;QACF;yCAAG;QAAC;QAAiB;QAAa;KAAkB;IAEpD,gDAAgD;IAChD,MAAM,iBAAkC,6JAAA,CAAA,UAAK,CAAC,OAAO;+CAAC;YACpD,IAAI,gBAAgB,WAAW;gBAC7B,IAAI,CAAC,iBAAiB,OAAO;oBAC3B,OAAO,EAAE;gBACX;gBAEA,OAAO,gBAAgB,KAAK,CACzB,MAAM;2DAAC,CAAC,OAAc,KAAK,IAAI,KAAK;0DACpC,GAAG;2DAAC,CAAC,OAAc,CAAC;4BACnB,GAAG,IAAI;4BACP,UAAU,KAAK,SAAS,IAAI,EAAE;wBAChC,CAAC;;YACL,OAAO;gBACL,yDAAyD;gBACzD,OAAO;YACT;QACF;8CAAG;QAAC;QAAiB;QAAa;KAAuB;IAEzD,qBAAqB;IACrB,MAAM,YAAY,gBAAgB,YAAY,oBAAoB;IAClE,MAAM,UAAU,gBAAgB,YAAY,kBAAkB;IAE9D,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,SAAS;sCAC3C,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC;4BAAG,WAAU;sCAAyB,EAAE;;;;;;sCACzC,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAQ;4BACR,MAAK;4BACL,SAAS;sCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;IAKlD;IAEA,mBAAmB;IACnB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,SAAS;sCAC3C,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC;4BAAG,WAAU;sCAAyB,EAAE;;;;;;sCACzC,6LAAC,8HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAQ;4BACR,MAAK;4BACL,SAAS;sCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAGjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAqB,EAAE;;;;;;0CACpC,6LAAC;gCACC,SAAS;oCACP,IAAI,gBAAgB,WAAW;wCAC7B,YAAY,iBAAiB,CAAC;4CAAE,UAAU;wCAAwB;oCACpE,OAAO;wCACL,YAAY,iBAAiB,CAAC;4CAAE,UAAU;wCAAkB;oCAC9D;gCACF;gCACA,WAAU;0CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;IAMf;IAEA,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,iBAAsC,CAAC;gBAC7C,UAAU,OAAO;6CAAC,CAAC;wBACjB,cAAc,CAAC,SAAS,EAAE,CAAC,GACzB,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG;oBAC/C;;gBACA,WAAW;YACb;QACF;gCAAG;QAAC,UAAU,MAAM;KAAC,GAAG,0DAA0D;IAElF,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,sBAAsB,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;gBAC3D,oBAAoB;gBAEpB,sCAAsC;gBACtC,MAAM,qBAAqB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;gBACzD,mBAAmB;YACrB;QACF;gCAAG;QAAC;QAAW;KAAQ;IAEvB,2EAA2E;IAC3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,iBAAiB,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;gBAClE,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS;gBACrD,MAAM,iBAAiB,OAAO,IAAI,CAAC,gBAAgB,MAAM,KAAK,OAAO,IAAI,CAAC,SAAS,MAAM,IACvF,OAAO,IAAI,CAAC,gBAAgB,IAAI;6CAAC,CAAA,MAAO,cAAc,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;;gBAE9E,IAAI,gBAAgB;oBAClB,WAAW;gBACb;YACF;QACF;gCAAG;QAAC;KAAiB,GAAG,+CAA+C;IAEvE,+BAA+B;IAC/B,MAAM,uBAAuB,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YACzC,OAAO,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;QAC3C;oDAAG;QAAC;QAAgB;KAAU;IAE9B,0BAA0B;IAC1B,MAAM,qBAAqB,6JAAA,CAAA,UAAK,CAAC,OAAO;mDAAC;YACvC,OAAO,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE;QAC/B;kDAAG;QAAC;KAAU;IAEd,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,uBAAuB,CAAA,GAAA,+HAAA,CAAA,gCAA6B,AAAD,EAAE,sBAAsB;YACjF,kBAAkB;QACpB;gCAAG;QAAC;KAAqB;IAEzB,6FAA6F;IAC7F,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;iDAAC;YACrC,2EAA2E;YAC3E,IAAI,gBAAgB,WAAW;gBAC7B,OAAO,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE,sBAAsB;YACtD,OAAO;gBACL,+DAA+D;gBAC/D,OAAO,UAAU,GAAG;6DAAC,CAAC,WAAuB,CAAC;4BAC5C,MAAM;4BACN,MAAM;4BACN,OAAO,SAAS,QAAQ;4BACxB,kBAAkB,SAAS,QAAQ;wBACrC,CAAC;;YACH;QACF;gDAAG;QAAC;QAAsB;QAAoB;QAAW;KAAY;IAErE,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,CAAC,OAAS,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC,YAAoB;QAC7C,WAAW,CAAC,OAAS,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;QACD,UAAU,CAAC,OAAS,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,kCAAkC;QAClC,MAAM,YAAY,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,kBAAkB;QAC7D,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,0CAA0C;IAC1C,MAAM,iBAAiB,CAAC,yBACtB,6LAAC;YAEC,WAAU;;8BAEV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6HAAA,CAAA,QAAK;4BAAC,WAAU;;gCACd,SAAS,KAAK;gCACd,SAAS,UAAU,kBAAI,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;wBAE7D,SAAS,IAAI,kBACZ,6LAAC;4BAAE,WAAU;sCAAsC,SAAS,IAAI;;;;;;wBAEjE,MAAM,CAAC,SAAS,EAAE,CAAC,kBAClB,6LAAC;4BAAE,WAAU;sCAA6B,MAAM,CAAC,SAAS,EAAE,CAAC;;;;;;;;;;;;8BAGjE,6LAAC;oBAAI,WAAU;8BAAQ,oBAAoB;;;;;;;WAftC,SAAS,EAAE;;;;;IAmBpB,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QACJ,OAAO,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,KAAK,eAAe,EAAE,GAAG,EAAE;QAExE,OAAQ,SAAS,SAAS;YACxB,KAAK;gBACH,IAAI,SAAS,IAAI,EAAE,SAAS,cAAc;oBACxC,qBACE,6LAAC,gIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,UAAU,CAAC,IACT,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wBAE/C,aAAa,SAAS,IAAI,IAAI,EAAE;wBAChC,UAAU,SAAS,UAAU;;;;;;gBAGnC;gBACA,qBACE,6LAAC,6HAAA,CAAA,QAAK;oBACJ,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI,EAAE;oBAChC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,6HAAA,CAAA,QAAK;oBACJ,MAAK;oBACL,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI,EAAE;oBAChC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,6HAAA,CAAA,QAAK;oBACJ,MAAK;oBACL,MAAM;oBACN,OAAO;oBACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oBAC9D,aAAa,SAAS,IAAI,IAAI,EAAE;oBAChC,UAAU,SAAS,UAAU;;;;;;YAInC,KAAK;gBACH,qBACE,6LAAC,sIAAA,CAAA,aAAU;oBACT,OAAO;oBACP,eAAe,CAAC,MAAgB,kBAAkB,SAAS,EAAE,EAAE;oBAC/D,UAAU,SAAS,UAAU;8BAE7B,cAAA,6LAAC;wBAAI,WAAU;kCACZ,SAAS,eAAe,EAAE,IAAI,CAAC,uBAC9B,6LAAC;gCAAgC,WAAU;;kDACzC,6LAAC,sIAAA,CAAA,iBAAc;wCACb,OAAO,OAAO,KAAK;wCACnB,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;;;;;;kDAE1C,6LAAC,6HAAA,CAAA,QAAK;wCACJ,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;wCAC7C,WAAU;kDAET,OAAO,KAAK;;;;;;oCAEd,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDACV,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;;;;;;;+BAbnB,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;;;;;;;;;;;;;;;YAsBzC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BACZ,SAAS,eAAe,EAAE,IAAI,CAAC,uBAC9B,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC,gIAAA,CAAA,WAAQ;oCACP,WAAU;oCACV,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCACzB,SAAS,CAAC,SAAS,EAAE,EAAE,QAAQ,CAAC,OAAO,KAAK;oCAC5C,iBAAiB,CAAC;wCAChB,MAAM,gBAAgB,SAAS,EAAE;wCACjC,MAAM,YAAY,UACd;+CAAI;4CAAe,OAAO,KAAK;yCAAC,GAChC,cAAc,MAAM,CAAC,CAAC,IAAc,MAAM,OAAO,KAAK;wCAC1D,kBAAkB,SAAS,EAAE,EAAE;oCACjC;;;;;;8CAEF,6LAAC,6HAAA,CAAA,QAAK;oCACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oCAC9B,WAAU;;wCAET,OAAO,KAAK;wCAAC;wCAAE,OAAO,QAAQ;;;;;;;;2BAjBzB,OAAO,EAAE;;;;;;;;;;YAwB3B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6HAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC9D,aAAa,SAAS,IAAI,IAAI,EAAE;4BAChC,UAAU,SAAS,UAAU;;;;;;sCAE/B,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;YAI1B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6HAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4BAC9D,aAAa,SAAS,IAAI,IAAI,EAAE;4BAChC,UAAU,SAAS,UAAU;;;;;;sCAE/B,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;YAIvB,KAAK;gBACH,qBACE,6LAAC,8IAAA,CAAA,aAAU;oBACT,YAAY,SAAS,EAAE;oBACvB,OAAO;oBACP,UAAU,CAAC,aACT,kBAAkB,SAAS,EAAE,EAAE;oBAEjC,UAAU,SAAS,UAAU;oBAC7B,YAAY,SAAS,KAAK;;;;;;YAIhC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,SAAS;kCAC3C,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,6LAAC;wBAAG,WAAU;kCAAyB,EAAE;;;;;;kCACzC,6LAAC,8HAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAQ;wBACR,MAAK;wBACL,SAAS;kCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,iBAAiB,GAAG,CAAC,CAAC;4BACrB,IAAI,KAAK,IAAI,KAAK,SAAS;gCACzB,MAAM,QAAQ,KAAK,IAAI;gCACvB,MAAM,aAAa,cAAc,CAAC,MAAM,EAAE,CAAC;gCAE3C,+BAA+B;gCAC/B,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,MAAM,EAAE;gCAC3E,MAAM,wBAAwB,eAAe,MAAM,CAAC,CAAA,KAClD,iBAAiB,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,GAAG,EAAE;gCAG7C,qBACE,6LAAC;oCAEC,WAAU;;sDAGV,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,qBAAqB,MAAM,EAAE;sDAE5C,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,2BACC,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEAE1B,6LAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAEd,6LAAC;wDAAK,WAAU;;4DAAiD;4DAC7D,sBAAsB,MAAM;4DAAC;4DAAE,EAAE;4DAClC,sBAAsB,MAAM,KAAK,IAAI,EAAE,OAAO;4DAAG;;;;;;;;;;;;;;;;;;wCAMvD,4BACC,6LAAC;4CAAI,WAAU;sDACZ,gBACE,MAAM,CAAC,CAAC,KACP,eAAe,IAAI,CACjB,CAAC,KAAO,GAAG,EAAE,KAAK,GAAG,QAAQ,CAAC,EAAE,GAGnC,GAAG,CAAC,CAAC,8BACJ,6LAAC,0JAAA,CAAA,UAAsB;oDAErB,eAAe;oDACf,qBAAqB;oDACrB,QAAQ;oDACR,WAAU;mDAJL,cAAc,QAAQ,CAAC,EAAE;;;;;;;;;;;mCAnCnC,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;4BA8C9B,OAAO;gCACL,MAAM,WAAW,KAAK,IAAI;gCAC1B,mDAAmD;gCACnD,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,KAAO,GAAG,EAAE,KAAK,SAAS,EAAE,GAAG;oCACzD,OAAO;gCACT;gCAEA,uDAAuD;gCACvD,MAAM,iBAAiB,gBAAgB,IAAI,CACzC,CAAC,KAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;gCAGxC,IAAI,gBAAgB;oCAClB,qBACE,6LAAC,0JAAA,CAAA,UAAsB;wCAErB,eAAe;wCACf,qBAAqB;wCACrB,QAAQ;wCACR,WAAU;uCAJL,SAAS,EAAE;;;;;gCAOtB;gCACA,OAAO,eAAe;4BACxB;wBACF;wBAGC,UAAU,MAAM,KAAK,mBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;wBAKR,UAAU,MAAM,GAAG,KAAK,iBAAiB,MAAM,KAAK,mBACnD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GAnkBgB;;QAwBJ,yMAAA,CAAA,kBAAe;QACL,yLAAA,CAAA,iBAAc;QAYwD,8KAAA,CAAA,WAAQ;QAOD,8KAAA,CAAA,WAAQ;QAc3D,8KAAA,CAAA,WAAQ;;;KA1DxC", "debugId": null}}, {"offset": {"line": 2507, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-preview/index.ts"], "sourcesContent": ["export { FormPreview } from './form-preview'; "], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-builder/QuestionItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { useSortable } from \"@dnd-kit/sortable\";\r\nimport { CSS } from \"@dnd-kit/utilities\";\r\nimport { Question } from \"@/types/formBuilder\";\r\n\r\nimport { GripVertical, Trash, Pencil, CopyPlus } from \"lucide-react\";\r\nimport { TemplateQuestion } from \"@/types\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface QuestionItemProps {\r\n  question: Question | TemplateQuestion;\r\n  onEdit: () => void;\r\n  onDelete: () => void;\r\n  onDuplicate: () => void;\r\n  isSelected?: boolean;\r\n  onToggleSelect?: () => void;\r\n  selectionMode?: boolean;\r\n}\r\n\r\nconst QuestionItem = ({\r\n  question,\r\n  onEdit,\r\n  onDelete,\r\n  onDuplicate,\r\n  isSelected = false,\r\n  onToggleSelect,\r\n  selectionMode = false,\r\n}: QuestionItemProps) => {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n    isDragging,\r\n  } = useSortable({\r\n    id: question.id,\r\n    data: {\r\n      type: 'question',\r\n      questionId: question.id,\r\n      questionGroupId: 'questionGroupId' in question ? question.questionGroupId : undefined,\r\n    }\r\n  });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n    opacity: isDragging ? 0.5 : 1,\r\n  };\r\n\r\n  const t = useTranslations();\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className={\"border border-neutral-400 rounded-md bg-card shadow-sm\"}\r\n    >\r\n      <div className=\"flex items-center p-4\">\r\n        {selectionMode && (\r\n          <div className=\"mr-2\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={isSelected}\r\n              onChange={() => onToggleSelect && onToggleSelect()}\r\n              className=\"h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        <div\r\n          {...attributes}\r\n          {...listeners}\r\n          className=\"cursor-move mr-3 hover:text-primary\"\r\n        >\r\n          <GripVertical className=\"h-5 w-5\" />\r\n        </div>\r\n\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-xl font-semibold\">\r\n            {question.label || (\r\n              <span className=\"text-muted-foreground italic\">\r\n               {t('emptyQuestion')}\r\n              </span>\r\n            )}\r\n          </h3>\r\n          {question.hint && (\r\n            <p className=\"text-sm sub-text mt-1\">{question.hint}</p>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-1\">\r\n          <button\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDuplicate();\r\n            }}\r\n            title={t('duplicate')}\r\n            className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n          >\r\n            <CopyPlus size={16} />\r\n          </button>\r\n          <button\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onDelete();\r\n            }}\r\n            title={t('delete')}\r\n            className=\"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors\"\r\n          >\r\n            <Trash size={16} />\r\n          </button>\r\n          <button\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              onEdit();\r\n            }}\r\n            title={t('edit')}\r\n            className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n          >\r\n            <Pencil size={16} />\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionItem };\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAGA;AAAA;AAAA;AAAA;AAEA;;;AATA;;;;;AAqBA,MAAM,eAAe,CAAC,EACpB,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,WAAW,EACX,aAAa,KAAK,EAClB,cAAc,EACd,gBAAgB,KAAK,EACH;;IAClB,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QACd,IAAI,SAAS,EAAE;QACf,MAAM;YACJ,MAAM;YACN,YAAY,SAAS,EAAE;YACvB,iBAAiB,qBAAqB,WAAW,SAAS,eAAe,GAAG;QAC9E;IACF;IAEA,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;QACA,SAAS,aAAa,MAAM;IAC9B;IAEA,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW;kBAEX,cAAA,6LAAC;YAAI,WAAU;;gBACZ,+BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,IAAM,kBAAkB;wBAClC,WAAU;;;;;;;;;;;8BAKhB,6LAAC;oBACE,GAAG,UAAU;oBACb,GAAG,SAAS;oBACb,WAAU;8BAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;8BAG1B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,SAAS,KAAK,kBACb,6LAAC;gCAAK,WAAU;0CACd,EAAE;;;;;;;;;;;wBAIP,SAAS,IAAI,kBACZ,6LAAC;4BAAE,WAAU;sCAAyB,SAAS,IAAI;;;;;;;;;;;;8BAIvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,OAAO,EAAE;4BACT,WAAU;sCAEV,cAAA,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;;;;;;sCAElB,6LAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,OAAO,EAAE;4BACT,WAAU;sCAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;;;;;;sCAEf,6LAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,OAAO,EAAE;4BACT,WAAU;sCAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GA3GM;;QAgBA,sKAAA,CAAA,cAAW;QAeL,yMAAA,CAAA,kBAAe;;;KA/BrB", "debugId": null}}, {"offset": {"line": 2731, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-builder/QuestionGroupItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { QuestionItem } from \"./QuestionItem\";\r\nimport { ChevronDown, ChevronRight, Edit, Trash, Plus, GripVertical, FolderPlus } from \"lucide-react\";\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent,\r\n  useDraggable,\r\n  useDroppable,\r\n} from \"@dnd-kit/core\";\r\nimport {\r\n  SortableContext,\r\n  verticalListSortingStrategy,\r\n  arrayMove,\r\n  useSortable,\r\n} from \"@dnd-kit/sortable\";\r\nimport { CSS } from \"@dnd-kit/utilities\";\r\n\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface QuestionGroupItemProps {\r\n  id: number;\r\n  title: string;\r\n  questions: Question[];\r\n  subGroups?: QuestionGroup[];\r\n  parentGroupId?: number;\r\n  nestingLevel?: number;\r\n  onEditGroup: (groupId: number) => void;\r\n  onDeleteGroup: (groupId: number) => void;\r\n  onAddQuestionToGroup: (groupId: number) => void;\r\n  onEditQuestion: (question: Question) => void;\r\n  onDeleteQuestion: (question: Question) => void;\r\n  onDuplicateQuestion: (question: Question) => void;\r\n  onReorderQuestions?: (questionPositions: { id: number; position: number }[]) => void;\r\n  onMoveQuestionBetweenGroups?: (questionId: number, fromGroupId: number | null, toGroupId: number | null) => void;\r\n  onMoveGroupInsideGroup?: (childGroupId: number, parentGroupId: number | null) => void;\r\n  isEditing?: boolean;\r\n  onStartEditing?: (groupId: number, currentName: string) => void;\r\n  onSaveGroupName?: (groupId: number) => void;\r\n  onCancelEditing?: () => void;\r\n  editingName?: string;\r\n  onEditingNameChange?: (name: string) => void;\r\n  selectionMode?: boolean;\r\n  isDraggable?: boolean;\r\n  selectedQuestionIds?: number[];\r\n  onToggleQuestionSelect?: (questionId: number) => void;\r\n  onCreateSubgroup?: (parentGroupId: number, selectedQuestionIds: number[]) => void;\r\n}\r\n\r\nconst QuestionGroupItem = ({\r\n  id,\r\n  title,\r\n  questions,\r\n  subGroups = [],\r\n  parentGroupId,\r\n  nestingLevel = 0,\r\n  onEditGroup, // Kept for future use\r\n  onDeleteGroup,\r\n  onAddQuestionToGroup,\r\n  onEditQuestion,\r\n  onDeleteQuestion,\r\n  onDuplicateQuestion,\r\n  onReorderQuestions,\r\n  onMoveQuestionBetweenGroups,\r\n  onMoveGroupInsideGroup,\r\n  isEditing = false,\r\n  onStartEditing,\r\n  onSaveGroupName,\r\n  onCancelEditing,\r\n  editingName = \"\",\r\n  onEditingNameChange,\r\n  selectionMode = false,\r\n  isDraggable = true,\r\n  selectedQuestionIds = [],\r\n  onToggleQuestionSelect,\r\n  onCreateSubgroup,\r\n}: QuestionGroupItemProps) => {\r\n  const [isExpanded, setIsExpanded] = useState(true);\r\n\r\n  // Get selected questions within this group\r\n  const selectedQuestionsInGroup = questions.filter(q =>\r\n    selectedQuestionIds.includes(q.id)\r\n  );\r\n\r\n  // Handle creating subgroup from selected questions\r\n  const handleCreateSubgroup = () => {\r\n    if (selectedQuestionsInGroup.length > 0 && onCreateSubgroup) {\r\n      onCreateSubgroup(id, selectedQuestionsInGroup.map(q => q.id));\r\n    }\r\n  };\r\n\r\n  // Make the group draggable\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef: setDragRef,\r\n    transform,\r\n    transition,\r\n    isDragging,\r\n  } = useSortable({\r\n    id: `group-${id}`,\r\n    data: {\r\n      type: 'group',\r\n      groupId: id,\r\n      parentGroupId,\r\n    }\r\n  });\r\n\r\n  // Make the group a drop zone\r\n  const { setNodeRef: setDropRef, isOver } = useDroppable({\r\n    id: `group-drop-${id}`,\r\n    data: {\r\n      type: 'group-drop',\r\n      groupId: id,\r\n    }\r\n  });\r\n\r\n  const dragStyle = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n    opacity: isDragging ? 0.5 : 1,\r\n  };\r\n\r\n  const t = useTranslations();\r\n\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor, {\r\n      activationConstraint: {\r\n        distance: 8,\r\n      },\r\n    }),\r\n    useSensor(KeyboardSensor)\r\n  );\r\n\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (!over || active.id === over.id) {\r\n      return;\r\n    }\r\n\r\n    const activeData = active.data.current;\r\n    const overData = over.data.current;\r\n\r\n    // Handle question reordering within the same group\r\n    if (activeData?.type === 'question' && overData?.type === 'question' && onReorderQuestions) {\r\n      // Sort questions by position to maintain order\r\n      const sortedQuestions = [...questions].sort((a, b) => a.position - b.position);\r\n\r\n      const oldIndex = sortedQuestions.findIndex((q) => q.id === active.id);\r\n      const newIndex = sortedQuestions.findIndex((q) => q.id === over.id);\r\n\r\n      if (oldIndex === -1 || newIndex === -1) {\r\n        return;\r\n      }\r\n\r\n      // Reorder the questions array\r\n      const reorderedQuestions = arrayMove(sortedQuestions, oldIndex, newIndex);\r\n\r\n      // Calculate new positions for all questions in this group\r\n      const questionPositions = reorderedQuestions.map((question, index) => ({\r\n        id: Number(question.id), // Ensure it's a number\r\n        position: index + 1, // Start positions from 1\r\n      }));\r\n\r\n      // Call the parent handler to update positions\r\n      onReorderQuestions(questionPositions);\r\n    }\r\n\r\n    // Handle moving questions between groups\r\n    if (activeData?.type === 'question' && overData?.type === 'group-drop' && onMoveQuestionBetweenGroups) {\r\n      const questionId = Number(active.id);\r\n      const fromGroupId = activeData.questionGroupId || null;\r\n      const toGroupId = overData.groupId;\r\n\r\n      if (fromGroupId !== toGroupId) {\r\n        onMoveQuestionBetweenGroups(questionId, fromGroupId, toGroupId);\r\n      }\r\n    }\r\n\r\n    // Handle moving groups inside other groups\r\n    if (activeData?.type === 'group' && overData?.type === 'group-drop' && onMoveGroupInsideGroup) {\r\n      const childGroupId = activeData.groupId;\r\n      const parentGroupId = overData.groupId;\r\n\r\n      // Prevent dropping a group into itself or its descendants\r\n      if (childGroupId !== parentGroupId) {\r\n        onMoveGroupInsideGroup(childGroupId, parentGroupId);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={(node) => {\r\n        setDragRef(node);\r\n        setDropRef(node);\r\n      }}\r\n      style={dragStyle}\r\n      className={`border border-neutral-400 rounded-md bg-card shadow-sm mb-4 ${\r\n        isOver ? 'ring-2 ring-primary-500 ring-opacity-50' : ''\r\n      } ${nestingLevel > 0 ? `ml-8 border-l-4 border-l-primary-300` : ''}`}\r\n    >\r\n      {/* Group Header */}\r\n      <div className=\"flex items-center p-4 bg-neutral-100 border-b border-neutral-300 rounded-t-md\">\r\n        {isDraggable && (\r\n          <div\r\n            {...attributes}\r\n            {...listeners}\r\n            className=\"cursor-move mr-2 hover:text-primary-500 transition-colors\"\r\n            title=\"Drag to reorder group\"\r\n          >\r\n            <GripVertical className=\"h-5 w-5\" />\r\n          </div>\r\n        )}\r\n\r\n        <button\r\n          onClick={() => setIsExpanded(!isExpanded)}\r\n          className=\"mr-2 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n          aria-label={isExpanded ? t(\"collapseGroup\") : t(\"expandGroup\")}\r\n        >\r\n          {isExpanded ? (\r\n            <ChevronDown className=\"h-5 w-5\" />\r\n          ) : (\r\n            <ChevronRight className=\"h-5 w-5\" />\r\n          )}\r\n        </button>\r\n\r\n        {isEditing ? (\r\n          <div className=\"flex-1 mr-4\">\r\n            <input\r\n              type=\"text\"\r\n              value={editingName}\r\n              onChange={(e) =>\r\n                onEditingNameChange && onEditingNameChange(e.target.value)\r\n              }\r\n              className=\"w-full p-2 border border-gray-300 rounded\"\r\n              autoFocus\r\n              onKeyDown={(e) => {\r\n                if (e.key === \"Enter\") {\r\n                  onSaveGroupName && onSaveGroupName(id);\r\n                } else if (e.key === \"Escape\") {\r\n                  onCancelEditing && onCancelEditing();\r\n                }\r\n              }}\r\n              placeholder={t(\"enterGroupName\")}\r\n            />\r\n          </div>\r\n        ) : (\r\n          <h3\r\n            className=\"flex-1 font-medium text-lg cursor-pointer hover:text-primary-500\"\r\n            onClick={() => onStartEditing && onStartEditing(id, title)}\r\n            title={t(\"clickToEditGroupName\")}\r\n          >\r\n            {title}\r\n          </h3>\r\n        )}\r\n\r\n        <div className=\"flex items-center space-x-3\">\r\n          {isEditing ? (\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => onCancelEditing && onCancelEditing()}\r\n                title={t(\"cancelEditing\")}\r\n                className=\"cursor-pointer px-3 py-1 rounded btn-outline\"\r\n              >\r\n                {t(\"cancel\")}\r\n              </button>\r\n              <button\r\n                onClick={() => onSaveGroupName && onSaveGroupName(id)}\r\n                title={t(\"saveGroupName\")}\r\n                className=\"cursor-pointer px-3 py-1 rounded btn-primary\"\r\n              >\r\n                {t(\"save\")}\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              {/* Create Subgroup button - show when questions are selected in this group */}\r\n              {selectionMode && selectedQuestionsInGroup.length > 0 && (\r\n                <button\r\n                  onClick={handleCreateSubgroup}\r\n                  title={`Create Subgroup (${selectedQuestionsInGroup.length} questions)`}\r\n                  className=\"cursor-pointer px-3 py-1 rounded btn-primary text-sm flex items-center gap-1\"\r\n                >\r\n                  <FolderPlus size={14} />\r\n                  Create Subgroup ({selectedQuestionsInGroup.length})\r\n                </button>\r\n              )}\r\n\r\n              <button\r\n                onClick={() => onAddQuestionToGroup(id)}\r\n                title={t(\"addQuestionToGroup\")}\r\n                className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n              >\r\n                <Plus size={16} />\r\n              </button>\r\n              <button\r\n                onClick={() => onStartEditing && onStartEditing(id, title)}\r\n                title={t(\"editGroupName\")}\r\n                className=\"cursor-pointer p-2 rounded-full hover:bg-primary-100 text-neutral-700 hover:text-primary-500 transition-colors\"\r\n              >\r\n                <Edit size={16} />\r\n              </button>\r\n              <button\r\n                onClick={() => onDeleteGroup(id)}\r\n                title={t(\"deleteGroup\")}\r\n                className=\"cursor-pointer p-2 rounded-full hover:bg-destructive/10 text-neutral-700 hover:text-destructive transition-colors\"\r\n              >\r\n                <Trash size={16} />\r\n              </button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Group Content */}\r\n      {isExpanded && (\r\n        <div className=\"p-4 space-y-4\">\r\n          {/* Nested Groups */}\r\n          {subGroups && subGroups.length > 0 && (\r\n            <div className=\"space-y-4\">\r\n              {subGroups\r\n                .sort((a, b) => a.order - b.order)\r\n                .map((subGroup) => (\r\n                  <QuestionGroupItem\r\n                    key={subGroup.id}\r\n                    id={subGroup.id}\r\n                    title={subGroup.title}\r\n                    questions={subGroup.question || []}\r\n                    subGroups={subGroup.subGroups}\r\n                    parentGroupId={id}\r\n                    nestingLevel={nestingLevel + 1}\r\n                    onEditGroup={onEditGroup}\r\n                    onDeleteGroup={onDeleteGroup}\r\n                    onAddQuestionToGroup={onAddQuestionToGroup}\r\n                    onEditQuestion={onEditQuestion}\r\n                    onDeleteQuestion={onDeleteQuestion}\r\n                    onDuplicateQuestion={onDuplicateQuestion}\r\n                    onReorderQuestions={onReorderQuestions}\r\n                    onMoveQuestionBetweenGroups={onMoveQuestionBetweenGroups}\r\n                    onMoveGroupInsideGroup={onMoveGroupInsideGroup}\r\n                    selectionMode={selectionMode}\r\n                    isDraggable={isDraggable}\r\n                    selectedQuestionIds={selectedQuestionIds}\r\n                    onToggleQuestionSelect={onToggleQuestionSelect}\r\n                    onCreateSubgroup={onCreateSubgroup}\r\n                  />\r\n                ))}\r\n            </div>\r\n          )}\r\n\r\n          {/* Questions in this group */}\r\n          {questions.length > 0 ? (\r\n            <DndContext\r\n              sensors={sensors}\r\n              collisionDetection={closestCenter}\r\n              onDragEnd={handleDragEnd}\r\n            >\r\n              <SortableContext\r\n                items={questions.map((question) => question.id)}\r\n                strategy={verticalListSortingStrategy}\r\n              >\r\n                {questions\r\n                  .sort((a, b) => a.position - b.position)\r\n                  .map((question) => (\r\n                    <div key={question.id} className=\"mb-4\">\r\n                      <QuestionItem\r\n                        question={question}\r\n                        onEdit={() => onEditQuestion(question)}\r\n                        onDelete={() => onDeleteQuestion(question)}\r\n                        onDuplicate={() => onDuplicateQuestion(question)}\r\n                        selectionMode={selectionMode}\r\n                        isSelected={selectedQuestionIds.includes(question.id)}\r\n                        onToggleSelect={() => onToggleQuestionSelect && onToggleQuestionSelect(question.id)}\r\n                      />\r\n                    </div>\r\n                  ))}\r\n              </SortableContext>\r\n            </DndContext>\r\n          ) : (\r\n            (!subGroups || subGroups.length === 0) && (\r\n              <div className=\"text-center py-4 text-neutral-500\">\r\n {t(\"noQuestionsInGroup\")}              </div>\r\n            )\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionGroupItem };"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAWA;AAMA;AAEA;;;AAzBA;;;;;;;;AAwDA,MAAM,oBAAoB,CAAC,EACzB,EAAE,EACF,KAAK,EACL,SAAS,EACT,YAAY,EAAE,EACd,aAAa,EACb,eAAe,CAAC,EAChB,WAAW,EACX,aAAa,EACb,oBAAoB,EACpB,cAAc,EACd,gBAAgB,EAChB,mBAAmB,EACnB,kBAAkB,EAClB,2BAA2B,EAC3B,sBAAsB,EACtB,YAAY,KAAK,EACjB,cAAc,EACd,eAAe,EACf,eAAe,EACf,cAAc,EAAE,EAChB,mBAAmB,EACnB,gBAAgB,KAAK,EACrB,cAAc,IAAI,EAClB,sBAAsB,EAAE,EACxB,sBAAsB,EACtB,gBAAgB,EACO;;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,2CAA2C;IAC3C,MAAM,2BAA2B,UAAU,MAAM,CAAC,CAAA,IAChD,oBAAoB,QAAQ,CAAC,EAAE,EAAE;IAGnC,mDAAmD;IACnD,MAAM,uBAAuB;QAC3B,IAAI,yBAAyB,MAAM,GAAG,KAAK,kBAAkB;YAC3D,iBAAiB,IAAI,yBAAyB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAC7D;IACF;IAEA,2BAA2B;IAC3B,MAAM,EACJ,UAAU,EACV,SAAS,EACT,YAAY,UAAU,EACtB,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QACd,IAAI,CAAC,MAAM,EAAE,IAAI;QACjB,MAAM;YACJ,MAAM;YACN,SAAS;YACT;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,EAAE,YAAY,UAAU,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QACtD,IAAI,CAAC,WAAW,EAAE,IAAI;QACtB,MAAM;YACJ,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,YAAY;QAChB,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;QACA,SAAS,aAAa,MAAM;IAC9B;IAEA,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,EAAE;QACvB,sBAAsB;YACpB,UAAU;QACZ;IACF,IACA,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc;IAG1B,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YAClC;QACF;QAEA,MAAM,aAAa,OAAO,IAAI,CAAC,OAAO;QACtC,MAAM,WAAW,KAAK,IAAI,CAAC,OAAO;QAElC,mDAAmD;QACnD,IAAI,YAAY,SAAS,cAAc,UAAU,SAAS,cAAc,oBAAoB;YAC1F,+CAA+C;YAC/C,MAAM,kBAAkB;mBAAI;aAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAE7E,MAAM,WAAW,gBAAgB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE;YACpE,MAAM,WAAW,gBAAgB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;YAElE,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;gBACtC;YACF;YAEA,8BAA8B;YAC9B,MAAM,qBAAqB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,UAAU;YAEhE,0DAA0D;YAC1D,MAAM,oBAAoB,mBAAmB,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;oBACrE,IAAI,OAAO,SAAS,EAAE;oBACtB,UAAU,QAAQ;gBACpB,CAAC;YAED,8CAA8C;YAC9C,mBAAmB;QACrB;QAEA,yCAAyC;QACzC,IAAI,YAAY,SAAS,cAAc,UAAU,SAAS,gBAAgB,6BAA6B;YACrG,MAAM,aAAa,OAAO,OAAO,EAAE;YACnC,MAAM,cAAc,WAAW,eAAe,IAAI;YAClD,MAAM,YAAY,SAAS,OAAO;YAElC,IAAI,gBAAgB,WAAW;gBAC7B,4BAA4B,YAAY,aAAa;YACvD;QACF;QAEA,2CAA2C;QAC3C,IAAI,YAAY,SAAS,WAAW,UAAU,SAAS,gBAAgB,wBAAwB;YAC7F,MAAM,eAAe,WAAW,OAAO;YACvC,MAAM,gBAAgB,SAAS,OAAO;YAEtC,0DAA0D;YAC1D,IAAI,iBAAiB,eAAe;gBAClC,uBAAuB,cAAc;YACvC;QACF;IACF;IAEA,qBACE,6LAAC;QACC,KAAK,CAAC;YACJ,WAAW;YACX,WAAW;QACb;QACA,OAAO;QACP,WAAW,CAAC,4DAA4D,EACtE,SAAS,4CAA4C,GACtD,CAAC,EAAE,eAAe,IAAI,CAAC,oCAAoC,CAAC,GAAG,IAAI;;0BAGpE,6LAAC;gBAAI,WAAU;;oBACZ,6BACC,6LAAC;wBACE,GAAG,UAAU;wBACb,GAAG,SAAS;wBACb,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAI5B,6LAAC;wBACC,SAAS,IAAM,cAAc,CAAC;wBAC9B,WAAU;wBACV,cAAY,aAAa,EAAE,mBAAmB,EAAE;kCAE/C,2BACC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;oBAI3B,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IACT,uBAAuB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BAE3D,WAAU;4BACV,SAAS;4BACT,WAAW,CAAC;gCACV,IAAI,EAAE,GAAG,KAAK,SAAS;oCACrB,mBAAmB,gBAAgB;gCACrC,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;oCAC7B,mBAAmB;gCACrB;4BACF;4BACA,aAAa,EAAE;;;;;;;;;;6CAInB,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB,eAAe,IAAI;wBACpD,OAAO,EAAE;kCAER;;;;;;kCAIL,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,OAAO,EAAE;oCACT,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC;oCACC,SAAS,IAAM,mBAAmB,gBAAgB;oCAClD,OAAO,EAAE;oCACT,WAAU;8CAET,EAAE;;;;;;;;;;;iDAIP;;gCAEG,iBAAiB,yBAAyB,MAAM,GAAG,mBAClD,6LAAC;oCACC,SAAS;oCACT,OAAO,CAAC,iBAAiB,EAAE,yBAAyB,MAAM,CAAC,WAAW,CAAC;oCACvE,WAAU;;sDAEV,6LAAC,qNAAA,CAAA,aAAU;4CAAC,MAAM;;;;;;wCAAM;wCACN,yBAAyB,MAAM;wCAAC;;;;;;;8CAItD,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,OAAO,EAAE;oCACT,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,6LAAC;oCACC,SAAS,IAAM,kBAAkB,eAAe,IAAI;oCACpD,OAAO,EAAE;oCACT,WAAU;8CAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,OAAO,EAAE;oCACT,WAAU;8CAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;YAQtB,4BACC,6LAAC;gBAAI,WAAU;;oBAEZ,aAAa,UAAU,MAAM,GAAG,mBAC/B,6LAAC;wBAAI,WAAU;kCACZ,UACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAC,yBACJ,6LAAC;gCAEC,IAAI,SAAS,EAAE;gCACf,OAAO,SAAS,KAAK;gCACrB,WAAW,SAAS,QAAQ,IAAI,EAAE;gCAClC,WAAW,SAAS,SAAS;gCAC7B,eAAe;gCACf,cAAc,eAAe;gCAC7B,aAAa;gCACb,eAAe;gCACf,sBAAsB;gCACtB,gBAAgB;gCAChB,kBAAkB;gCAClB,qBAAqB;gCACrB,oBAAoB;gCACpB,6BAA6B;gCAC7B,wBAAwB;gCACxB,eAAe;gCACf,aAAa;gCACb,qBAAqB;gCACrB,wBAAwB;gCACxB,kBAAkB;+BApBb,SAAS,EAAE;;;;;;;;;;oBA2BzB,UAAU,MAAM,GAAG,kBAClB,6LAAC,8JAAA,CAAA,aAAU;wBACT,SAAS;wBACT,oBAAoB,8JAAA,CAAA,gBAAa;wBACjC,WAAW;kCAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;4BACd,OAAO,UAAU,GAAG,CAAC,CAAC,WAAa,SAAS,EAAE;4BAC9C,UAAU,sKAAA,CAAA,8BAA2B;sCAEpC,UACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,EACtC,GAAG,CAAC,CAAC,yBACJ,6LAAC;oCAAsB,WAAU;8CAC/B,cAAA,6LAAC,iJAAA,CAAA,eAAY;wCACX,UAAU;wCACV,QAAQ,IAAM,eAAe;wCAC7B,UAAU,IAAM,iBAAiB;wCACjC,aAAa,IAAM,oBAAoB;wCACvC,eAAe;wCACf,YAAY,oBAAoB,QAAQ,CAAC,SAAS,EAAE;wCACpD,gBAAgB,IAAM,0BAA0B,uBAAuB,SAAS,EAAE;;;;;;mCAR5E,SAAS,EAAE;;;;;;;;;;;;;;+BAe7B,CAAC,CAAC,aAAa,UAAU,MAAM,KAAK,CAAC,mBACnC,6LAAC;wBAAI,WAAU;;4BAC3B,EAAE;4BAAsB;;;;;;;;;;;;;;;;;;;AAO1B;GArVM;;QAkDA,sKAAA,CAAA,cAAW;QAU4B,8JAAA,CAAA,eAAY;QAc7C,yMAAA,CAAA,kBAAe;QAET,8JAAA,CAAA,aAAU;;;KA5EtB", "debugId": null}}, {"offset": {"line": 3160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/constants/inputType.ts"], "sourcesContent": ["const inputTypeMapLiteral = {\r\n  text: \"Text\",\r\n  number: \"Number\",\r\n  decimal: \"Decimal\",\r\n  selectone: \"Select one\",\r\n  selectmany: \"Select many\",\r\n  date: \"Date\",\r\n  dateandtime: \"Date and time\",\r\n  table: \"Table\",\r\n} as const;\r\n\r\nexport const InputTypeMap: Record<string, string> = inputTypeMapLiteral;\r\n\r\nexport const InputTypeKeys = Object.keys(inputTypeMapLiteral) as Array<\r\n  keyof typeof inputTypeMapLiteral\r\n>;\r\n\r\nexport type InputType = (typeof InputTypeKeys)[number];\r\n"], "names": [], "mappings": ";;;;AAAA,MAAM,sBAAsB;IAC1B,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;IACX,YAAY;IACZ,MAAM;IACN,aAAa;IACb,OAAO;AACT;AAEO,MAAM,eAAuC;AAE7C,MAAM,gBAAgB,OAAO,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 3185, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-builder/QuestionSelector.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport {\r\n  fetchQuestions,\r\n  fetchTemplateQuestions,\r\n  fetchQuestionBlockQuestions,\r\n} from \"@/lib/api/form-builder\";\r\nimport { ContextType } from \"@/types\";\r\nimport { Question } from \"@/types/formBuilder\";\r\n\r\ninterface QuestionSelectorProps {\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  value?: number | null;\r\n  onChange: (questionId: number | null) => void;\r\n  currentQuestionId?: number; // To exclude current question from options\r\n  placeholder?: string;\r\n}\r\n\r\nconst QuestionSelector: React.FC<QuestionSelectorProps> = ({\r\n  contextType,\r\n  contextId,\r\n  value,\r\n  onChange,\r\n  currentQuestionId,\r\n  placeholder = \"Select next question (optional)\",\r\n}) => {\r\n  // Fetch questions based on context type\r\n  const {\r\n    data: questions = [],\r\n    isLoading,\r\n    error,\r\n  } = useQuery<Question[]>({\r\n    queryKey:\r\n      contextType === \"project\"\r\n        ? [\"questions\", contextId]\r\n        : contextType === \"template\"\r\n        ? [\"templateQuestions\", contextId]\r\n        : [\"questionBlockQuestions\", contextId],\r\n    queryFn: () => {\r\n      if (contextType === \"project\") {\r\n        return fetchQuestions({ projectId: contextId });\r\n      } else if (contextType === \"template\") {\r\n        return fetchTemplateQuestions({ templateId: contextId });\r\n      } else if (contextType === \"questionBlock\") {\r\n        return fetchQuestionBlockQuestions();\r\n      }\r\n      return [];\r\n    },\r\n    enabled: !!contextId,\r\n  });\r\n\r\n\r\n  // Filter out current question to prevent self-reference\r\n  const availableQuestions = questions.filter(\r\n    (q) => q.id !== currentQuestionId\r\n  );\r\n\r\n  const selectedQuestion = availableQuestions.find((q) => q.id === value);\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-1\">\r\n      <select\r\n        value={value || \"\"}\r\n        onChange={(e) => {\r\n          const selectedValue = e.target.value;\r\n          onChange(selectedValue ? parseInt(selectedValue) : null);\r\n        }}\r\n        className=\"input-field text-sm\"\r\n        disabled={isLoading}\r\n      >\r\n        <option value=\"\">\r\n          {isLoading ? \"Loading questions...\" : placeholder}\r\n        </option>\r\n        {availableQuestions.map((question) => (\r\n          <option key={question.id} value={question.id}>\r\n            {question.label || `Question ${question.id}`}\r\n          </option>\r\n        ))}\r\n      </select>\r\n      {selectedQuestion && (\r\n        <div className=\"text-xs text-gray-500 mt-1\">\r\n          Type: {selectedQuestion.inputType}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionSelector };\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;;AAiBA,MAAM,mBAAoD,CAAC,EACzD,WAAW,EACX,SAAS,EACT,KAAK,EACL,QAAQ,EACR,iBAAiB,EACjB,cAAc,iCAAiC,EAChD;;IACC,wCAAwC;IACxC,MAAM,EACJ,MAAM,YAAY,EAAE,EACpB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UACE,gBAAgB,YACZ;YAAC;YAAa;SAAU,GACxB,gBAAgB,aAChB;YAAC;YAAqB;SAAU,GAChC;YAAC;YAA0B;SAAU;QAC3C,OAAO;yCAAE;gBACP,IAAI,gBAAgB,WAAW;oBAC7B,OAAO,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE;wBAAE,WAAW;oBAAU;gBAC/C,OAAO,IAAI,gBAAgB,YAAY;oBACrC,OAAO,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE;wBAAE,YAAY;oBAAU;gBACxD,OAAO,IAAI,gBAAgB,iBAAiB;oBAC1C,OAAO,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;gBACnC;gBACA,OAAO,EAAE;YACX;;QACA,SAAS,CAAC,CAAC;IACb;IAGA,wDAAwD;IACxD,MAAM,qBAAqB,UAAU,MAAM,CACzC,CAAC,IAAM,EAAE,EAAE,KAAK;IAGlB,MAAM,mBAAmB,mBAAmB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IAEjE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,OAAO,SAAS;gBAChB,UAAU,CAAC;oBACT,MAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oBACpC,SAAS,gBAAgB,SAAS,iBAAiB;gBACrD;gBACA,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAO,OAAM;kCACX,YAAY,yBAAyB;;;;;;oBAEvC,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC;4BAAyB,OAAO,SAAS,EAAE;sCACzC,SAAS,KAAK,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;2BADjC,SAAS,EAAE;;;;;;;;;;;YAK3B,kCACC,6LAAC;gBAAI,WAAU;;oBAA6B;oBACnC,iBAAiB,SAAS;;;;;;;;;;;;;AAK3C;GApEM;;QAaA,8KAAA,CAAA,WAAQ;;;KAbR", "debugId": null}}, {"offset": {"line": 3300, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-builder/DynamicOptions.tsx"], "sourcesContent": ["import { Plus, Trash } from \"lucide-react\";\r\nimport { useEffect } from \"react\";\r\nimport { useFieldArray, useFormContext } from \"react-hook-form\";\r\nimport { QuestionSelector } from \"./QuestionSelector\";\r\nimport { ContextType } from \"@/types\";\r\n\r\ninterface DynamicOptionsProps {\r\n  contextType?: ContextType;\r\n  contextId?: number;\r\n  currentQuestionId?: number;\r\n  inputType?: string;\r\n}\r\n\r\nconst DynamicOptions = ({\r\n  contextType,\r\n  contextId,\r\n  currentQuestionId,\r\n  inputType,\r\n}: DynamicOptionsProps) => {\r\n  const {\r\n    control,\r\n    register,\r\n    formState: { errors },\r\n    setValue,\r\n    watch,\r\n  } = useFormContext();\r\n  const { fields, append, remove } = useFieldArray({\r\n    control,\r\n    name: \"questionOptions\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (fields.length === 0) {\r\n      append({ label: \"\", sublabel: \"\", code: \"\", nextQuestionId: null });\r\n    }\r\n  }, [fields, append]);\r\n\r\n  // Check if current input type supports conditional questions\r\n  const supportsConditionalQuestions =\r\n    inputType === \"selectone\" || inputType === \"selectmany\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2\">\r\n      <label className=\"label-text\">Options</label>\r\n      <div className=\"flex flex-col gap-2\">\r\n        {fields.map((field, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"border  border-gray-400 rounded-lg p-3 space-y-2\"\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <input\r\n                {...register(`questionOptions.${index}.label`)}\r\n                placeholder={`Option ${index + 1}`}\r\n                className=\"input-field flex-1 min-w-[150px]\"\r\n              />\r\n\r\n              <input\r\n                {...register(`questionOptions.${index}.sublabel`)}\r\n                placeholder={`Sub Options`}\r\n                className=\"input-field flex-1 min-w-[150px]\"\r\n              />\r\n              <input\r\n                {...register(`questionOptions.${index}.code`)}\r\n                placeholder=\"Code\"\r\n                className=\"w-28 input-field\"\r\n              />\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => remove(index)}\r\n                className=\"p-2 rounded-full hover:bg-red-500/10 text-neutral-700 hover:text-red-500 transition-colors cursor-pointer duration-300\"\r\n              >\r\n                <Trash size={16} />\r\n              </button>\r\n            </div>\r\n\r\n            {/* Conditional Question Selector */}\r\n            {supportsConditionalQuestions && contextType && contextId && (\r\n              <div className=\"ml-2\">\r\n                <label className=\"text-xs text-gray-600 mb-1 block\">\r\n                  Next Question (when this option is selected):\r\n                </label>\r\n                <QuestionSelector\r\n                  contextType={contextType}\r\n                  contextId={contextId}\r\n                  currentQuestionId={currentQuestionId}\r\n                  value={watch(`questionOptions.${index}.nextQuestionId`)}\r\n                  onChange={(questionId) => {\r\n                    setValue(\r\n                      `questionOptions.${index}.nextQuestionId`,\r\n                      questionId\r\n                    );\r\n                  }}\r\n                  placeholder=\"No follow-up question\"\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n\r\n        {errors.questionOptions && (\r\n          <p className=\"text-sm text-red-500\">{`${errors.questionOptions.root?.message}`}</p>\r\n        )}\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => append({ label: \"\", sublabel: \"\", code: \"\", nextQuestionId: null })}\r\n          className=\"btn-outline mt-2 flex items-center justify-center gap-2\"\r\n        >\r\n          <Plus size={16} />\r\n          Add Option\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { DynamicOptions };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAUA,MAAM,iBAAiB,CAAC,EACtB,WAAW,EACX,SAAS,EACT,iBAAiB,EACjB,SAAS,EACW;;IACpB,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACjB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C;QACA,MAAM;IACR;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,OAAO;oBAAE,OAAO;oBAAI,UAAU;oBAAI,MAAM;oBAAI,gBAAgB;gBAAK;YACnE;QACF;mCAAG;QAAC;QAAQ;KAAO;IAEnB,6DAA6D;IAC7D,MAAM,+BACJ,cAAc,eAAe,cAAc;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAM,WAAU;0BAAa;;;;;;0BAC9B,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACE,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,MAAM,CAAC,CAAC;4CAC9C,aAAa,CAAC,OAAO,EAAE,QAAQ,GAAG;4CAClC,WAAU;;;;;;sDAGZ,6LAAC;4CACE,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,SAAS,CAAC,CAAC;4CACjD,aAAa,CAAC,WAAW,CAAC;4CAC1B,WAAU;;;;;;sDAEZ,6LAAC;4CACE,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,KAAK,CAAC,CAAC;4CAC7C,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,OAAO;4CACtB,WAAU;sDAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;gCAKhB,gCAAgC,eAAe,2BAC9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAmC;;;;;;sDAGpD,6LAAC,qJAAA,CAAA,mBAAgB;4CACf,aAAa;4CACb,WAAW;4CACX,mBAAmB;4CACnB,OAAO,MAAM,CAAC,gBAAgB,EAAE,MAAM,eAAe,CAAC;4CACtD,UAAU,CAAC;gDACT,SACE,CAAC,gBAAgB,EAAE,MAAM,eAAe,CAAC,EACzC;4CAEJ;4CACA,aAAY;;;;;;;;;;;;;2BA9Cb;;;;;oBAqDR,OAAO,eAAe,kBACrB,6LAAC;wBAAE,WAAU;kCAAwB,GAAG,OAAO,eAAe,CAAC,IAAI,EAAE,SAAS;;;;;;kCAEhF,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO;gCAAE,OAAO;gCAAI,UAAU;gCAAI,MAAM;gCAAI,gBAAgB;4BAAK;wBAChF,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;;;;;;;AAM5B;GArGM;;QAYA,iKAAA,CAAA,iBAAc;QACiB,iKAAA,CAAA,gBAAa;;;KAb5C", "debugId": null}}, {"offset": {"line": 3509, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/lib/needsOptions.ts"], "sourcesContent": ["export const needsOptions = (inputType: string) => {\r\n  const multipleChoiceInputTypes = [\"selectone\", \"selectmany\"];\r\n  return multipleChoiceInputTypes.includes(inputType);\r\n};\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,eAAe,CAAC;IAC3B,MAAM,2BAA2B;QAAC;QAAa;KAAa;IAC5D,OAAO,yBAAyB,QAAQ,CAAC;AAC3C", "debugId": null}}, {"offset": {"line": 3528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/types/formBuilder.ts"], "sourcesContent": ["import { InputTypeMap, InputType } from \"@/constants/inputType\";\r\nimport { needsOptions } from \"@/lib/needsOptions\";\r\nimport { z } from \"zod\";\r\n\r\nexport type QuestionOption = {\r\n  id: number;\r\n  label: string;\r\n  sublabel: string;\r\n  code: string;\r\n  nextQuestionId?: number | null;\r\n};\r\n\r\nexport type Question = {\r\n  id: number;\r\n  label: string;\r\n  inputType: InputType;\r\n  hint?: string;\r\n  placeholder?: string;\r\n  isRequired: boolean;\r\n  position: number;\r\n  questionOptions: QuestionOption[];\r\n  questionGroupId?: number;\r\n};\r\n\r\nexport type QuestionGroup = {\r\n  id: number;\r\n  title: string;\r\n  order: number;\r\n  projectId: number;\r\n  parentGroupId?: number;\r\n  parentGroup?: QuestionGroup;\r\n  subGroups?: QuestionGroup[];\r\n  createdAt?: string;\r\n  updatedAt?: string;\r\n  question?: Question[];\r\n};\r\n\r\n// schema for validating question\r\nexport const InputTypeKeys = Object.keys(\r\n  InputTypeMap\r\n) as (keyof typeof InputTypeMap)[];\r\n\r\nexport const QuestionSchema = z\r\n  .object({\r\n    label: z.string().min(1, \"Question name is required\"),\r\n    inputType: z.enum(InputTypeKeys as [string, ...string[]]),\r\n    hint: z.string().optional(),\r\n    placeholder: z.string().optional(),\r\n    questionOptions: z\r\n      .array(\r\n        z.object({\r\n          label: z.string(),\r\n          sublabel: z.string().optional(),\r\n          code: z.string(),\r\n          nextQuestionId: z.number().optional().nullable(),\r\n        })\r\n      )\r\n      .optional(),\r\n  })\r\n  .superRefine((data, ctx) => {\r\n    const needsOpts = needsOptions(data.inputType);\r\n\r\n    // Only validate questionOptions if using manual entry (not file upload)\r\n    // This will be checked separately in the form submission\r\n    // if (needsOpts) {\r\n    //   if (!data.questionOptions || data.questionOptions.length === 0) {\r\n    //     ctx.addIssue({\r\n    //       path: [\"questionOptions\"],\r\n    //       code: z.ZodIssueCode.custom,\r\n    //       message: \"At least one option is required for this input type\",\r\n    //     });\r\n    //   } else {\r\n    //     // Validate each option has required fields\r\n    //     data.questionOptions.forEach((option, index) => {\r\n    //       if (!option.label.trim()) {\r\n    //         ctx.addIssue({\r\n    //           path: [\"questionOptions\", index, \"label\"],\r\n    //           code: z.ZodIssueCode.custom,\r\n    //           message: \"Option label is required\",\r\n    //         });\r\n    //       }\r\n    //       if (!option.code.trim()) {\r\n    //         ctx.addIssue({\r\n    //           path: [\"questionOptions\", index, \"code\"],\r\n    //           code: z.ZodIssueCode.custom,\r\n    //           message: \"Option code is required\",\r\n    //         });\r\n    //       }\r\n    //     });\r\n    //   }\r\n    // }\r\n  });\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAoCO,MAAM,gBAAgB,OAAO,IAAI,CACtC,yHAAA,CAAA,eAAY;;AAGP,MAAM,iBAAiB,uIAAA,CAAA,IAAC,CAC5B,MAAM,CAAC;IACN,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,WAAW,uIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAClB,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,iBAAiB,uIAAA,CAAA,IAAC,CACf,KAAK,CACJ,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACP,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM;QACf,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM;QACd,gBAAgB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,IAED,QAAQ;AACb,GACC,WAAW,OAAC,CAAC,MAAM;IAClB,MAAM,YAAY,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,SAAS;AAE7C,wEAAwE;AACxE,yDAAyD;AACzD,mBAAmB;AACnB,sEAAsE;AACtE,qBAAqB;AACrB,mCAAmC;AACnC,qCAAqC;AACrC,wEAAwE;AACxE,UAAU;AACV,aAAa;AACb,kDAAkD;AAClD,wDAAwD;AACxD,oCAAoC;AACpC,yBAAyB;AACzB,uDAAuD;AACvD,yCAAyC;AACzC,iDAAiD;AACjD,cAAc;AACd,UAAU;AACV,mCAAmC;AACnC,yBAAyB;AACzB,sDAAsD;AACtD,yCAAyC;AACzC,gDAAgD;AAChD,cAAc;AACd,UAAU;AACV,UAAU;AACV,MAAM;AACN,IAAI;AACN", "debugId": null}}, {"offset": {"line": 3597, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/general/LoadingOverlay.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Spinner from \"./Spinner\";\r\n\r\nconst LoadingOverlay = () => {\r\n  return (\r\n    <div\r\n      className=\"fixed top-0 left-0 h-screen w-screen bg-neutral-900/20 z-50 flex items-center justify-center\"\r\n      onClick={(e) => {\r\n        e.stopPropagation();\r\n      }}\r\n    >\r\n      <Spinner />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { LoadingOverlay };\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,iBAAiB;IACrB,qBACE,6LAAC;QACC,WAAU;QACV,SAAS,CAAC;YACR,EAAE,eAAe;QACnB;kBAEA,cAAA,6LAAC,oIAAA,CAAA,UAAO;;;;;;;;;;AAGd;KAXM", "debugId": null}}, {"offset": {"line": 3634, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/hooks/use-toast.ts"], "sourcesContent": ["import { useState } from \"react\";\r\n\r\ninterface Toast {\r\n  title: string;\r\n  description?: string;\r\n  variant?: \"default\" | \"destructive\";\r\n}\r\n\r\nexport function useToast() {\r\n  const [toasts, setToasts] = useState<Toast[]>([]);\r\n\r\n  const toast = (toast: Toast) => {\r\n    setToasts((prevToasts) => [...prevToasts, toast]);\r\n    // Remove the toast after 3 seconds\r\n    setTimeout(() => {\r\n      setToasts((prevToasts) => prevToasts.filter((t) => t !== toast));\r\n    }, 3000);\r\n  };\r\n\r\n  return { toast, toasts };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAQO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,QAAQ,CAAC;QACb,UAAU,CAAC,aAAe;mBAAI;gBAAY;aAAM;QAChD,mCAAmC;QACnC,WAAW;YACT,UAAU,CAAC,aAAe,WAAW,MAAM,CAAC,CAAC,IAAM,MAAM;QAC3D,GAAG;IACL;IAEA,OAAO;QAAE;QAAO;IAAO;AACzB;GAZgB", "debugId": null}}, {"offset": {"line": 3668, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-builder/TableQuestionBuilder.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Trash, Plus, Eye, EyeOff } from \"lucide-react\";\r\nimport { createTable, updateTable } from \"@/lib/api/table\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface Column {\r\n  id: string; // Unique identifier for the column in the UI\r\n  columnName: string;\r\n  parentId?: string; // Reference to parent column in the UI (string ID)\r\n  level: number; // Nesting level (0 for top level)\r\n  children?: Column[]; // Child columns\r\n  // Database ID of the parent column for API submission\r\n  // This should be the actual database ID of the parent column\r\n  parentColumnId?: number; // Database ID of the parent column\r\n}\r\n\r\ninterface TableQuestionBuilderProps {\r\n  projectId: number;\r\n  onTableCreated?: (tableId: number) => void;\r\n  isInModal?: boolean; // Flag to indicate if component is used inside a modal\r\n  isEditMode?: boolean; // Flag to indicate if component is used for editing\r\n  existingTableData?: {\r\n    id: number;\r\n    label: string;\r\n    tableColumns: {\r\n      id: number;\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n      childColumns?: {\r\n        id: number;\r\n        columnName: string;\r\n        parentColumnId: number;\r\n      }[];\r\n    }[];\r\n    tableRows: {\r\n      id: number;\r\n      rowsName: string;\r\n    }[];\r\n  };\r\n}\r\n\r\nexport function TableQuestionBuilder({\r\n  projectId,\r\n  onTableCreated,\r\n  isInModal = false,\r\n  isEditMode = false,\r\n  existingTableData,\r\n}: TableQuestionBuilderProps) {\r\n  const { toast } = useToast();\r\n  const [label, setLabel] = useState(existingTableData?.label || \"\");\r\n  const [columns, setColumns] = useState<Column[]>(() => {\r\n    if (existingTableData?.tableColumns) {\r\n      // Convert existing columns to the format needed for the UI\r\n      const formattedColumns: Column[] = [];\r\n\r\n      // Process parent columns first and add them to the formatted array\r\n      existingTableData.tableColumns.forEach((parentCol) => {\r\n        // Create the parent column\r\n        const parentColumn: Column = {\r\n          id: `col-${parentCol.id}`, // Use the actual database ID in the UI ID\r\n          columnName: parentCol.columnName,\r\n          level: 0,\r\n          parentColumnId: undefined,\r\n        };\r\n\r\n        // Add parent column to the formatted array\r\n        formattedColumns.push(parentColumn);\r\n\r\n    \r\n\r\n        // Process child columns if they exist and add them right after the parent\r\n        if (parentCol.childColumns && parentCol.childColumns.length > 0) {\r\n          console.error(\r\n            `Processing ${parentCol.childColumns.length} child columns for parent \"${parentCol.columnName}\"`\r\n          );\r\n\r\n          parentCol.childColumns.forEach((childCol) => {\r\n            const childColumn: Column = {\r\n              id: `col-${childCol.id}`,\r\n              columnName: childCol.columnName,\r\n              level: 1, // Child columns are always level 1\r\n              parentId: `col-${parentCol.id}`, // Reference to parent's UI ID\r\n              parentColumnId: parentCol.id, // Reference to parent's database ID\r\n            };\r\n\r\n            // Add child column to the formatted array\r\n            formattedColumns.push(childColumn);\r\n\r\n           \r\n          });\r\n        }\r\n      });\r\n\r\n\r\n      return formattedColumns.length > 0\r\n        ? formattedColumns\r\n        : [{ id: \"col-1\", columnName: \"\", level: 0 }];\r\n    }\r\n\r\n    return [{ id: \"col-1\", columnName: \"\", level: 0 }];\r\n  });\r\n\r\n  // Initialize rows with a default name if no existing data\r\n  const [rows, setRows] = useState<{ id?: number; rowsName: string }[]>(() => {\r\n    if (\r\n      existingTableData?.tableRows &&\r\n      existingTableData.tableRows.length > 0\r\n    ) {\r\n      // Sort rows by ID to maintain consistent order\r\n      const sortedRows = [...existingTableData.tableRows].sort(\r\n        (a, b) => a.id - b.id\r\n      );\r\n\r\n      return sortedRows.map((row) => ({\r\n        id: row.id,\r\n        rowsName: row.rowsName,\r\n      }));\r\n    }\r\n    // Initialize with a default row name\r\n    return [{ rowsName: \"Row 1\" }];\r\n  });\r\n\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [showPreview, setShowPreview] = useState(true);\r\n\r\n  // Generate a unique ID for new columns\r\n  const generateId = () =>\r\n    `col-${Date.now()}-${Math.floor(Math.random() * 1000)}`;\r\n\r\n  // Add a top-level column\r\n  const handleAddColumn = () => {\r\n    setColumns([\r\n      ...columns,\r\n      {\r\n        id: generateId(),\r\n        columnName: \"\",\r\n        level: 0,\r\n      },\r\n    ]);\r\n  };\r\n\r\n  // Add a child column to a parent column\r\n  const handleAddChildColumn = (parentId: string) => {\r\n    // Find the parent column and its level\r\n    const parentColumn = findColumnById(parentId);\r\n    if (!parentColumn) return;\r\n\r\n    // Check if parent is already a child column (level > 0)\r\n    // If so, we can't add a child to it (would create a 3rd level)\r\n    if (parentColumn.level > 0) {\r\n      toast({\r\n        title: \"Cannot add child column\",\r\n        description:\r\n          \"Cannot create more than 2 levels of nested columns (parent → child → grandchild)\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Count existing children of this parent\r\n    const childCount = columns.filter(\r\n      (col) => col.parentId === parentId\r\n    ).length;\r\n\r\n    // Check if parent already has 2 children\r\n    if (childCount >= 2) {\r\n      toast({\r\n        title: \"Cannot add more child columns\",\r\n        description: `Parent column \"${\r\n          parentColumn.columnName || \"Unnamed\"\r\n        }\" cannot have more than 2 child columns`,\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Extract the parent's database ID from its UI ID\r\n    const parentDbIdMatch = parentId.match(/^col-(\\d+)/);\r\n    const parentDbId = parentDbIdMatch\r\n      ? parseInt(parentDbIdMatch[1], 10)\r\n      : undefined;\r\n\r\n    const newColumn: Column = {\r\n      id: generateId(),\r\n      columnName: \"\",\r\n      parentId: parentId, // This is the UI ID of the parent\r\n      level: parentColumn.level + 1,\r\n      // Store the parent's database ID for API submission\r\n      parentColumnId: parentDbId,\r\n    };\r\n\r\n    // Insert the new column after the parent and its existing children\r\n    const newColumns = [...columns];\r\n    const lastChildIndex = findLastChildIndex(parentId);\r\n\r\n    // If there are no existing children, insert right after the parent\r\n    if (\r\n      lastChildIndex === -1 ||\r\n      lastChildIndex === columns.findIndex((col) => col.id === parentId)\r\n    ) {\r\n      const parentIndex = columns.findIndex((col) => col.id === parentId);\r\n      newColumns.splice(parentIndex + 1, 0, newColumn);\r\n    } else {\r\n      // Otherwise, insert after the last child\r\n      newColumns.splice(lastChildIndex + 1, 0, newColumn);\r\n    }\r\n\r\n    setColumns(newColumns);\r\n  };\r\n\r\n  // Find a column by its ID\r\n  const findColumnById = (id: string): Column | undefined => {\r\n    return columns.find((col) => col.id === id);\r\n  };\r\n\r\n  // Find the index of the last child of a parent column\r\n  const findLastChildIndex = (parentId: string): number => {\r\n    const parentIndex = columns.findIndex((col) => col.id === parentId);\r\n    if (parentIndex === -1) return -1;\r\n\r\n    // Find the last consecutive child\r\n    let lastChildIndex = parentIndex;\r\n    let foundChild = false;\r\n\r\n    for (let i = parentIndex + 1; i < columns.length; i++) {\r\n      if (columns[i].parentId === parentId) {\r\n        // Direct child of this parent\r\n        lastChildIndex = i;\r\n        foundChild = true;\r\n      } else if (isDescendantOf(columns[i], parentId)) {\r\n        // Descendant (could be a grandchild)\r\n        lastChildIndex = i;\r\n        foundChild = true;\r\n      } else {\r\n        // Not a descendant, we've reached the end of this parent's children\r\n        break;\r\n      }\r\n    }\r\n\r\n    // If no children were found, return the parent index\r\n    return foundChild ? lastChildIndex : parentIndex;\r\n  };\r\n\r\n  // Check if a column is a descendant of a parent column\r\n  const isDescendantOf = (column: Column, ancestorId: string): boolean => {\r\n    if (column.parentId === ancestorId) return true;\r\n    if (!column.parentId) return false;\r\n\r\n    const parent = findColumnById(column.parentId);\r\n    if (!parent) return false;\r\n\r\n    return isDescendantOf(parent, ancestorId);\r\n  };\r\n\r\n  const handleAddRow = () => {\r\n    // Add a new row with a default name\r\n    const newRowIndex = rows.length + 1;\r\n    setRows([...rows, { rowsName: `Row ${newRowIndex}` }]);\r\n  };\r\n\r\n  // Remove a column and all its children\r\n  const handleRemoveColumn = (id: string) => {\r\n    if (columns.length <= 1) return;\r\n\r\n    // Find all descendants of this column\r\n    const columnsToRemove = new Set<string>([id]);\r\n    columns.forEach((col) => {\r\n      if (isDescendantOf(col, id)) {\r\n        columnsToRemove.add(col.id);\r\n      }\r\n    });\r\n\r\n    // Filter out the column and its descendants\r\n    const newColumns = columns.filter((col) => !columnsToRemove.has(col.id));\r\n\r\n    // Ensure we always have at least one column\r\n    if (newColumns.length === 0) {\r\n      newColumns.push({\r\n        id: generateId(),\r\n        columnName: \"\",\r\n        level: 0,\r\n      });\r\n    }\r\n\r\n    setColumns(newColumns);\r\n  };\r\n\r\n  const handleRemoveRow = (index: number) => {\r\n    // Allow removing all rows since rows are now optional\r\n    const newRows = [...rows];\r\n    newRows.splice(index, 1);\r\n    setRows(newRows);\r\n  };\r\n\r\n  const handleColumnNameChange = (id: string, value: string) => {\r\n    const newColumns = columns.map((col) => {\r\n      if (col.id === id) {\r\n        return { ...col, columnName: value };\r\n      }\r\n      return col;\r\n    });\r\n    setColumns(newColumns);\r\n  };\r\n\r\n  const handleRowNameChange = (index: number, value: string) => {\r\n    const newRows = [...rows];\r\n    newRows[index] = { ...newRows[index], rowsName: value };\r\n    setRows(newRows);\r\n    \r\n    // Add visual feedback if the row is empty\r\n    const rowInputs = document.querySelectorAll('.row-input');\r\n    if (rowInputs && rowInputs[index]) {\r\n      if (!value.trim()) {\r\n        rowInputs[index].classList.add('border-red-500');\r\n      } else {\r\n        rowInputs[index].classList.remove('border-red-500');\r\n      }\r\n    }\r\n  };\r\n\r\n  // Prepare columns for API submission by converting the hierarchical structure\r\n  const prepareColumnsForSubmission = () => {\r\n  \r\n    \r\n    // Check if we're in edit mode with existing table data\r\n    const isEditing = isEditMode || existingTableData?.id;\r\n    console.log(\"Is editing mode:\", isEditing);\r\n\r\n    // Create a map of UI column IDs to their database IDs\r\n    const columnIdMap = new Map<string, number>();\r\n\r\n    // If we're editing, initialize the map with existing column IDs\r\n    if (isEditing && existingTableData?.tableColumns) {\r\n      existingTableData.tableColumns.forEach((col) => {\r\n        const uiId = `col-${col.id}`;\r\n        columnIdMap.set(uiId, col.id);\r\n      });\r\n    }\r\n\r\n    // Create a map to track parent-child relationships\r\n    const parentChildMap = new Map<string, string[]>();\r\n\r\n    // Build the parent-child relationship map from the current UI state\r\n    columns.forEach((col) => {\r\n      if (col.parentId) {\r\n        if (!parentChildMap.has(col.parentId)) {\r\n          parentChildMap.set(col.parentId, []);\r\n        }\r\n        parentChildMap.get(col.parentId)?.push(col.id);\r\n      }\r\n    });\r\n\r\n    // If editing, try to extract existing database IDs from the column IDs\r\n    if (isEditing && existingTableData?.tableColumns) {\r\n      // Map existing column IDs from the database\r\n      columns.forEach((col) => {\r\n        if (col.columnName.trim()) {\r\n          // Extract the database ID from the UI ID (format: \"col-{id}\")\r\n          const match = col.id.match(/^col-(\\d+)/);\r\n          if (match && match[1]) {\r\n            const dbId = parseInt(match[1], 10);\r\n            // Verify this ID exists in the original data\r\n            const existingColumn = existingTableData.tableColumns.find(\r\n              (ec) => ec.id === dbId\r\n            );\r\n            if (existingColumn) {\r\n              columnIdMap.set(col.id, dbId);\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n\r\n    // For columns without mapped IDs (new columns), assign temporary IDs\r\n    let nextId =\r\n      Math.max(\r\n        ...Array.from(columnIdMap.values(), (id) => id || 0),\r\n        ...(existingTableData?.tableColumns?.map((col) => col.id) || [0]),\r\n        0\r\n      ) + 1;\r\n\r\n    // Assign IDs to columns that don't have them yet\r\n    columns.forEach((col) => {\r\n      if (col.columnName.trim() && !columnIdMap.has(col.id)) {\r\n        columnIdMap.set(col.id, nextId++);\r\n      }\r\n    });\r\n\r\n    // Create API columns array with proper ordering\r\n    const apiColumns: {\r\n      id?: number;\r\n      columnName: string;\r\n      parentColumnId?: number;\r\n    }[] = [];\r\n\r\n    // Create a map to track which UI column ID corresponds to which position in the API columns array\r\n    const apiColumnPositionMap = new Map<string, number>();\r\n\r\n    // Process columns in order of their level (0 = top level, 1 = first child level, etc.)\r\n    // This ensures that parent columns are processed before their children\r\n    // Use 0 as fallback if there are no columns with levels\r\n    const maxLevel = Math.max(...columns.map((col) => col.level || 0), 0);\r\n\r\n    // Process each level, starting from the top (level 0)\r\n    for (let level = 0; level <= maxLevel; level++) {\r\n      // Get all columns at this level with valid names\r\n      const columnsAtLevel = columns.filter(\r\n        (col) => col.level === level && col.columnName.trim()\r\n      );\r\n\r\n      // Process each column at this level\r\n      columnsAtLevel.forEach((col) => {\r\n        const columnId = columnIdMap.get(col.id);\r\n\r\n        // Create the API column object\r\n        const apiColumn: {\r\n          id?: number;\r\n          columnName: string;\r\n          parentColumnId?: number;\r\n        } = {\r\n          columnName: col.columnName.trim(),\r\n        };\r\n\r\n        // If editing and we have a column ID, include it\r\n        if (isEditing && columnId) {\r\n          apiColumn.id = columnId;\r\n        }\r\n\r\n        // If this is a child column (has a parentId), set the parentColumnId\r\n        if (col.parentId) {\r\n          if (isEditing) {\r\n            // For editing, use database IDs for parentColumnId\r\n            const parentColumn = columns.find((c) => c.id === col.parentId);\r\n            const parentDbId = columnIdMap.get(col.parentId);\r\n\r\n            if (parentDbId) {\r\n              // Use the parent's database ID\r\n              apiColumn.parentColumnId = parentDbId;\r\n            } else {\r\n              console.warn(\r\n                `Could not find parent DB ID for column \"${col.columnName}\" (parentId: ${col.parentId})`\r\n              );\r\n              // Don't set a parent ID if we can't find the parent - make it a top-level column\r\n              toast({\r\n                title: \"Warning\",\r\n                description: `Column \"${col.columnName}\" had a missing parent reference and was converted to a top-level column.`,\r\n                variant: \"destructive\",\r\n              });\r\n            }\r\n          } else {\r\n            // For creation, use position-based indices (1-based)\r\n            const parentPosition = apiColumnPositionMap.get(col.parentId);\r\n\r\n            if (parentPosition !== undefined) {\r\n              // Use the parent's position (1-based index)\r\n              apiColumn.parentColumnId = parentPosition + 1; // Convert to 1-based index\r\n            } else {\r\n              console.warn(\r\n                `Could not find parent position for column \"${col.columnName}\" (parentId: ${col.parentId})`\r\n              );\r\n              // Don't set a parent ID if we can't find the parent - make it a top-level column\r\n              toast({\r\n                title: \"Warning\",\r\n                description: `Column \"${col.columnName}\" had a missing parent reference and was converted to a top-level column.`,\r\n                variant: \"destructive\",\r\n              });\r\n            }\r\n          }\r\n        }\r\n\r\n        // Add the column to the API columns array\r\n        const apiColumnIndex = apiColumns.length;\r\n        apiColumns.push(apiColumn);\r\n\r\n        // Store the mapping between UI column ID and its position in the API columns array\r\n        apiColumnPositionMap.set(col.id, apiColumnIndex);\r\n      });\r\n    }\r\n\r\n    // Validate all parent column references before returning\r\n    const invalidParentReferences = apiColumns.filter((col) => {\r\n      if (col.parentColumnId === undefined) {\r\n        return false; // No parent, so it's valid\r\n      }\r\n\r\n      if (isEditing) {\r\n        // For editing, parentColumnId should be a valid database ID\r\n        // It must be a positive number\r\n        if (col.parentColumnId <= 0) {\r\n          return true; // Invalid if not a positive number\r\n        }\r\n      } else {\r\n        // For creation, parentColumnId should be a valid position (1-based index)\r\n        // It must be a positive number and not greater than the number of columns\r\n        if (col.parentColumnId <= 0 || col.parentColumnId > apiColumns.length) {\r\n          return true; // Invalid position\r\n        }\r\n\r\n        // Check if the referenced parent is itself a child column\r\n        const parentColumn = apiColumns[col.parentColumnId - 1]; // Convert to 0-based index\r\n        if (parentColumn && parentColumn.parentColumnId !== undefined) {\r\n          return true; // Invalid: parent is itself a child (would create a 3rd level)\r\n        }\r\n      }\r\n\r\n      return false;\r\n    });\r\n\r\n    if (invalidParentReferences.length > 0) {\r\n      console.error(\r\n        \"Found invalid parent column references:\",\r\n        invalidParentReferences\r\n      );\r\n\r\n      // Fix the invalid references by removing them\r\n      invalidParentReferences.forEach((col) => {\r\n        col.parentColumnId = undefined;\r\n      });\r\n\r\n      toast({\r\n        title: \"Warning\",\r\n        description: `Fixed ${invalidParentReferences.length} invalid column relationships. Some child columns were converted to top-level columns.`,\r\n        variant: \"destructive\",\r\n      });\r\n    }\r\n\r\n    return apiColumns;\r\n  };\r\n\r\n  const handleSubmit = async (e?: React.FormEvent) => {\r\n    \r\n    // Prevent default form submission if event is provided\r\n    if (e) {\r\n      e.preventDefault();\r\n    }\r\n\r\n    // Validate inputs\r\n    if (!label.trim()) {\r\n      console.log(\"Validation failed: Empty label\");\r\n      toast({\r\n        title: \"Error\",\r\n        description: \"Please enter a table label\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    const validColumns = columns.filter((col) => col.columnName.trim());\r\n    const validRows = rows.filter((row) => row.rowsName.trim());\r\n    \r\n \r\n\r\n    // Highlight empty rows\r\n    const rowInputs = document.querySelectorAll('.row-input');\r\n    rows.forEach((row, index) => {\r\n      if (rowInputs && rowInputs[index]) {\r\n        if (!row.rowsName.trim()) {\r\n          rowInputs[index].classList.add('border-red-500');\r\n        } else {\r\n          rowInputs[index].classList.remove('border-red-500');\r\n        }\r\n      }\r\n    });\r\n\r\n    if (validColumns.length === 0) {\r\n      console.log(\"Validation failed: No valid columns\");\r\n      toast({\r\n        title: \"Error\",\r\n        description: \"Please add at least one column with a name\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (validRows.length === 0) {\r\n      console.log(\"Validation failed: No valid rows\");\r\n      toast({\r\n        title: \"Error\",\r\n        description: \"Please add at least one row with a name\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    console.log(\"Validation passed, proceeding with submission\");\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Prepare columns for API submission\r\n      console.log(\"Preparing columns for submission\");\r\n      const apiColumns = prepareColumnsForSubmission();\r\n      console.log(\"Prepared API columns:\", apiColumns);\r\n\r\n      // Prepare rows with trimmed values and proper IDs\r\n      // Sort rows to maintain consistent order\r\n      const sortedValidRows = [...validRows].sort((a, b) => {\r\n        // If both rows have IDs, sort by ID\r\n        if (a.id && b.id) {\r\n          return a.id - b.id;\r\n        }\r\n        // If only one has an ID, put the one with ID first\r\n        if (a.id) return -1;\r\n        if (b.id) return 1;\r\n        // Otherwise, maintain the order they appear in the UI\r\n        return validRows.indexOf(a) - validRows.indexOf(b);\r\n      });\r\n\r\n      const apiRows = sortedValidRows.map((row) => {\r\n        const rowData: { id?: number; rowsName: string } = {\r\n          rowsName: row.rowsName.trim(),\r\n        };\r\n\r\n        // Only include ID if it's a valid existing ID\r\n        if (isEditMode || existingTableData?.id) {\r\n          if (\r\n            row.id &&\r\n            existingTableData?.tableRows?.some((r) => r.id === row.id)\r\n          ) {\r\n            rowData.id = row.id;\r\n          }\r\n        }\r\n\r\n        return rowData;\r\n      });\r\n\r\n      let table;\r\n\r\n      // Check if we're editing an existing table or creating a new one\r\n      if (existingTableData?.id) {\r\n        // Update existing table\r\n        table = await updateTable(\r\n          existingTableData.id,\r\n          label.trim(),\r\n          apiColumns,\r\n          apiRows\r\n        );\r\n\r\n        toast({\r\n          title: \"Success\",\r\n          description: \"Table question updated successfully\",\r\n        });\r\n      } else {\r\n        // Create new table\r\n        table = await createTable(label.trim(), projectId, apiColumns, apiRows);\r\n\r\n        toast({\r\n          title: \"Success\",\r\n          description: \"Table question created successfully\",\r\n        });\r\n\r\n        // Reset form for new table creation\r\n        setLabel(\"\");\r\n        setColumns([\r\n          {\r\n            id: generateId(),\r\n            columnName: \"\",\r\n            level: 0,\r\n          },\r\n        ]);\r\n        setRows([]); // Start with no rows since they're optional\r\n      }\r\n\r\n      // Notify parent component\r\n      if (onTableCreated && table?.id) {\r\n        onTableCreated(table.id);\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"Error with table operation:\", error);\r\n\r\n      // Extract more detailed error message if available\r\n      let errorMessage = existingTableData?.id\r\n        ? \"Failed to update table question\"\r\n        : \"Failed to create table question\";\r\n\r\n      if (error.response?.data?.message) {\r\n        errorMessage = error.response.data.message;\r\n      } else if (error.message) {\r\n        errorMessage = error.message;\r\n      }\r\n\r\n      toast({\r\n        title: \"Error\",\r\n        description: errorMessage,\r\n        variant: \"destructive\",\r\n      });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Create a ref for the table builder component\r\n  const tableBuilderRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  // Add event listener for the custom submitTable event\r\n  useEffect(() => {\r\n    if (isInModal) {\r\n      const handleSubmitEvent = (event: Event) => {\r\n        // Prevent the event from bubbling further if needed\r\n        if (event.cancelable) {\r\n          event.stopPropagation();\r\n        }\r\n\r\n        // Call the submit handler\r\n        handleSubmit();\r\n      };\r\n\r\n      // Add event listener to multiple possible elements\r\n      // 1. Add to the component's root element\r\n      if (tableBuilderRef.current) {\r\n        const tableBuilder = tableBuilderRef.current;\r\n\r\n        tableBuilder.addEventListener(\"submitTable\", handleSubmitEvent);\r\n      }\r\n\r\n      // 2. Add to document for bubbled events\r\n      document.addEventListener(\"submitTable\", handleSubmitEvent);\r\n\r\n      // 3. Add to any existing table-question-builder elements\r\n      const allTableBuilders = document.querySelectorAll(\r\n        \".table-question-builder\"\r\n      );\r\n\r\n      allTableBuilders.forEach((element) => {\r\n        element.addEventListener(\"submitTable\", handleSubmitEvent);\r\n      });\r\n\r\n      return () => {\r\n        // Clean up when component unmounts\r\n\r\n        if (tableBuilderRef.current) {\r\n          tableBuilderRef.current.removeEventListener(\r\n            \"submitTable\",\r\n            handleSubmitEvent\r\n          );\r\n        }\r\n\r\n        document.removeEventListener(\"submitTable\", handleSubmitEvent);\r\n\r\n        allTableBuilders.forEach((element) => {\r\n          element.removeEventListener(\"submitTable\", handleSubmitEvent);\r\n        });\r\n      };\r\n    }\r\n  }, [isInModal, handleSubmit, columns]);\r\n\r\n  return (\r\n    <div\r\n      ref={tableBuilderRef}\r\n      className=\"space-y-6 p-4 border border-gray-200 rounded-md w-full table-question-builder\"\r\n    >\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-lg font-medium\">\r\n          {isEditMode || existingTableData?.id\r\n            ? \"Edit Table Question\"\r\n            : \"Create Table Question\"}\r\n        </h3>\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={() => setShowPreview(!showPreview)}\r\n          className=\"flex items-center gap-1\"\r\n        >\r\n          {showPreview ? (\r\n            <>\r\n              <EyeOff className=\"h-4 w-4\" />\r\n              Hide Preview\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Eye className=\"h-4 w-4\" />\r\n              Show Preview\r\n            </>\r\n          )}\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200\">\r\n        <p className=\"font-medium mb-1\">Table Structure Guidelines:</p>\r\n        <ul className=\"list-disc pl-5 space-y-1\">\r\n          <li>\r\n            Create multiple <span className=\"font-medium\">parent columns</span>{\" \"}\r\n            using the \"Add Top-Level Column\" button\r\n          </li>\r\n          <li>\r\n            Add up to 2 <span className=\"font-medium\">child columns</span> under\r\n            each parent using the \"+\" button\r\n          </li>\r\n          <li>\r\n            Child columns cannot have their own children (maximum 2 levels)\r\n          </li>\r\n        </ul>\r\n      </div>\r\n\r\n      {isInModal ? (\r\n        <div className=\"space-y-4\">\r\n          <div>\r\n            <Label htmlFor=\"table-label\">Table Label</Label>\r\n            <Input\r\n              id=\"table-label\"\r\n              value={label}\r\n              onChange={(e) => setLabel(e.target.value)}\r\n              placeholder=\"Enter table question label\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto\">\r\n            <div className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label>Columns</Label>\r\n                {columns.map((column) => (\r\n                  <div\r\n                    key={column.id}\r\n                    className={cn(\r\n                      \"flex items-center gap-2 p-2 rounded-md\",\r\n                      column.level === 0\r\n                        ? \"bg-gray-50\"\r\n                        : \"bg-white border-l-2 border-gray-300\"\r\n                    )}\r\n                    style={{ marginLeft: `${column.level * 20}px` }}\r\n                  >\r\n                    <div className=\"flex-1 flex items-center gap-2\">\r\n                      {column.level > 0 && (\r\n                        <div className=\"w-4 h-4 flex items-center justify-center\">\r\n                          <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n                        </div>\r\n                      )}\r\n                      <Input\r\n                        value={column.columnName}\r\n                        onChange={(e) =>\r\n                          handleColumnNameChange(column.id, e.target.value)\r\n                        }\r\n                        placeholder={`${\r\n                          column.level === 0 ? \"Parent\" : \"Child\"\r\n                        } Column`}\r\n                        className={cn(\r\n                          \"flex-1\",\r\n                          column.level === 0\r\n                            ? \"border-blue-200 bg-white\"\r\n                            : \"border-dashed\"\r\n                        )}\r\n                      />\r\n                      {column.level === 0 && (\r\n                        <div className=\"text-xs text-blue-500 font-medium\">\r\n                          Parent\r\n                        </div>\r\n                      )}\r\n                      {column.level > 0 && (\r\n                        <div className=\"text-xs text-gray-500 font-medium\">\r\n                          Child\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        onClick={() => handleAddChildColumn(column.id)}\r\n                        title={\r\n                          column.level > 0\r\n                            ? \"Child columns cannot have their own children\"\r\n                            : columns.filter(\r\n                                (col) => col.parentId === column.id\r\n                              ).length >= 2\r\n                            ? \"Maximum 2 child columns allowed\"\r\n                            : \"Add child column\"\r\n                        }\r\n                        disabled={\r\n                          column.level > 0 || // Disable if this is already a child column\r\n                          columns.filter((col) => col.parentId === column.id)\r\n                            .length >= 2 // Disable if already has 2 children\r\n                        }\r\n                      >\r\n                        <Plus\r\n                          className={cn(\r\n                            \"h-4 w-4\",\r\n                            (column.level > 0 ||\r\n                              columns.filter(\r\n                                (col) => col.parentId === column.id\r\n                              ).length >= 2) &&\r\n                              \"text-gray-300\"\r\n                          )}\r\n                        />\r\n                      </Button>\r\n\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        onClick={() => handleRemoveColumn(column.id)}\r\n                        disabled={columns.length <= 1}\r\n                        title=\"Remove column\"\r\n                      >\r\n                        <Trash className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={handleAddColumn}\r\n                  className=\"mt-2\"\r\n                >\r\n                  <Plus className=\"h-4 w-4 mr-2\" />\r\n                  Add Top-Level Column\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label>Rows</Label>\r\n                {rows.map((row, index) => (\r\n                  <div key={index} className=\"flex items-center gap-2\">\r\n                    <Input\r\n                      value={row.rowsName}\r\n                      onChange={(e) =>\r\n                        handleRowNameChange(index, e.target.value)\r\n                      }\r\n                      placeholder={`Row ${index + 1}`}\r\n                      className={`row-input ${!row.rowsName.trim() ? 'border-red-500' : ''}`}\r\n                    />\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      onClick={() => handleRemoveRow(index)}\r\n                      disabled={false}\r\n                    >\r\n                      <Trash className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </div>\r\n                ))}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={handleAddRow}\r\n                  className=\"mt-2\"\r\n                >\r\n                  <Plus className=\"h-4 w-4 mr-2\" />\r\n                  Add Row\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {showPreview && (\r\n              <div className=\"space-y-2\">\r\n                <Label>Table Preview</Label>\r\n                <div className=\"border rounded-md p-4 overflow-x-auto max-h-[300px] overflow-y-auto\">\r\n                  <Table>\r\n                    <TableHeader>\r\n                      {/* Calculate the maximum nesting level */}\r\n                      {(() => {\r\n                        const maxLevel = Math.max(\r\n                          ...columns.map((col) => col.level),\r\n                          0\r\n                        );\r\n\r\n                        // Create header rows for each level\r\n                        const headerRows = [];\r\n\r\n                        // First row with parent columns starting from leftmost position\r\n                        headerRows.push(\r\n                          <TableRow key=\"header-row-0\">\r\n                            {/* Render top-level (parent) columns starting from position 1 */}\r\n                            {columns\r\n                              .filter((col) => col.level === 0)\r\n                              .map((parentCol) => {\r\n                                // Count all descendants at all levels\r\n                                const getAllDescendants = (\r\n                                  colId: string\r\n                                ): Column[] => {\r\n                                  const directChildren = columns.filter(\r\n                                    (c) => c.parentId === colId\r\n                                  );\r\n                                  let allDescendants = [...directChildren];\r\n\r\n                                  directChildren.forEach((child) => {\r\n                                    allDescendants = [\r\n                                      ...allDescendants,\r\n                                      ...getAllDescendants(child.id),\r\n                                    ];\r\n                                  });\r\n\r\n                                  return allDescendants;\r\n                                };\r\n\r\n                                const descendants = getAllDescendants(\r\n                                  parentCol.id\r\n                                );\r\n                                const leafNodes = descendants.filter(\r\n                                  (d) =>\r\n                                    !columns.some((c) => c.parentId === d.id)\r\n                                );\r\n\r\n                                // If no descendants, it's a leaf node itself\r\n                                const colSpanCount =\r\n                                  descendants.length > 0\r\n                                    ? leafNodes.length || 1\r\n                                    : 1;\r\n\r\n                                return (\r\n                                  <TableHead\r\n                                    key={parentCol.id}\r\n                                    colSpan={colSpanCount}\r\n                                    className=\"text-center border-b\"\r\n                                  >\r\n                                    {parentCol.columnName || \"Column\"}\r\n                                  </TableHead>\r\n                                );\r\n                              })}\r\n                          </TableRow>\r\n                        );\r\n\r\n                        // Create header rows for each additional level\r\n                        for (let level = 1; level <= maxLevel; level++) {\r\n                          headerRows.push(\r\n                            <TableRow key={`header-row-${level}`}>\r\n                              {/* For each level, we need to find columns at that level and their parents */}\r\n                              {(() => {\r\n                                const columnsAtLevel = columns.filter(\r\n                                  (col) => col.level === level - 1\r\n                                );\r\n\r\n                                return columnsAtLevel.map((col) => {\r\n                                  // Get direct children of this column\r\n                                  const children = columns.filter(\r\n                                    (c) => c.parentId === col.id\r\n                                  );\r\n\r\n                                  // If no children, render an empty cell to maintain alignment\r\n                                  if (children.length === 0) {\r\n                                    return (\r\n                                      <TableHead\r\n                                        key={`empty-${col.id}`}\r\n                                        className=\"text-center border-b\"\r\n                                      >\r\n                                        {/* Empty cell to maintain column alignment */}\r\n                                      </TableHead>\r\n                                    );\r\n                                  }\r\n\r\n                                  // Render each child with appropriate colspan\r\n                                  return children.map((child) => {\r\n                                    // Calculate how many leaf nodes are under this child\r\n                                    const getAllDescendants = (\r\n                                      colId: string\r\n                                    ): Column[] => {\r\n                                      const directChildren = columns.filter(\r\n                                        (c) => c.parentId === colId\r\n                                      );\r\n                                      let allDescendants = [...directChildren];\r\n\r\n                                      directChildren.forEach((child) => {\r\n                                        allDescendants = [\r\n                                          ...allDescendants,\r\n                                          ...getAllDescendants(child.id),\r\n                                        ];\r\n                                      });\r\n\r\n                                      return allDescendants;\r\n                                    };\r\n\r\n                                    const descendants = getAllDescendants(\r\n                                      child.id\r\n                                    );\r\n                                    const leafNodes = descendants.filter(\r\n                                      (d) =>\r\n                                        !columns.some(\r\n                                          (c) => c.parentId === d.id\r\n                                        )\r\n                                    );\r\n\r\n                                    // If no descendants, it's a leaf node itself\r\n                                    const colSpanCount =\r\n                                      descendants.length > 0\r\n                                        ? leafNodes.length || 1\r\n                                        : 1;\r\n\r\n                                    return (\r\n                                      <TableHead\r\n                                        key={child.id}\r\n                                        colSpan={colSpanCount}\r\n                                        className=\"text-center border-b\"\r\n                                      >\r\n                                        {child.columnName || \"Child Column\"}\r\n                                      </TableHead>\r\n                                    );\r\n                                  });\r\n                                });\r\n                              })()}\r\n                            </TableRow>\r\n                          );\r\n                        }\r\n\r\n                        return headerRows;\r\n                      })()}\r\n                    </TableHeader>\r\n                    <TableBody>\r\n                      {rows.length > 0 ? (\r\n                        rows.map((row, rowIndex) => (\r\n                          <TableRow key={rowIndex}>\r\n                            {/* Render cells for each column starting from leftmost position */}\r\n                            {columns\r\n                              .filter((col) => {\r\n                                // Show cells for top-level columns with no children\r\n                                // or for child columns (level > 0)\r\n                                const hasChildren = columns.some(\r\n                                  (c) => c.parentId === col.id\r\n                                );\r\n                                return (\r\n                                  (col.level === 0 && !hasChildren) ||\r\n                                  col.level > 0\r\n                                );\r\n                              })\r\n                              .map((col) => (\r\n                                <TableCell key={col.id} className=\"bg-gray-50\">\r\n                                  <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\r\n                                    Input field\r\n                                  </div>\r\n                                </TableCell>\r\n                              ))}\r\n                          </TableRow>\r\n                        ))\r\n                      ) : (\r\n                        // When no rows exist, show a single row with input fields under columns\r\n                        <TableRow>\r\n                          {/* Render input cells for each column starting from leftmost position */}\r\n                          {columns\r\n                            .filter((col) => {\r\n                              // Show cells for top-level columns with no children\r\n                              // or for child columns (level > 0)\r\n                              const hasChildren = columns.some(\r\n                                (c) => c.parentId === col.id\r\n                              );\r\n                              return (\r\n                                (col.level === 0 && !hasChildren) ||\r\n                                col.level > 0\r\n                              );\r\n                            })\r\n                            .map((col) => (\r\n                              <TableCell key={col.id} className=\"bg-gray-50\">\r\n                                <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\r\n                                  Input field\r\n                                </div>\r\n                              </TableCell>\r\n                            ))}\r\n                        </TableRow>\r\n                      )}\r\n                    </TableBody>\r\n                  </Table>\r\n                </div>\r\n                <p className=\"text-xs text-gray-500 mt-2\">\r\n                  This preview shows how the table will appear to users filling\r\n                  out the form.\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n          <div className=\"bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200 mb-4\">\r\n            <p className=\"font-medium mb-1\">Table Structure Guidelines:</p>\r\n            <ul className=\"list-disc pl-5 space-y-1\">\r\n              <li>\r\n                Create multiple{\" \"}\r\n                <span className=\"font-medium\">parent columns</span> using the\r\n                \"Add Top-Level Column\" button\r\n              </li>\r\n              <li>\r\n                Add up to 2 <span className=\"font-medium\">child columns</span>{\" \"}\r\n                under each parent using the \"+\" button\r\n              </li>\r\n              <li>\r\n                Child columns cannot have their own children (maximum 2 levels)\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <Label htmlFor=\"table-label\">Table Label</Label>\r\n            <Input\r\n              id=\"table-label\"\r\n              value={label}\r\n              onChange={(e) => setLabel(e.target.value)}\r\n              placeholder=\"Enter table question label\"\r\n              required\r\n            />\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label>Columns</Label>\r\n                {columns.map((column) => (\r\n                  <div\r\n                    key={column.id}\r\n                    className={cn(\r\n                      \"flex items-center gap-2 p-2 rounded-md\",\r\n                      column.level === 0\r\n                        ? \"bg-gray-50\"\r\n                        : \"bg-white border-l-2 border-gray-300\"\r\n                    )}\r\n                    style={{ marginLeft: `${column.level * 20}px` }}\r\n                  >\r\n                    <div className=\"flex-1 flex items-center gap-2\">\r\n                      {column.level > 0 && (\r\n                        <div className=\"w-4 h-4 flex items-center justify-center\">\r\n                          <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n                        </div>\r\n                      )}\r\n                      <Input\r\n                        value={column.columnName}\r\n                        onChange={(e) =>\r\n                          handleColumnNameChange(column.id, e.target.value)\r\n                        }\r\n                        placeholder={`${\r\n                          column.level === 0 ? \"Parent\" : \"Child\"\r\n                        } Column`}\r\n                        className={cn(\r\n                          \"flex-1\",\r\n                          column.level === 0\r\n                            ? \"border-blue-200 bg-white\"\r\n                            : \"border-dashed\"\r\n                        )}\r\n                      />\r\n                      {column.level === 0 && (\r\n                        <div className=\"text-xs text-blue-500 font-medium\">\r\n                          Parent\r\n                        </div>\r\n                      )}\r\n                      {column.level > 0 && (\r\n                        <div className=\"text-xs text-gray-500 font-medium\">\r\n                          Child\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        onClick={() => handleAddChildColumn(column.id)}\r\n                        title={\r\n                          column.level > 0\r\n                            ? \"Child columns cannot have their own children\"\r\n                            : columns.filter(\r\n                                (col) => col.parentId === column.id\r\n                              ).length >= 2\r\n                            ? \"Maximum 2 child columns allowed\"\r\n                            : \"Add child column\"\r\n                        }\r\n                        disabled={\r\n                          column.level > 0 || // Disable if this is already a child column\r\n                          columns.filter((col) => col.parentId === column.id)\r\n                            .length >= 2 // Disable if already has 2 children\r\n                        }\r\n                      >\r\n                        <Plus\r\n                          className={cn(\r\n                            \"h-4 w-4\",\r\n                            (column.level > 0 ||\r\n                              columns.filter(\r\n                                (col) => col.parentId === column.id\r\n                              ).length >= 2) &&\r\n                              \"text-gray-300\"\r\n                          )}\r\n                        />\r\n                      </Button>\r\n\r\n                      <Button\r\n                        type=\"button\"\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        onClick={() => handleRemoveColumn(column.id)}\r\n                        disabled={columns.length <= 1}\r\n                        title=\"Remove column\"\r\n                      >\r\n                        <Trash className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={handleAddColumn}\r\n                  className=\"mt-2\"\r\n                >\r\n                  <Plus className=\"h-4 w-4 mr-2\" />\r\n                  Add Top-Level Column\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label>Rows</Label>\r\n                {rows.map((row, index) => (\r\n                  <div key={index} className=\"flex items-center gap-2\">\r\n                    <Input\r\n                      value={row.rowsName}\r\n                      onChange={(e) =>\r\n                        handleRowNameChange(index, e.target.value)\r\n                      }\r\n                      placeholder={`Row ${index + 1}`}\r\n                      className={`row-input ${!row.rowsName.trim() ? 'border-red-500' : ''}`}\r\n                    />\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      onClick={() => handleRemoveRow(index)}\r\n                      disabled={false}\r\n                    >\r\n                      <Trash className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </div>\r\n                ))}\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={handleAddRow}\r\n                  className=\"mt-2\"\r\n                >\r\n                  <Plus className=\"h-4 w-4 mr-2\" />\r\n                  Add Row\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {showPreview && (\r\n              <div className=\"space-y-2\">\r\n                <Label>Table Preview</Label>\r\n                <div className=\"border rounded-md p-4 overflow-x-auto\">\r\n                  <Table>\r\n                    <TableHeader>\r\n                      {/* Calculate the maximum nesting level */}\r\n                      {(() => {\r\n                        const maxLevel = Math.max(\r\n                          ...columns.map((col) => col.level),\r\n                          0\r\n                        );\r\n\r\n                        // Create header rows for each level\r\n                        const headerRows = [];\r\n\r\n                        // First row with parent columns starting from leftmost position\r\n                        headerRows.push(\r\n                          <TableRow key=\"header-row-0\">\r\n                            {/* Render top-level (parent) columns starting from position 1 */}\r\n                            {columns\r\n                              .filter((col) => col.level === 0)\r\n                              .map((parentCol) => {\r\n                                // Count all descendants at all levels\r\n                                const getAllDescendants = (\r\n                                  colId: string\r\n                                ): Column[] => {\r\n                                  const directChildren = columns.filter(\r\n                                    (c) => c.parentId === colId\r\n                                  );\r\n                                  let allDescendants = [...directChildren];\r\n\r\n                                  directChildren.forEach((child) => {\r\n                                    allDescendants = [\r\n                                      ...allDescendants,\r\n                                      ...getAllDescendants(child.id),\r\n                                    ];\r\n                                  });\r\n\r\n                                  return allDescendants;\r\n                                };\r\n\r\n                                const descendants = getAllDescendants(\r\n                                  parentCol.id\r\n                                );\r\n                                const leafNodes = descendants.filter(\r\n                                  (d) =>\r\n                                    !columns.some((c) => c.parentId === d.id)\r\n                                );\r\n\r\n                                // If no descendants, it's a leaf node itself\r\n                                const colSpanCount =\r\n                                  descendants.length > 0\r\n                                    ? leafNodes.length || 1\r\n                                    : 1;\r\n\r\n                                return (\r\n                                  <TableHead\r\n                                    key={parentCol.id}\r\n                                    colSpan={colSpanCount}\r\n                                    className=\"text-center border-b\"\r\n                                  >\r\n                                    {parentCol.columnName || \"Column\"}\r\n                                  </TableHead>\r\n                                );\r\n                              })}\r\n                          </TableRow>\r\n                        );\r\n\r\n                        // Create header rows for each additional level\r\n                        for (let level = 1; level <= maxLevel; level++) {\r\n                          headerRows.push(\r\n                            <TableRow key={`header-row-${level}`}>\r\n                              {/* For each level, we need to find columns at that level and their parents */}\r\n                              {(() => {\r\n                                const columnsAtLevel = columns.filter(\r\n                                  (col) => col.level === level - 1\r\n                                );\r\n\r\n                                return columnsAtLevel.map((col) => {\r\n                                  // Get direct children of this column\r\n                                  const children = columns.filter(\r\n                                    (c) => c.parentId === col.id\r\n                                  );\r\n\r\n                                  // If no children, render an empty cell to maintain alignment\r\n                                  if (children.length === 0) {\r\n                                    return (\r\n                                      <TableHead\r\n                                        key={`empty-${col.id}`}\r\n                                        className=\"text-center border-b\"\r\n                                      >\r\n                                        {/* Empty cell to maintain column alignment */}\r\n                                      </TableHead>\r\n                                    );\r\n                                  }\r\n\r\n                                  // Render each child with appropriate colspan\r\n                                  return children.map((child) => {\r\n                                    // Calculate how many leaf nodes are under this child\r\n                                    const getAllDescendants = (\r\n                                      colId: string\r\n                                    ): Column[] => {\r\n                                      const directChildren = columns.filter(\r\n                                        (c) => c.parentId === colId\r\n                                      );\r\n                                      let allDescendants = [...directChildren];\r\n\r\n                                      directChildren.forEach((child) => {\r\n                                        allDescendants = [\r\n                                          ...allDescendants,\r\n                                          ...getAllDescendants(child.id),\r\n                                        ];\r\n                                      });\r\n\r\n                                      return allDescendants;\r\n                                    };\r\n\r\n                                    const descendants = getAllDescendants(\r\n                                      child.id\r\n                                    );\r\n                                    const leafNodes = descendants.filter(\r\n                                      (d) =>\r\n                                        !columns.some(\r\n                                          (c) => c.parentId === d.id\r\n                                        )\r\n                                    );\r\n\r\n                                    // If no descendants, it's a leaf node itself\r\n                                    const colSpanCount =\r\n                                      descendants.length > 0\r\n                                        ? leafNodes.length || 1\r\n                                        : 1;\r\n\r\n                                    return (\r\n                                      <TableHead\r\n                                        key={child.id}\r\n                                        colSpan={colSpanCount}\r\n                                        className=\"text-center border-b\"\r\n                                      >\r\n                                        {child.columnName || \"Child Column\"}\r\n                                      </TableHead>\r\n                                    );\r\n                                  });\r\n                                });\r\n                              })()}\r\n                            </TableRow>\r\n                          );\r\n                        }\r\n\r\n                        return headerRows;\r\n                      })()}\r\n                    </TableHeader>\r\n                    <TableBody>\r\n                      {rows.length > 0 ? (\r\n                        rows.map((row, rowIndex) => (\r\n                          <TableRow key={rowIndex}>\r\n                            {/* Render cells for each column starting from leftmost position */}\r\n                            {columns\r\n                              .filter((col) => {\r\n                                // Show cells for top-level columns with no children\r\n                                // or for child columns (level > 0)\r\n                                const hasChildren = columns.some(\r\n                                  (c) => c.parentId === col.id\r\n                                );\r\n                                return (\r\n                                  (col.level === 0 && !hasChildren) ||\r\n                                  col.level > 0\r\n                                );\r\n                              })\r\n                              .map((col) => (\r\n                                <TableCell key={col.id} className=\"bg-gray-50\">\r\n                                  <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\r\n                                    Input field\r\n                                  </div>\r\n                                </TableCell>\r\n                              ))}\r\n                          </TableRow>\r\n                        ))\r\n                      ) : (\r\n                        // When no rows exist, show a single row with input fields under columns\r\n                        <TableRow>\r\n                          {/* Render input cells for each column starting from leftmost position */}\r\n                          {columns\r\n                            .filter((col) => {\r\n                              // Show cells for top-level columns with no children\r\n                              // or for child columns (level > 0)\r\n                              const hasChildren = columns.some(\r\n                                (c) => c.parentId === col.id\r\n                              );\r\n                              return (\r\n                                (col.level === 0 && !hasChildren) ||\r\n                                col.level > 0\r\n                              );\r\n                            })\r\n                            .map((col) => (\r\n                              <TableCell key={col.id} className=\"bg-gray-50\">\r\n                                <div className=\"h-8 flex items-center justify-center text-gray-400 text-xs\">\r\n                                  Input field\r\n                                </div>\r\n                              </TableCell>\r\n                            ))}\r\n                        </TableRow>\r\n                      )}\r\n                    </TableBody>\r\n                  </Table>\r\n                </div>\r\n                <p className=\"text-xs text-gray-500 mt-2\">\r\n                  This preview shows how the table will appear to users filling\r\n                  out the form.\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-end space-x-4 mt-6\">\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              className=\"bg-primary-500 text-white hover:bg-primary-600\"\r\n            >\r\n              {isSubmitting\r\n                ? \"Saving...\"\r\n                : isEditMode || existingTableData?.id\r\n                ? \"Update\"\r\n                : \"Save\"}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAQA;;;AAjBA;;;;;;;;;;AAuDO,SAAS,qBAAqB,EACnC,SAAS,EACT,cAAc,EACd,YAAY,KAAK,EACjB,aAAa,KAAK,EAClB,iBAAiB,EACS;;IAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB,SAAS;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;yCAAY;YAC/C,IAAI,mBAAmB,cAAc;gBACnC,2DAA2D;gBAC3D,MAAM,mBAA6B,EAAE;gBAErC,mEAAmE;gBACnE,kBAAkB,YAAY,CAAC,OAAO;qDAAC,CAAC;wBACtC,2BAA2B;wBAC3B,MAAM,eAAuB;4BAC3B,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;4BACzB,YAAY,UAAU,UAAU;4BAChC,OAAO;4BACP,gBAAgB;wBAClB;wBAEA,2CAA2C;wBAC3C,iBAAiB,IAAI,CAAC;wBAItB,0EAA0E;wBAC1E,IAAI,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,GAAG;4BAC/D,QAAQ,KAAK,CACX,CAAC,WAAW,EAAE,UAAU,YAAY,CAAC,MAAM,CAAC,2BAA2B,EAAE,UAAU,UAAU,CAAC,CAAC,CAAC;4BAGlG,UAAU,YAAY,CAAC,OAAO;iEAAC,CAAC;oCAC9B,MAAM,cAAsB;wCAC1B,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;wCACxB,YAAY,SAAS,UAAU;wCAC/B,OAAO;wCACP,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;wCAC/B,gBAAgB,UAAU,EAAE;oCAC9B;oCAEA,0CAA0C;oCAC1C,iBAAiB,IAAI,CAAC;gCAGxB;;wBACF;oBACF;;gBAGA,OAAO,iBAAiB,MAAM,GAAG,IAC7B,mBACA;oBAAC;wBAAE,IAAI;wBAAS,YAAY;wBAAI,OAAO;oBAAE;iBAAE;YACjD;YAEA,OAAO;gBAAC;oBAAE,IAAI;oBAAS,YAAY;oBAAI,OAAO;gBAAE;aAAE;QACpD;;IAEA,0DAA0D;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;yCAAuC;YACpE,IACE,mBAAmB,aACnB,kBAAkB,SAAS,CAAC,MAAM,GAAG,GACrC;gBACA,+CAA+C;gBAC/C,MAAM,aAAa;uBAAI,kBAAkB,SAAS;iBAAC,CAAC,IAAI;gEACtD,CAAC,GAAG,IAAM,EAAE,EAAE,GAAG,EAAE,EAAE;;gBAGvB,OAAO,WAAW,GAAG;qDAAC,CAAC,MAAQ,CAAC;4BAC9B,IAAI,IAAI,EAAE;4BACV,UAAU,IAAI,QAAQ;wBACxB,CAAC;;YACH;YACA,qCAAqC;YACrC,OAAO;gBAAC;oBAAE,UAAU;gBAAQ;aAAE;QAChC;;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uCAAuC;IACvC,MAAM,aAAa,IACjB,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;IAEzD,yBAAyB;IACzB,MAAM,kBAAkB;QACtB,WAAW;eACN;YACH;gBACE,IAAI;gBACJ,YAAY;gBACZ,OAAO;YACT;SACD;IACH;IAEA,wCAAwC;IACxC,MAAM,uBAAuB,CAAC;QAC5B,uCAAuC;QACvC,MAAM,eAAe,eAAe;QACpC,IAAI,CAAC,cAAc;QAEnB,wDAAwD;QACxD,+DAA+D;QAC/D,IAAI,aAAa,KAAK,GAAG,GAAG;YAC1B,MAAM;gBACJ,OAAO;gBACP,aACE;gBACF,SAAS;YACX;YACA;QACF;QAEA,yCAAyC;QACzC,MAAM,aAAa,QAAQ,MAAM,CAC/B,CAAC,MAAQ,IAAI,QAAQ,KAAK,UAC1B,MAAM;QAER,yCAAyC;QACzC,IAAI,cAAc,GAAG;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,eAAe,EAC3B,aAAa,UAAU,IAAI,UAC5B,uCAAuC,CAAC;gBACzC,SAAS;YACX;YACA;QACF;QAEA,kDAAkD;QAClD,MAAM,kBAAkB,SAAS,KAAK,CAAC;QACvC,MAAM,aAAa,kBACf,SAAS,eAAe,CAAC,EAAE,EAAE,MAC7B;QAEJ,MAAM,YAAoB;YACxB,IAAI;YACJ,YAAY;YACZ,UAAU;YACV,OAAO,aAAa,KAAK,GAAG;YAC5B,oDAAoD;YACpD,gBAAgB;QAClB;QAEA,mEAAmE;QACnE,MAAM,aAAa;eAAI;SAAQ;QAC/B,MAAM,iBAAiB,mBAAmB;QAE1C,mEAAmE;QACnE,IACE,mBAAmB,CAAC,KACpB,mBAAmB,QAAQ,SAAS,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,WACzD;YACA,MAAM,cAAc,QAAQ,SAAS,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;YAC1D,WAAW,MAAM,CAAC,cAAc,GAAG,GAAG;QACxC,OAAO;YACL,yCAAyC;YACzC,WAAW,MAAM,CAAC,iBAAiB,GAAG,GAAG;QAC3C;QAEA,WAAW;IACb;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC;QACtB,OAAO,QAAQ,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;IAC1C;IAEA,sDAAsD;IACtD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,cAAc,QAAQ,SAAS,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;QAC1D,IAAI,gBAAgB,CAAC,GAAG,OAAO,CAAC;QAEhC,kCAAkC;QAClC,IAAI,iBAAiB;QACrB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,cAAc,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrD,IAAI,OAAO,CAAC,EAAE,CAAC,QAAQ,KAAK,UAAU;gBACpC,8BAA8B;gBAC9B,iBAAiB;gBACjB,aAAa;YACf,OAAO,IAAI,eAAe,OAAO,CAAC,EAAE,EAAE,WAAW;gBAC/C,qCAAqC;gBACrC,iBAAiB;gBACjB,aAAa;YACf,OAAO;gBAEL;YACF;QACF;QAEA,qDAAqD;QACrD,OAAO,aAAa,iBAAiB;IACvC;IAEA,uDAAuD;IACvD,MAAM,iBAAiB,CAAC,QAAgB;QACtC,IAAI,OAAO,QAAQ,KAAK,YAAY,OAAO;QAC3C,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO;QAE7B,MAAM,SAAS,eAAe,OAAO,QAAQ;QAC7C,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAO,eAAe,QAAQ;IAChC;IAEA,MAAM,eAAe;QACnB,oCAAoC;QACpC,MAAM,cAAc,KAAK,MAAM,GAAG;QAClC,QAAQ;eAAI;YAAM;gBAAE,UAAU,CAAC,IAAI,EAAE,aAAa;YAAC;SAAE;IACvD;IAEA,uCAAuC;IACvC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,QAAQ,MAAM,IAAI,GAAG;QAEzB,sCAAsC;QACtC,MAAM,kBAAkB,IAAI,IAAY;YAAC;SAAG;QAC5C,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,eAAe,KAAK,KAAK;gBAC3B,gBAAgB,GAAG,CAAC,IAAI,EAAE;YAC5B;QACF;QAEA,4CAA4C;QAC5C,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAC,MAAQ,CAAC,gBAAgB,GAAG,CAAC,IAAI,EAAE;QAEtE,4CAA4C;QAC5C,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,WAAW,IAAI,CAAC;gBACd,IAAI;gBACJ,YAAY;gBACZ,OAAO;YACT;QACF;QAEA,WAAW;IACb;IAEA,MAAM,kBAAkB,CAAC;QACvB,sDAAsD;QACtD,MAAM,UAAU;eAAI;SAAK;QACzB,QAAQ,MAAM,CAAC,OAAO;QACtB,QAAQ;IACV;IAEA,MAAM,yBAAyB,CAAC,IAAY;QAC1C,MAAM,aAAa,QAAQ,GAAG,CAAC,CAAC;YAC9B,IAAI,IAAI,EAAE,KAAK,IAAI;gBACjB,OAAO;oBAAE,GAAG,GAAG;oBAAE,YAAY;gBAAM;YACrC;YACA,OAAO;QACT;QACA,WAAW;IACb;IAEA,MAAM,sBAAsB,CAAC,OAAe;QAC1C,MAAM,UAAU;eAAI;SAAK;QACzB,OAAO,CAAC,MAAM,GAAG;YAAE,GAAG,OAAO,CAAC,MAAM;YAAE,UAAU;QAAM;QACtD,QAAQ;QAER,0CAA0C;QAC1C,MAAM,YAAY,SAAS,gBAAgB,CAAC;QAC5C,IAAI,aAAa,SAAS,CAAC,MAAM,EAAE;YACjC,IAAI,CAAC,MAAM,IAAI,IAAI;gBACjB,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;YACjC,OAAO;gBACL,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACpC;QACF;IACF;IAEA,8EAA8E;IAC9E,MAAM,8BAA8B;QAGlC,uDAAuD;QACvD,MAAM,YAAY,cAAc,mBAAmB;QACnD,QAAQ,GAAG,CAAC,oBAAoB;QAEhC,sDAAsD;QACtD,MAAM,cAAc,IAAI;QAExB,gEAAgE;QAChE,IAAI,aAAa,mBAAmB,cAAc;YAChD,kBAAkB,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtC,MAAM,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;gBAC5B,YAAY,GAAG,CAAC,MAAM,IAAI,EAAE;YAC9B;QACF;QAEA,mDAAmD;QACnD,MAAM,iBAAiB,IAAI;QAE3B,oEAAoE;QACpE,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,IAAI,QAAQ,EAAE;gBAChB,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,QAAQ,GAAG;oBACrC,eAAe,GAAG,CAAC,IAAI,QAAQ,EAAE,EAAE;gBACrC;gBACA,eAAe,GAAG,CAAC,IAAI,QAAQ,GAAG,KAAK,IAAI,EAAE;YAC/C;QACF;QAEA,uEAAuE;QACvE,IAAI,aAAa,mBAAmB,cAAc;YAChD,4CAA4C;YAC5C,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI;oBACzB,8DAA8D;oBAC9D,MAAM,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC;oBAC3B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;wBACrB,MAAM,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE;wBAChC,6CAA6C;wBAC7C,MAAM,iBAAiB,kBAAkB,YAAY,CAAC,IAAI,CACxD,CAAC,KAAO,GAAG,EAAE,KAAK;wBAEpB,IAAI,gBAAgB;4BAClB,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;wBAC1B;oBACF;gBACF;YACF;QACF;QAEA,qEAAqE;QACrE,IAAI,SACF,KAAK,GAAG,IACH,MAAM,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,KAAO,MAAM,OAC9C,mBAAmB,cAAc,IAAI,CAAC,MAAQ,IAAI,EAAE,KAAK;YAAC;SAAE,EAChE,KACE;QAEN,iDAAiD;QACjD,QAAQ,OAAO,CAAC,CAAC;YACf,IAAI,IAAI,UAAU,CAAC,IAAI,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE,GAAG;gBACrD,YAAY,GAAG,CAAC,IAAI,EAAE,EAAE;YAC1B;QACF;QAEA,gDAAgD;QAChD,MAAM,aAIA,EAAE;QAER,kGAAkG;QAClG,MAAM,uBAAuB,IAAI;QAEjC,uFAAuF;QACvF,uEAAuE;QACvE,wDAAwD;QACxD,MAAM,WAAW,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC,MAAQ,IAAI,KAAK,IAAI,IAAI;QAEnE,sDAAsD;QACtD,IAAK,IAAI,QAAQ,GAAG,SAAS,UAAU,QAAS;YAC9C,iDAAiD;YACjD,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,IAAI,KAAK,KAAK,SAAS,IAAI,UAAU,CAAC,IAAI;YAGrD,oCAAoC;YACpC,eAAe,OAAO,CAAC,CAAC;gBACtB,MAAM,WAAW,YAAY,GAAG,CAAC,IAAI,EAAE;gBAEvC,+BAA+B;gBAC/B,MAAM,YAIF;oBACF,YAAY,IAAI,UAAU,CAAC,IAAI;gBACjC;gBAEA,iDAAiD;gBACjD,IAAI,aAAa,UAAU;oBACzB,UAAU,EAAE,GAAG;gBACjB;gBAEA,qEAAqE;gBACrE,IAAI,IAAI,QAAQ,EAAE;oBAChB,IAAI,WAAW;wBACb,mDAAmD;wBACnD,MAAM,eAAe,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,QAAQ;wBAC9D,MAAM,aAAa,YAAY,GAAG,CAAC,IAAI,QAAQ;wBAE/C,IAAI,YAAY;4BACd,+BAA+B;4BAC/B,UAAU,cAAc,GAAG;wBAC7B,OAAO;4BACL,QAAQ,IAAI,CACV,CAAC,wCAAwC,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC;4BAE1F,iFAAiF;4BACjF,MAAM;gCACJ,OAAO;gCACP,aAAa,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,yEAAyE,CAAC;gCACjH,SAAS;4BACX;wBACF;oBACF,OAAO;wBACL,qDAAqD;wBACrD,MAAM,iBAAiB,qBAAqB,GAAG,CAAC,IAAI,QAAQ;wBAE5D,IAAI,mBAAmB,WAAW;4BAChC,4CAA4C;4BAC5C,UAAU,cAAc,GAAG,iBAAiB,GAAG,2BAA2B;wBAC5E,OAAO;4BACL,QAAQ,IAAI,CACV,CAAC,2CAA2C,EAAE,IAAI,UAAU,CAAC,aAAa,EAAE,IAAI,QAAQ,CAAC,CAAC,CAAC;4BAE7F,iFAAiF;4BACjF,MAAM;gCACJ,OAAO;gCACP,aAAa,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,yEAAyE,CAAC;gCACjH,SAAS;4BACX;wBACF;oBACF;gBACF;gBAEA,0CAA0C;gBAC1C,MAAM,iBAAiB,WAAW,MAAM;gBACxC,WAAW,IAAI,CAAC;gBAEhB,mFAAmF;gBACnF,qBAAqB,GAAG,CAAC,IAAI,EAAE,EAAE;YACnC;QACF;QAEA,yDAAyD;QACzD,MAAM,0BAA0B,WAAW,MAAM,CAAC,CAAC;YACjD,IAAI,IAAI,cAAc,KAAK,WAAW;gBACpC,OAAO,OAAO,2BAA2B;YAC3C;YAEA,IAAI,WAAW;gBACb,4DAA4D;gBAC5D,+BAA+B;gBAC/B,IAAI,IAAI,cAAc,IAAI,GAAG;oBAC3B,OAAO,MAAM,mCAAmC;gBAClD;YACF,OAAO;gBACL,0EAA0E;gBAC1E,0EAA0E;gBAC1E,IAAI,IAAI,cAAc,IAAI,KAAK,IAAI,cAAc,GAAG,WAAW,MAAM,EAAE;oBACrE,OAAO,MAAM,mBAAmB;gBAClC;gBAEA,0DAA0D;gBAC1D,MAAM,eAAe,UAAU,CAAC,IAAI,cAAc,GAAG,EAAE,EAAE,2BAA2B;gBACpF,IAAI,gBAAgB,aAAa,cAAc,KAAK,WAAW;oBAC7D,OAAO,MAAM,+DAA+D;gBAC9E;YACF;YAEA,OAAO;QACT;QAEA,IAAI,wBAAwB,MAAM,GAAG,GAAG;YACtC,QAAQ,KAAK,CACX,2CACA;YAGF,8CAA8C;YAC9C,wBAAwB,OAAO,CAAC,CAAC;gBAC/B,IAAI,cAAc,GAAG;YACvB;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa,CAAC,MAAM,EAAE,wBAAwB,MAAM,CAAC,sFAAsF,CAAC;gBAC5I,SAAS;YACX;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAE1B,uDAAuD;QACvD,IAAI,GAAG;YACL,EAAE,cAAc;QAClB;QAEA,kBAAkB;QAClB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,QAAQ,GAAG,CAAC;YACZ,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,MAAQ,IAAI,UAAU,CAAC,IAAI;QAChE,MAAM,YAAY,KAAK,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,CAAC,IAAI;QAIxD,uBAAuB;QACvB,MAAM,YAAY,SAAS,gBAAgB,CAAC;QAC5C,KAAK,OAAO,CAAC,CAAC,KAAK;YACjB,IAAI,aAAa,SAAS,CAAC,MAAM,EAAE;gBACjC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,IAAI;oBACxB,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;gBACjC,OAAO;oBACL,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;gBACpC;YACF;QACF;QAEA,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,QAAQ,GAAG,CAAC;YACZ,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,QAAQ,GAAG,CAAC;YACZ,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,gBAAgB;QAEhB,IAAI;YACF,qCAAqC;YACrC,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa;YACnB,QAAQ,GAAG,CAAC,yBAAyB;YAErC,kDAAkD;YAClD,yCAAyC;YACzC,MAAM,kBAAkB;mBAAI;aAAU,CAAC,IAAI,CAAC,CAAC,GAAG;gBAC9C,oCAAoC;gBACpC,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;oBAChB,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;gBACpB;gBACA,mDAAmD;gBACnD,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC;gBAClB,IAAI,EAAE,EAAE,EAAE,OAAO;gBACjB,sDAAsD;gBACtD,OAAO,UAAU,OAAO,CAAC,KAAK,UAAU,OAAO,CAAC;YAClD;YAEA,MAAM,UAAU,gBAAgB,GAAG,CAAC,CAAC;gBACnC,MAAM,UAA6C;oBACjD,UAAU,IAAI,QAAQ,CAAC,IAAI;gBAC7B;gBAEA,8CAA8C;gBAC9C,IAAI,cAAc,mBAAmB,IAAI;oBACvC,IACE,IAAI,EAAE,IACN,mBAAmB,WAAW,KAAK,CAAC,IAAM,EAAE,EAAE,KAAK,IAAI,EAAE,GACzD;wBACA,QAAQ,EAAE,GAAG,IAAI,EAAE;oBACrB;gBACF;gBAEA,OAAO;YACT;YAEA,IAAI;YAEJ,iEAAiE;YACjE,IAAI,mBAAmB,IAAI;gBACzB,wBAAwB;gBACxB,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EACtB,kBAAkB,EAAE,EACpB,MAAM,IAAI,IACV,YACA;gBAGF,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,mBAAmB;gBACnB,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,MAAM,IAAI,IAAI,WAAW,YAAY;gBAE/D,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBAEA,oCAAoC;gBACpC,SAAS;gBACT,WAAW;oBACT;wBACE,IAAI;wBACJ,YAAY;wBACZ,OAAO;oBACT;iBACD;gBACD,QAAQ,EAAE,GAAG,4CAA4C;YAC3D;YAEA,0BAA0B;YAC1B,IAAI,kBAAkB,OAAO,IAAI;gBAC/B,eAAe,MAAM,EAAE;YACzB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,mDAAmD;YACnD,IAAI,eAAe,mBAAmB,KAClC,oCACA;YAEJ,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,+CAA+C;IAC/C,MAAM,kBAAkB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAErD,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,WAAW;gBACb,MAAM;wEAAoB,CAAC;wBACzB,oDAAoD;wBACpD,IAAI,MAAM,UAAU,EAAE;4BACpB,MAAM,eAAe;wBACvB;wBAEA,0BAA0B;wBAC1B;oBACF;;gBAEA,mDAAmD;gBACnD,yCAAyC;gBACzC,IAAI,gBAAgB,OAAO,EAAE;oBAC3B,MAAM,eAAe,gBAAgB,OAAO;oBAE5C,aAAa,gBAAgB,CAAC,eAAe;gBAC/C;gBAEA,wCAAwC;gBACxC,SAAS,gBAAgB,CAAC,eAAe;gBAEzC,yDAAyD;gBACzD,MAAM,mBAAmB,SAAS,gBAAgB,CAChD;gBAGF,iBAAiB,OAAO;sDAAC,CAAC;wBACxB,QAAQ,gBAAgB,CAAC,eAAe;oBAC1C;;gBAEA;sDAAO;wBACL,mCAAmC;wBAEnC,IAAI,gBAAgB,OAAO,EAAE;4BAC3B,gBAAgB,OAAO,CAAC,mBAAmB,CACzC,eACA;wBAEJ;wBAEA,SAAS,mBAAmB,CAAC,eAAe;wBAE5C,iBAAiB,OAAO;8DAAC,CAAC;gCACxB,QAAQ,mBAAmB,CAAC,eAAe;4BAC7C;;oBACF;;YACF;QACF;yCAAG;QAAC;QAAW;QAAc;KAAQ;IAErC,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX,cAAc,mBAAmB,KAC9B,wBACA;;;;;;kCAEN,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;kCAET,4BACC;;8CACE,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;yDAIhC;;8CACE,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;0BAOnC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;;oCAAG;kDACc,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAsB;oCAAI;;;;;;;0CAG1E,6LAAC;;oCAAG;kDACU,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAoB;;;;;;;0CAGhE,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;YAMP,0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAC7B,6LAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oDAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0CACA,OAAO,KAAK,KAAK,IACb,eACA;oDAEN,OAAO;wDAAE,YAAY,GAAG,OAAO,KAAK,GAAG,GAAG,EAAE,CAAC;oDAAC;;sEAE9C,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAGnB,6LAAC,6HAAA,CAAA,QAAK;oEACJ,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IACT,uBAAuB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oEAElD,aAAa,GACX,OAAO,KAAK,KAAK,IAAI,WAAW,QACjC,OAAO,CAAC;oEACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,UACA,OAAO,KAAK,KAAK,IACb,6BACA;;;;;;gEAGP,OAAO,KAAK,KAAK,mBAChB,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;gEAIpD,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAKvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,OAAO,EAAE;oEAC7C,OACE,OAAO,KAAK,GAAG,IACX,iDACA,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,IACZ,oCACA;oEAEN,UACE,OAAO,KAAK,GAAG,KAAK,4CAA4C;oEAChE,QAAQ,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EAC/C,MAAM,IAAI,EAAE,oCAAoC;;8EAGrD,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,WACA,CAAC,OAAO,KAAK,GAAG,KACd,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,CAAC,KACb;;;;;;;;;;;8EAKR,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,mBAAmB,OAAO,EAAE;oEAC3C,UAAU,QAAQ,MAAM,IAAI;oEAC5B,OAAM;8EAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;;mDAlFhB,OAAO,EAAE;;;;;0DAuFlB,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,6HAAA,CAAA,QAAK;4DACJ,OAAO,IAAI,QAAQ;4DACnB,UAAU,CAAC,IACT,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;4DAE3C,aAAa,CAAC,IAAI,EAAE,QAAQ,GAAG;4DAC/B,WAAW,CAAC,UAAU,EAAE,CAAC,IAAI,QAAQ,CAAC,IAAI,KAAK,mBAAmB,IAAI;;;;;;sEAExE,6LAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,UAAU;sEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;mDAhBX;;;;;0DAoBZ,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAMtC,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;8DACJ,6LAAC,6HAAA,CAAA,cAAW;8DAET,CAAC;wDACA,MAAM,WAAW,KAAK,GAAG,IACpB,QAAQ,GAAG,CAAC,CAAC,MAAQ,IAAI,KAAK,GACjC;wDAGF,oCAAoC;wDACpC,MAAM,aAAa,EAAE;wDAErB,gEAAgE;wDAChE,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC,MAAQ,IAAI,KAAK,KAAK,GAC9B,GAAG,CAAC,CAAC;gEACJ,sCAAsC;gEACtC,MAAM,oBAAoB,CACxB;oEAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;oEAExB,IAAI,iBAAiB;2EAAI;qEAAe;oEAExC,eAAe,OAAO,CAAC,CAAC;wEACtB,iBAAiB;+EACZ;+EACA,kBAAkB,MAAM,EAAE;yEAC9B;oEACH;oEAEA,OAAO;gEACT;gEAEA,MAAM,cAAc,kBAClB,UAAU,EAAE;gEAEd,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;gEAG5C,6CAA6C;gEAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;gEAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;oEAER,SAAS;oEACT,WAAU;8EAET,UAAU,UAAU,IAAI;mEAJpB,UAAU,EAAE;;;;;4DAOvB;2DA/CU;;;;;wDAmDhB,+CAA+C;wDAC/C,IAAK,IAAI,QAAQ,GAAG,SAAS,UAAU,QAAS;4DAC9C,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;0EAEN,CAAC;oEACA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,IAAI,KAAK,KAAK,QAAQ;oEAGjC,OAAO,eAAe,GAAG,CAAC,CAAC;wEACzB,qCAAqC;wEACrC,MAAM,WAAW,QAAQ,MAAM,CAC7B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;wEAG9B,6DAA6D;wEAC7D,IAAI,SAAS,MAAM,KAAK,GAAG;4EACzB,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,WAAU;+EADL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;;;;;wEAM5B;wEAEA,6CAA6C;wEAC7C,OAAO,SAAS,GAAG,CAAC,CAAC;4EACnB,qDAAqD;4EACrD,MAAM,oBAAoB,CACxB;gFAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;gFAExB,IAAI,iBAAiB;uFAAI;iFAAe;gFAExC,eAAe,OAAO,CAAC,CAAC;oFACtB,iBAAiB;2FACZ;2FACA,kBAAkB,MAAM,EAAE;qFAC9B;gFACH;gFAEA,OAAO;4EACT;4EAEA,MAAM,cAAc,kBAClB,MAAM,EAAE;4EAEV,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CACX,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;4EAIhC,6CAA6C;4EAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;4EAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,SAAS;gFACT,WAAU;0FAET,MAAM,UAAU,IAAI;+EAJhB,MAAM,EAAE;;;;;wEAOnB;oEACF;gEACF,CAAC;+DAzEY,CAAC,WAAW,EAAE,OAAO;;;;;wDA4ExC;wDAEA,OAAO;oDACT,CAAC;;;;;;8DAEH,6LAAC,6HAAA,CAAA,YAAS;8DACP,KAAK,MAAM,GAAG,IACb,KAAK,GAAG,CAAC,CAAC,KAAK,yBACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC;gEACP,oDAAoD;gEACpD,mCAAmC;gEACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;gEAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;4DAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;oEAAc,WAAU;8EAChC,cAAA,6LAAC;wEAAI,WAAU;kFAA6D;;;;;;mEAD9D,IAAI,EAAE;;;;;2DAfb;;;;oEAwBjB,wEAAwE;kEACxE,6LAAC,6HAAA,CAAA,WAAQ;kEAEN,QACE,MAAM,CAAC,CAAC;4DACP,oDAAoD;4DACpD,mCAAmC;4DACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;4DAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;wDAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;gEAAc,WAAU;0EAChC,cAAA,6LAAC;oEAAI,WAAU;8EAA6D;;;;;;+DAD9D,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWpC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;qCASlD,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmB;;;;;;0CAChC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;;4CAAG;4CACc;0DAChB,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAqB;;;;;;;kDAGrD,6LAAC;;4CAAG;0DACU,6LAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAqB;4CAAI;;;;;;;kDAGrE,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAMR,6LAAC;;0CACC,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAc;;;;;;0CAC7B,6LAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,QAAQ;;;;;;;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oDAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0CACA,OAAO,KAAK,KAAK,IACb,eACA;oDAEN,OAAO;wDAAE,YAAY,GAAG,OAAO,KAAK,GAAG,GAAG,EAAE,CAAC;oDAAC;;sEAE9C,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAGnB,6LAAC,6HAAA,CAAA,QAAK;oEACJ,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IACT,uBAAuB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;oEAElD,aAAa,GACX,OAAO,KAAK,KAAK,IAAI,WAAW,QACjC,OAAO,CAAC;oEACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,UACA,OAAO,KAAK,KAAK,IACb,6BACA;;;;;;gEAGP,OAAO,KAAK,KAAK,mBAChB,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;gEAIpD,OAAO,KAAK,GAAG,mBACd,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAKvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,OAAO,EAAE;oEAC7C,OACE,OAAO,KAAK,GAAG,IACX,iDACA,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,IACZ,oCACA;oEAEN,UACE,OAAO,KAAK,GAAG,KAAK,4CAA4C;oEAChE,QAAQ,MAAM,CAAC,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EAC/C,MAAM,IAAI,EAAE,oCAAoC;;8EAGrD,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,WACA,CAAC,OAAO,KAAK,GAAG,KACd,QAAQ,MAAM,CACZ,CAAC,MAAQ,IAAI,QAAQ,KAAK,OAAO,EAAE,EACnC,MAAM,IAAI,CAAC,KACb;;;;;;;;;;;8EAKR,6LAAC,8HAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,mBAAmB,OAAO,EAAE;oEAC3C,UAAU,QAAQ,MAAM,IAAI;oEAC5B,OAAM;8EAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;;mDAlFhB,OAAO,EAAE;;;;;0DAuFlB,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;4CACN,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,6HAAA,CAAA,QAAK;4DACJ,OAAO,IAAI,QAAQ;4DACnB,UAAU,CAAC,IACT,oBAAoB,OAAO,EAAE,MAAM,CAAC,KAAK;4DAE3C,aAAa,CAAC,IAAI,EAAE,QAAQ,GAAG;4DAC/B,WAAW,CAAC,UAAU,EAAE,CAAC,IAAI,QAAQ,CAAC,IAAI,KAAK,mBAAmB,IAAI;;;;;;sEAExE,6LAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,gBAAgB;4DAC/B,UAAU;sEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;mDAhBX;;;;;0DAoBZ,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAMtC,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;8DACJ,6LAAC,6HAAA,CAAA,cAAW;8DAET,CAAC;wDACA,MAAM,WAAW,KAAK,GAAG,IACpB,QAAQ,GAAG,CAAC,CAAC,MAAQ,IAAI,KAAK,GACjC;wDAGF,oCAAoC;wDACpC,MAAM,aAAa,EAAE;wDAErB,gEAAgE;wDAChE,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC,MAAQ,IAAI,KAAK,KAAK,GAC9B,GAAG,CAAC,CAAC;gEACJ,sCAAsC;gEACtC,MAAM,oBAAoB,CACxB;oEAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;oEAExB,IAAI,iBAAiB;2EAAI;qEAAe;oEAExC,eAAe,OAAO,CAAC,CAAC;wEACtB,iBAAiB;+EACZ;+EACA,kBAAkB,MAAM,EAAE;yEAC9B;oEACH;oEAEA,OAAO;gEACT;gEAEA,MAAM,cAAc,kBAClB,UAAU,EAAE;gEAEd,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;gEAG5C,6CAA6C;gEAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;gEAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;oEAER,SAAS;oEACT,WAAU;8EAET,UAAU,UAAU,IAAI;mEAJpB,UAAU,EAAE;;;;;4DAOvB;2DA/CU;;;;;wDAmDhB,+CAA+C;wDAC/C,IAAK,IAAI,QAAQ,GAAG,SAAS,UAAU,QAAS;4DAC9C,WAAW,IAAI,eACb,6LAAC,6HAAA,CAAA,WAAQ;0EAEN,CAAC;oEACA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,MAAQ,IAAI,KAAK,KAAK,QAAQ;oEAGjC,OAAO,eAAe,GAAG,CAAC,CAAC;wEACzB,qCAAqC;wEACrC,MAAM,WAAW,QAAQ,MAAM,CAC7B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;wEAG9B,6DAA6D;wEAC7D,IAAI,SAAS,MAAM,KAAK,GAAG;4EACzB,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,WAAU;+EADL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;;;;;wEAM5B;wEAEA,6CAA6C;wEAC7C,OAAO,SAAS,GAAG,CAAC,CAAC;4EACnB,qDAAqD;4EACrD,MAAM,oBAAoB,CACxB;gFAEA,MAAM,iBAAiB,QAAQ,MAAM,CACnC,CAAC,IAAM,EAAE,QAAQ,KAAK;gFAExB,IAAI,iBAAiB;uFAAI;iFAAe;gFAExC,eAAe,OAAO,CAAC,CAAC;oFACtB,iBAAiB;2FACZ;2FACA,kBAAkB,MAAM,EAAE;qFAC9B;gFACH;gFAEA,OAAO;4EACT;4EAEA,MAAM,cAAc,kBAClB,MAAM,EAAE;4EAEV,MAAM,YAAY,YAAY,MAAM,CAClC,CAAC,IACC,CAAC,QAAQ,IAAI,CACX,CAAC,IAAM,EAAE,QAAQ,KAAK,EAAE,EAAE;4EAIhC,6CAA6C;4EAC7C,MAAM,eACJ,YAAY,MAAM,GAAG,IACjB,UAAU,MAAM,IAAI,IACpB;4EAEN,qBACE,6LAAC,6HAAA,CAAA,YAAS;gFAER,SAAS;gFACT,WAAU;0FAET,MAAM,UAAU,IAAI;+EAJhB,MAAM,EAAE;;;;;wEAOnB;oEACF;gEACF,CAAC;+DAzEY,CAAC,WAAW,EAAE,OAAO;;;;;wDA4ExC;wDAEA,OAAO;oDACT,CAAC;;;;;;8DAEH,6LAAC,6HAAA,CAAA,YAAS;8DACP,KAAK,MAAM,GAAG,IACb,KAAK,GAAG,CAAC,CAAC,KAAK,yBACb,6LAAC,6HAAA,CAAA,WAAQ;sEAEN,QACE,MAAM,CAAC,CAAC;gEACP,oDAAoD;gEACpD,mCAAmC;gEACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;gEAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;4DAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;oEAAc,WAAU;8EAChC,cAAA,6LAAC;wEAAI,WAAU;kFAA6D;;;;;;mEAD9D,IAAI,EAAE;;;;;2DAfb;;;;oEAwBjB,wEAAwE;kEACxE,6LAAC,6HAAA,CAAA,WAAQ;kEAEN,QACE,MAAM,CAAC,CAAC;4DACP,oDAAoD;4DACpD,mCAAmC;4DACnC,MAAM,cAAc,QAAQ,IAAI,CAC9B,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,EAAE;4DAE9B,OACE,AAAC,IAAI,KAAK,KAAK,KAAK,CAAC,eACrB,IAAI,KAAK,GAAG;wDAEhB,GACC,GAAG,CAAC,CAAC,oBACJ,6LAAC,6HAAA,CAAA,YAAS;gEAAc,WAAU;0EAChC,cAAA,6LAAC;oEAAI,WAAU;8EAA6D;;;;;;+DAD9D,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWpC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,eACG,cACA,cAAc,mBAAmB,KACjC,WACA;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAt/CgB;;QAOI,wHAAA,CAAA,WAAQ;;;KAPZ", "debugId": null}}, {"offset": {"line": 5480, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/AddQuestionModal.tsx"], "sourcesContent": ["\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { FieldVal<PERSON>, FormProvider, useForm } from \"react-hook-form\";\r\nimport { Select } from \"../general/Select\";\r\nimport { labelToKey } from \"@/lib/labelToKey\";\r\nimport { InputTypeMap } from \"@/constants/inputType\";\r\nimport { Switch } from \"../ui\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { addQuestion } from \"@/lib/api/form-builder\";\r\nimport { DynamicOptions } from \"../form-builder/DynamicOptions\";\r\nimport { z } from \"zod\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { needsOptions } from \"@/lib/needsOptions\";\r\nimport { QuestionSchema } from \"@/types/formBuilder\";\r\nimport { ContextType } from \"@/types\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\nimport { TableQuestionBuilder } from \"../form-builder/TableQuestionBuilder\";\r\nimport {\r\n  BiImport,\r\n  BiUpload,\r\n  BiTrash,\r\n  BiDownload,\r\n  BiCheckCircle,\r\n  BiErrorCircle,\r\n} from \"react-icons/bi\";\r\nimport * as XLSX from \"xlsx\"; // Import XLSX for client-side validation\r\nimport { useTranslations } from \"next-intl\";\r\n\r\n\r\n// File validation utility\r\nconst validateExcelFile = (\r\n  file: File\r\n): Promise<{ isValid: boolean; error?: string }> => {\r\n  return new Promise((resolve) => {\r\n    const validTypes = [\r\n      \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n      \"application/vnd.ms-excel\",\r\n    ];\r\n    const maxSize = 5 * 1024 * 1024; // 5MB\r\n\r\n    if (!validTypes.includes(file.type)) {\r\n      resolve({\r\n        isValid: false,\r\n        error: \"Please select a valid Excel file (.xlsx or .xls)\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (file.size > maxSize) {\r\n      resolve({ isValid: false, error: \"File size must be less than 5MB\" });\r\n      return;\r\n    }\r\n\r\n    // Client-side Excel content validation\r\n    const reader = new FileReader();\r\n    reader.onload = (e) => {\r\n      try {\r\n        const data = new Uint8Array(e.target?.result as ArrayBuffer);\r\n        const workbook = XLSX.read(data, { type: \"array\" });\r\n        const worksheet = workbook.Sheets[workbook.SheetNames[0]];\r\n        const rows = XLSX.utils.sheet_to_json(worksheet, {\r\n          header: 1,\r\n        }) as any[][];\r\n\r\n        if (rows.length < 2) {\r\n          resolve({\r\n            isValid: false,\r\n            error: \"Excel file is empty or has no valid data\",\r\n          });\r\n          return;\r\n        }\r\n\r\n        const headers = rows[0].map((h: any) => h?.toString().trim());\r\n        const expectedHeaders = [\"label\", \"code\", \"Next Question ID\"];\r\n        if (!headers[0]?.includes(\"label\") || !headers[1]?.includes(\"code\")) {\r\n          resolve({\r\n            isValid: false,\r\n            error:\r\n              \"Invalid Excel format: Missing required headers (Label, Code)\",\r\n          });\r\n          return;\r\n        }\r\n\r\n        const dataRows = rows.slice(1);\r\n        if (dataRows.length === 0) {\r\n          resolve({\r\n            isValid: false,\r\n            error: \"Excel file contains no valid options\",\r\n          });\r\n          return;\r\n        }\r\n\r\n        for (let i = 0; i < dataRows.length; i++) {\r\n          const row = dataRows[i];\r\n          if (!row[0] || !row[1]) {\r\n            resolve({\r\n              isValid: false,\r\n              error: `Invalid data in row ${\r\n                i + 2\r\n              }: Label and Code are required`,\r\n            });\r\n            return;\r\n          }\r\n        }\r\n\r\n        resolve({ isValid: true });\r\n      } catch (error) {\r\n        resolve({ isValid: false, error: \"Failed to parse Excel file\" });\r\n      }\r\n    };\r\n    reader.onerror = () => {\r\n      resolve({ isValid: false, error: \"Error reading Excel file\" });\r\n    };\r\n    reader.readAsArrayBuffer(file);\r\n  });\r\n};\r\n\r\n// File upload status component\r\nconst FileUploadStatus = ({\r\n  file,\r\n  onRemove,\r\n  error,\r\n}: {\r\n  file: File;\r\n  onRemove: () => void;\r\n  error?: string;\r\n}) => (\r\n  <div\r\n    className={`flex items-center justify-between p-3 rounded-lg ${\r\n      error\r\n        ? \"bg-red-50 border border-red-200\"\r\n        : \"bg-green-50 border border-green-200\"\r\n    }`}\r\n  >\r\n    <div className=\"flex items-center space-x-2\">\r\n      {error ? (\r\n        <BiErrorCircle className=\"text-red-500\" />\r\n      ) : (\r\n        <BiCheckCircle className=\"text-green-500\" />\r\n      )}\r\n      <div>\r\n        <span className=\"text-sm font-medium\">{file.name}</span>\r\n        <div className=\"text-xs text-gray-500\">\r\n          {(file.size / 1024).toFixed(1)} KB\r\n        </div>\r\n        {error && <div className=\"text-xs text-red-600\">{error}</div>}\r\n      </div>\r\n    </div>\r\n    <button\r\n      type=\"button\"\r\n      onClick={onRemove}\r\n      className=\"text-red-500 hover:text-red-700 p-1\"\r\n      title=\"Remove file\"\r\n    >\r\n      <BiTrash />\r\n    </button>\r\n  </div>\r\n);\r\n\r\n// Download template function\r\nconst downloadExcelTemplate = () => {\r\n  const csvContent =\r\n    \"Label,Code,Next Question ID\\nOption 1,opt1,\\nOption 2,opt2,\\nOption 3,opt3,\";\r\n  const blob = new Blob([csvContent], { type: \"text/csv\" });\r\n  const url = window.URL.createObjectURL(blob);\r\n  const a = document.createElement(\"a\");\r\n  a.href = url;\r\n  a.download = \"question_options_template.csv\";\r\n  document.body.appendChild(a);\r\n  a.click();\r\n  window.URL.revokeObjectURL(url);\r\n  document.body.removeChild(a);\r\n};\r\n\r\n// Confirmation dialog component\r\nconst ConfirmationDialog = ({\r\n  isOpen,\r\n  onConfirm,\r\n  onCancel,\r\n}: {\r\n  isOpen: boolean;\r\n  onConfirm: () => void;\r\n  onCancel: () => void;\r\n}) => {\r\n  if (!isOpen) return null;\r\n  const t = useTranslations();\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white rounded-lg p-6 max-w-md w-full\">\r\n        <h2 className=\"text-lg text-neutral-700 font-semibold mb-4\">\r\n          {t('unsavedChanges')}\r\n        </h2>\r\n        <p className=\"mb-6 text-neutral-700\">\r\n          {t('unsavedChangesWarning')}\r\n        </p>\r\n        <div className=\"flex justify-end space-x-3\">\r\n          <button onClick={onCancel} className=\"btn-outline\">\r\n            {t('cancel')}\r\n          </button>\r\n          <button onClick={onConfirm} className=\"btn-danger\">\r\n            {t('discardChanges')}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddQuestionModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType,\r\n  contextId,\r\n  position,\r\n}: {\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  position?: number;\r\n}) => {\r\n  const methods = useForm<z.infer<typeof QuestionSchema>>({\r\n    resolver: zodResolver(QuestionSchema),\r\n    defaultValues: {\r\n      label: \"\",\r\n      inputType: \"\",\r\n      hint: \"\",\r\n      placeholder: \"\",\r\n      questionOptions: [],\r\n    },\r\n  });\r\n\r\n  const t = useTranslations();\r\n\r\n\r\n  const {\r\n    register,\r\n    formState: { errors, isSubmitted, isDirty },\r\n    setValue,\r\n    handleSubmit,\r\n    reset,\r\n    watch,\r\n  } = methods;\r\n\r\n  const [showConfirmation, setShowConfirmation] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\r\n  const [uploadMethod, setUploadMethod] = useState<\"form\" | \"excel\">(\"form\");\r\n  const [fileError, setFileError] = useState<string>(\"\");\r\n  const [isRequired, setIsRequired] = useState(false);\r\n  const [selectedInputType, setSelectedInputType] = useState<string>(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\", contextId]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\", contextId]\r\n      : [\"questionBlockQuestions\", contextId];\r\n\r\n  const questionMutation = useMutation({\r\n    mutationFn: addQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey });\r\n      // Also invalidate form builder data for project contexts\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: [\"formBuilderData\", contextId] });\r\n      }\r\n      handleClose();\r\n    },\r\n    onError: (error: any) => {\r\n      setFileError(error.message || \"Failed to add question\");\r\n      setIsLoading(false);\r\n    },\r\n  });\r\n\r\n  useEffect(() => {\r\n    register(\"inputType\", { required: t('selectInputType') });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"inputType\", selectedInputType, { shouldValidate: isSubmitted });\r\n  }, [selectedInputType, setValue, isSubmitted]);\r\n\r\n  useEffect(() => {\r\n    if (showModal) {\r\n      setIsLoading(false);\r\n    }\r\n  }, [showModal]);\r\n\r\n  const handleFileSelect = async (\r\n    event: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const file = event.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    const validation = await validateExcelFile(file);\r\n    if (!validation.isValid) {\r\n      setFileError(validation.error || \"Invalid file\");\r\n      setSelectedFile(null);\r\n      return;\r\n    }\r\n\r\n    setFileError(\"\");\r\n    setSelectedFile(file);\r\n  };\r\n\r\n  const removeFile = () => {\r\n    setSelectedFile(null);\r\n    setFileError(\"\");\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = \"\";\r\n    }\r\n  };\r\n\r\n  const handleUploadMethodChange = (method: \"form\" | \"excel\") => {\r\n    setUploadMethod(method);\r\n    if (method === \"form\") {\r\n      removeFile();\r\n    }\r\n  };\r\n\r\n  const hasUnsavedChanges = () => {\r\n    const questionOptions = watch(\"questionOptions\");\r\n    return (\r\n      isDirty ||\r\n      !!watch(\"label\") ||\r\n      !!watch(\"hint\") ||\r\n      !!watch(\"placeholder\") ||\r\n      !!selectedInputType ||\r\n      !!selectedFile ||\r\n      (questionOptions &&\r\n        Array.isArray(questionOptions) &&\r\n        questionOptions.length > 0)\r\n    );\r\n  };\r\n\r\n  const handleCloseWithConfirmation = () => {\r\n    if (hasUnsavedChanges()) {\r\n      setShowConfirmation(true);\r\n    } else {\r\n      handleClose();\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    reset();\r\n    setSelectedInputType(\"\");\r\n    setSelectedFile(null);\r\n    setUploadMethod(\"form\");\r\n    setFileError(\"\");\r\n    setIsLoading(false);\r\n    setShowConfirmation(false);\r\n    setShowModal(false);\r\n  };\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    if (isLoading) return;\r\n\r\n    // If it's a table question, let the TableQuestionBuilder handle it\r\n    if (selectedInputType === \"table\") {\r\n      const tableBuilder = document.querySelector(\".table-question-builder\");\r\n      if (tableBuilder) {\r\n        tableBuilder.dispatchEvent(new CustomEvent(\"submitTable\"));\r\n      } else {\r\n        console.error(\"TableQuestionBuilder not found\");\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Check if this input type requires options\r\n    const inputTypeNeedsOptions = needsOptions(selectedInputType);\r\n\r\n    // Validate based on upload method\r\n    if (inputTypeNeedsOptions) {\r\n      if (uploadMethod === \"excel\") {\r\n        // For Excel upload, validate file is selected\r\n        if (!selectedFile) {\r\n          setFileError(\"Please select an Excel file\");\r\n          return;\r\n        }\r\n        // If file is selected, we don't need to validate questionOptions\r\n        // as they will come from the file\r\n      } else {\r\n        // For manual entry, validate options exist\r\n        const options = data.questionOptions || [];\r\n        if (options.length === 0) {\r\n          methods.setError(\"questionOptions\", {\r\n            type: \"custom\",\r\n            message: t('atLeastOneOptionRequired'),\r\n          });\r\n          return;\r\n        }\r\n\r\n        // Validate each option has required fields\r\n      }\r\n    }\r\n\r\n    setIsLoading(true);\r\n    const fileToSend = selectedFile ?? undefined;\r\n    const dataToSend = {\r\n      label: data.label,\r\n      isRequired,\r\n      hint: data.hint,\r\n      placeholder: data.placeholder,\r\n      inputType: selectedInputType,\r\n      // Only include questionOptions for manual entry\r\n      questionOptions:\r\n        uploadMethod === \"form\" ? data.questionOptions : undefined,\r\n      // Only include file for Excel upload\r\n      file: uploadMethod === \"excel\" ? fileToSend : undefined,\r\n    };\r\n\r\n    questionMutation.mutate({\r\n      contextType,\r\n      contextId,\r\n      dataToSend,\r\n      position,\r\n    });\r\n  };\r\n\r\n  const inputTypeNeedsOptions = needsOptions(selectedInputType);\r\n\r\n  return (\r\n    <>\r\n      <Modal\r\n        isOpen={showModal}\r\n        onClose={handleCloseWithConfirmation}\r\n        className=\"w-11/12 tablet:w-4/5 desktop:w-4/5 bg-neutral-100 rounded-lg p-6\"\r\n      >\r\n        <h1 className=\"heading-text capitalize mb-4\">{t('addQuestion')}</h1>\r\n\r\n        {questionMutation.isPending && <LoadingOverlay />}\r\n\r\n        <FormProvider {...methods}>\r\n          <form\r\n            className=\"space-y-4 max-h-[500px] overflow-y-auto p-4\"\r\n            onSubmit={handleSubmit(onSubmit)}\r\n          >\r\n            <div className=\"label-input-group group \">\r\n              <input\r\n                {...register(\"label\", {\r\n                  required: t('questionNameRequired'),\r\n                })}\r\n                className=\"input-field\"\r\n                placeholder= {t('enterQuestion')}\r\n               \r\n              />\r\n              {errors.label && (\r\n                <p className=\"text-sm text-red-500\">{`${errors.label.message}`}</p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 gap-4\">\r\n              <div className=\"label-input-group group\">\r\n                <label htmlFor=\"question-type\" className=\"label-text\">\r\n                  {t('inputType')}\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <Select\r\n                    id=\"question-type\"\r\n                    options={Object.values(InputTypeMap)}\r\n                    value={\r\n                      selectedInputType && InputTypeMap[selectedInputType]\r\n                        ? InputTypeMap[selectedInputType]\r\n                        : t('selectOption')\r\n                    }\r\n                    onChange={(label) => {\r\n                      const selectedKey = labelToKey(label, InputTypeMap);\r\n                      setSelectedInputType(selectedKey ?? \"\");\r\n                      setUploadMethod(\"form\");\r\n                      removeFile();\r\n                    }}\r\n                  />\r\n                </div>\r\n                {errors.inputType && (\r\n                  <p className=\"text-sm text-red-500\">{`${errors.inputType.message}`}</p>\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"flex items-end\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Switch\r\n                    id=\"required\"\r\n                    checked={isRequired}\r\n                    onCheckedChange={() => setIsRequired((prev) => !prev)}\r\n                    className=\"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500\"\r\n                  />\r\n                  <label htmlFor=\"required\" className=\"label-text\">\r\n                    {t('required')}\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"hint\" className=\"label-text\">\r\n                {t('helpText')}\r\n              </label>\r\n              <textarea\r\n                {...register(\"hint\")}\r\n                id=\"hint\"\r\n                placeholder= {t('helpTextHint')}\r\n                className=\"input-field resize-none\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"placeholder\" className=\"label-text\">\r\n                {t('placeholderText')}\r\n              </label>\r\n              <input\r\n                {...register(\"placeholder\")}\r\n                id=\"placeholder\"\r\n                placeholder={t('placeholderHint')}\r\n                className=\"input-field\"\r\n              />\r\n            </div>\r\n\r\n            {inputTypeNeedsOptions && (\r\n              <div className=\"space-y-4\">\r\n                <div className=\"label-input-group group\">\r\n                  <label className=\"label-text\">{t('questionOptions')}</label>\r\n\r\n                  <div className=\"flex space-x-4 mb-4\">\r\n                    <label className=\"flex items-center space-x-2 cursor-pointer\">\r\n                      <input\r\n                        type=\"radio\"\r\n                        value=\"form\"\r\n                        checked={uploadMethod === \"form\"}\r\n                        onChange={(e) =>\r\n                          handleUploadMethodChange(\r\n                            e.target.value as \"form\" | \"excel\"\r\n                          )\r\n                        }\r\n                        className=\"text-primary-500\"\r\n                      />\r\n                      <span>{t('manualEntry')}</span>\r\n                    </label>\r\n                    <label className=\"flex items-center space-x-2 cursor-pointer\">\r\n                      <input\r\n                        type=\"radio\"\r\n                        value=\"excel\"\r\n                        checked={uploadMethod === \"excel\"}\r\n                        onChange={(e) =>\r\n                          handleUploadMethodChange(\r\n                            e.target.value as \"form\" | \"excel\"\r\n                          )\r\n                        }\r\n                        className=\"text-primary-500\"\r\n                      />\r\n                      <span>{t('excelUpload')}</span>\r\n                    </label>\r\n                  </div>\r\n\r\n                  {uploadMethod === \"excel\" && (\r\n                    <div className=\"space-y-3\">\r\n                      <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors\">\r\n                        <div className=\"text-center\">\r\n                          <BiUpload className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n                          <div className=\"mt-2\">\r\n                            <label\r\n                              htmlFor=\"excel-file\"\r\n                              className=\"cursor-pointer\"\r\n                            >\r\n                              <span className=\"mt-2 block text-sm font-medium text-gray-900\">\r\n                                {t('uploadExcel')}\r\n                              </span>\r\n                              <span className=\"mt-1 block text-xs text-gray-500\">\r\n                                {t('supportedFormats')}: .xlsx, .xls (max 5MB)\r\n                              </span>\r\n                            </label>\r\n                            <input\r\n                              ref={fileInputRef}\r\n                              id=\"excel-file\"\r\n                              type=\"file\"\r\n                              accept=\".xlsx,.xls\"\r\n                              onChange={handleFileSelect}\r\n                              className=\"sr-only\"\r\n                            />\r\n                          </div>\r\n                          <div className=\"mt-3 flex justify-center space-x-2\">\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={() => fileInputRef.current?.click()}\r\n                              className=\"btn-outline inline-flex items-center\"\r\n                            >\r\n                              <BiImport className=\"mr-2\" />\r\n                              {t('chooseExcel')}\r\n                            </button>\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={downloadExcelTemplate}\r\n                              className=\"btn-outline inline-flex items-center\"\r\n                              title={t('downloadTemplate')}\r\n                            >\r\n                              <BiDownload className=\"mr-2\" />\r\n                              {t('downloadTemplate')}\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {selectedFile && (\r\n                        <FileUploadStatus\r\n                          file={selectedFile}\r\n                          onRemove={removeFile}\r\n                          error={fileError}\r\n                        />\r\n                      )}\r\n\r\n                      {fileError && !selectedFile && (\r\n                        <div className=\"text-sm text-red-600 bg-red-50 p-2 rounded\">\r\n                          {fileError}\r\n                        </div>\r\n                      )}\r\n\r\n                      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-3\">\r\n                        <p className=\"text-sm text-blue-800 font-medium mb-2\">\r\n                          {t('excelFormatRequirements')}\r\n                        </p>\r\n                        <ul className=\"text-xs text-blue-700 space-y-1\">\r\n                          <li>\r\n                            • {t('columnA')}: <strong>{t('label')}</strong> ({t('required')}) -\r\n                            {t('optionDisplayText')}\r\n                          </li>\r\n                          <li>\r\n                            • {t('columnB')}: <strong>{t('code')}</strong> ({t('required')}) -\r\n                            {t('uniqueIdentifiers')}\r\n                          </li>\r\n                          <li>\r\n                            • {t('ColumnC')}: <strong>{t('nextQuestionId')}</strong>{\" \"}\r\n                            ({t('optional')}) - {t('forConditionalLogic')}\r\n                          </li>\r\n                          <li>• {t('firstRowHeaders')}</li>\r\n                          <li>• {t('eachRowOption')}</li>\r\n                        </ul>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {uploadMethod === \"form\" && (\r\n                    <DynamicOptions\r\n                      contextType={contextType}\r\n                      contextId={contextId}\r\n                      inputType={selectedInputType}\r\n                    />\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {selectedInputType === \"table\" && (\r\n              <div className=\"mt-4\">\r\n                <TableQuestionBuilder\r\n                  projectId={contextId}\r\n                  isInModal={true}\r\n                  onTableCreated={(tableId) => {\r\n                    if (tableId !== -1) {\r\n                      queryClient.invalidateQueries({ queryKey });\r\n                      // Also invalidate form builder data for project contexts\r\n                      if (contextType === \"project\") {\r\n                        queryClient.invalidateQueries({ queryKey: [\"formBuilderData\", contextId] });\r\n                      }\r\n                    }\r\n                    handleClose();\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"flex items-center justify-end space-x-4 pt-4\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleCloseWithConfirmation}\r\n                className=\"btn-outline\"\r\n                disabled={isLoading}\r\n              >\r\n                {t('cancel')}\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn-primary flex items-center justify-center gap-2\"\r\n                onClick={(e) => {\r\n                  if (selectedInputType === \"table\") {\r\n                    e.preventDefault();\r\n                    const tableBuilder = document.querySelector(\r\n                      \".table-question-builder\"\r\n                    );\r\n                    if (tableBuilder) {\r\n                      tableBuilder.dispatchEvent(\r\n                        new CustomEvent(\"submitTable\")\r\n                      );\r\n                    }\r\n                  }\r\n                }}\r\n                disabled={\r\n                  isLoading ||\r\n                  (uploadMethod === \"excel\" && (!selectedFile || !!fileError))\r\n                }\r\n              >\r\n                {isLoading ? (\r\n                  <>\r\n                    <span className=\"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full\" />\r\n                    {t('saving')}...\r\n                  </>\r\n                ) : (\r\n                  t('save')\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </FormProvider>\r\n      </Modal>\r\n\r\n      <ConfirmationDialog\r\n        isOpen={showConfirmation}\r\n        onConfirm={handleClose}\r\n        onCancel={() => setShowConfirmation(false)}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport { AddQuestionModal };"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAQA,kOAA8B,yCAAyC;AACvE;;;;;;;;;;;;;;;;;;;;;AAGA,0BAA0B;AAC1B,MAAM,oBAAoB,CACxB;IAEA,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,aAAa;YACjB;YACA;SACD;QACD,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;QAEvC,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnC,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;YACA;QACF;QAEA,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,QAAQ;gBAAE,SAAS;gBAAO,OAAO;YAAkC;YACnE;QACF;QAEA,uCAAuC;QACvC,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,OAAO,IAAI,WAAW,EAAE,MAAM,EAAE;gBACtC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,OAAS,AAAD,EAAE,MAAM;oBAAE,MAAM;gBAAQ;gBACjD,MAAM,YAAY,SAAS,MAAM,CAAC,SAAS,UAAU,CAAC,EAAE,CAAC;gBACzD,MAAM,OAAO,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC,WAAW;oBAC/C,QAAQ;gBACV;gBAEA,IAAI,KAAK,MAAM,GAAG,GAAG;oBACnB,QAAQ;wBACN,SAAS;wBACT,OAAO;oBACT;oBACA;gBACF;gBAEA,MAAM,UAAU,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAW,GAAG,WAAW;gBACtD,MAAM,kBAAkB;oBAAC;oBAAS;oBAAQ;iBAAmB;gBAC7D,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,SAAS;oBACnE,QAAQ;wBACN,SAAS;wBACT,OACE;oBACJ;oBACA;gBACF;gBAEA,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,IAAI,SAAS,MAAM,KAAK,GAAG;oBACzB,QAAQ;wBACN,SAAS;wBACT,OAAO;oBACT;oBACA;gBACF;gBAEA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;oBACxC,MAAM,MAAM,QAAQ,CAAC,EAAE;oBACvB,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE;wBACtB,QAAQ;4BACN,SAAS;4BACT,OAAO,CAAC,oBAAoB,EAC1B,IAAI,EACL,6BAA6B,CAAC;wBACjC;wBACA;oBACF;gBACF;gBAEA,QAAQ;oBAAE,SAAS;gBAAK;YAC1B,EAAE,OAAO,OAAO;gBACd,QAAQ;oBAAE,SAAS;oBAAO,OAAO;gBAA6B;YAChE;QACF;QACA,OAAO,OAAO,GAAG;YACf,QAAQ;gBAAE,SAAS;gBAAO,OAAO;YAA2B;QAC9D;QACA,OAAO,iBAAiB,CAAC;IAC3B;AACF;AAEA,+BAA+B;AAC/B,MAAM,mBAAmB,CAAC,EACxB,IAAI,EACJ,QAAQ,EACR,KAAK,EAKN,iBACC,6LAAC;QACC,WAAW,CAAC,iDAAiD,EAC3D,QACI,oCACA,uCACJ;;0BAEF,6LAAC;gBAAI,WAAU;;oBACZ,sBACC,6LAAC,iJAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;6CAEzB,6LAAC,iJAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCAE3B,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;0CAAuB,KAAK,IAAI;;;;;;0CAChD,6LAAC;gCAAI,WAAU;;oCACZ,CAAC,KAAK,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;4BAEhC,uBAAS,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAGrD,6LAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;KApCR;AAyCN,6BAA6B;AAC7B,MAAM,wBAAwB;IAC5B,MAAM,aACJ;IACF,MAAM,OAAO,IAAI,KAAK;QAAC;KAAW,EAAE;QAAE,MAAM;IAAW;IACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;IACvC,MAAM,IAAI,SAAS,aAAa,CAAC;IACjC,EAAE,IAAI,GAAG;IACT,EAAE,QAAQ,GAAG;IACb,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,EAAE,KAAK;IACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;AAC5B;AAEA,gCAAgC;AAChC,MAAM,qBAAqB,CAAC,EAC1B,MAAM,EACN,SAAS,EACT,QAAQ,EAKT;;IACC,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BACX,EAAE;;;;;;8BAEL,6LAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAEL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAO,SAAS;4BAAU,WAAU;sCAClC,EAAE;;;;;;sCAEL,6LAAC;4BAAO,SAAS;4BAAW,WAAU;sCACnC,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf;GAhCM;;QAUM,yMAAA,CAAA,kBAAe;;;MAVrB;AAkCN,MAAM,mBAAmB,CAAC,EACxB,SAAS,EACT,YAAY,EACZ,WAAW,EACX,SAAS,EACT,QAAQ,EAOT;;IACC,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkC;QACtD,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,uHAAA,CAAA,iBAAc;QACpC,eAAe;YACb,OAAO;YACP,WAAW;YACX,MAAM;YACN,aAAa;YACb,iBAAiB,EAAE;QACrB;IACF;IAEA,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAGxB,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,EAC3C,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,KAAK,EACN,GAAG;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,WACJ,gBAAgB,YACZ;QAAC;QAAa;KAAU,GACxB,gBAAgB,aAChB;QAAC;QAAqB;KAAU,GAChC;QAAC;QAA0B;KAAU;IAE3C,MAAM,mBAAmB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY,gIAAA,CAAA,cAAW;QACvB,SAAS;8DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;gBACzC,yDAAyD;gBACzD,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAmB;yBAAU;oBAAC;gBAC3E;gBACA;YACF;;QACA,OAAO;8DAAE,CAAC;gBACR,aAAa,MAAM,OAAO,IAAI;gBAC9B,aAAa;YACf;;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,SAAS,aAAa;gBAAE,UAAU,EAAE;YAAmB;QACzD;qCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,SAAS,aAAa,mBAAmB;gBAAE,gBAAgB;YAAY;QACzE;qCAAG;QAAC;QAAmB;QAAU;KAAY;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW;gBACb,aAAa;YACf;QACF;qCAAG;QAAC;KAAU;IAEd,MAAM,mBAAmB,OACvB;QAEA,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,aAAa,MAAM,kBAAkB;QAC3C,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,aAAa,WAAW,KAAK,IAAI;YACjC,gBAAgB;YAChB;QACF;QAEA,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,gBAAgB;QAChB,aAAa;QACb,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB,IAAI,WAAW,QAAQ;YACrB;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,kBAAkB,MAAM;QAC9B,OACE,WACA,CAAC,CAAC,MAAM,YACR,CAAC,CAAC,MAAM,WACR,CAAC,CAAC,MAAM,kBACR,CAAC,CAAC,qBACF,CAAC,CAAC,gBACD,mBACC,MAAM,OAAO,CAAC,oBACd,gBAAgB,MAAM,GAAG;IAE/B;IAEA,MAAM,8BAA8B;QAClC,IAAI,qBAAqB;YACvB,oBAAoB;QACtB,OAAO;YACL;QACF;IACF;IAEA,MAAM,cAAc;QAClB;QACA,qBAAqB;QACrB,gBAAgB;QAChB,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,oBAAoB;QACpB,aAAa;IACf;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,WAAW;QAEf,mEAAmE;QACnE,IAAI,sBAAsB,SAAS;YACjC,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,IAAI,cAAc;gBAChB,aAAa,aAAa,CAAC,IAAI,YAAY;YAC7C,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;YACA;QACF;QAEA,4CAA4C;QAC5C,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;QAE3C,kCAAkC;QAClC,IAAI,uBAAuB;YACzB,IAAI,iBAAiB,SAAS;gBAC5B,8CAA8C;gBAC9C,IAAI,CAAC,cAAc;oBACjB,aAAa;oBACb;gBACF;YACA,iEAAiE;YACjE,kCAAkC;YACpC,OAAO;gBACL,2CAA2C;gBAC3C,MAAM,UAAU,KAAK,eAAe,IAAI,EAAE;gBAC1C,IAAI,QAAQ,MAAM,KAAK,GAAG;oBACxB,QAAQ,QAAQ,CAAC,mBAAmB;wBAClC,MAAM;wBACN,SAAS,EAAE;oBACb;oBACA;gBACF;YAEA,2CAA2C;YAC7C;QACF;QAEA,aAAa;QACb,MAAM,aAAa,gBAAgB;QACnC,MAAM,aAAa;YACjB,OAAO,KAAK,KAAK;YACjB;YACA,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,WAAW;YACX,gDAAgD;YAChD,iBACE,iBAAiB,SAAS,KAAK,eAAe,GAAG;YACnD,qCAAqC;YACrC,MAAM,iBAAiB,UAAU,aAAa;QAChD;QAEA,iBAAiB,MAAM,CAAC;YACtB;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,wBAAwB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;IAE3C,qBACE;;0BACE,6LAAC,iIAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS;gBACT,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;kCAAgC,EAAE;;;;;;oBAE/C,iBAAiB,SAAS,kBAAI,6LAAC,2IAAA,CAAA,iBAAc;;;;;kCAE9C,6LAAC,iKAAA,CAAA,eAAY;wBAAE,GAAG,OAAO;kCACvB,cAAA,6LAAC;4BACC,WAAU;4BACV,UAAU,aAAa;;8CAEvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACE,GAAG,SAAS,SAAS;gDACpB,UAAU,EAAE;4CACd,EAAE;4CACF,WAAU;4CACV,aAAc,EAAE;;;;;;wCAGjB,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAAwB,GAAG,OAAO,KAAK,CAAC,OAAO,EAAE;;;;;;;;;;;;8CAIlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DACtC,EAAE;;;;;;8DAEL,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mIAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS,OAAO,MAAM,CAAC,yHAAA,CAAA,eAAY;wDACnC,OACE,qBAAqB,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAChD,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAC/B,EAAE;wDAER,UAAU,CAAC;4DACT,MAAM,cAAc,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,yHAAA,CAAA,eAAY;4DAClD,qBAAqB,eAAe;4DACpC,gBAAgB;4DAChB;wDACF;;;;;;;;;;;gDAGH,OAAO,SAAS,kBACf,6LAAC;oDAAE,WAAU;8DAAwB,GAAG,OAAO,SAAS,CAAC,OAAO,EAAE;;;;;;;;;;;;sDAItE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8HAAA,CAAA,SAAM;wDACL,IAAG;wDACH,SAAS;wDACT,iBAAiB,IAAM,cAAc,CAAC,OAAS,CAAC;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEACjC,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAMX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAC7B,EAAE;;;;;;sDAEL,6LAAC;4CACE,GAAG,SAAS,OAAO;4CACpB,IAAG;4CACH,aAAc,EAAE;4CAChB,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDACpC,EAAE;;;;;;sDAEL,6LAAC;4CACE,GAAG,SAAS,cAAc;4CAC3B,IAAG;4CACH,aAAa,EAAE;4CACf,WAAU;;;;;;;;;;;;gCAIb,uCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAc,EAAE;;;;;;0DAEjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,OAAM;gEACN,SAAS,iBAAiB;gEAC1B,UAAU,CAAC,IACT,yBACE,EAAE,MAAM,CAAC,KAAK;gEAGlB,WAAU;;;;;;0EAEZ,6LAAC;0EAAM,EAAE;;;;;;;;;;;;kEAEX,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,OAAM;gEACN,SAAS,iBAAiB;gEAC1B,UAAU,CAAC,IACT,yBACE,EAAE,MAAM,CAAC,KAAK;gEAGlB,WAAU;;;;;;0EAEZ,6LAAC;0EAAM,EAAE;;;;;;;;;;;;;;;;;;4CAIZ,iBAAiB,yBAChB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iJAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,SAAQ;4EACR,WAAU;;8FAEV,6LAAC;oFAAK,WAAU;8FACb,EAAE;;;;;;8FAEL,6LAAC;oFAAK,WAAU;;wFACb,EAAE;wFAAoB;;;;;;;;;;;;;sFAG3B,6LAAC;4EACC,KAAK;4EACL,IAAG;4EACH,MAAK;4EACL,QAAO;4EACP,UAAU;4EACV,WAAU;;;;;;;;;;;;8EAGd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,MAAK;4EACL,SAAS,IAAM,aAAa,OAAO,EAAE;4EACrC,WAAU;;8FAEV,6LAAC,iJAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFACnB,EAAE;;;;;;;sFAEL,6LAAC;4EACC,MAAK;4EACL,SAAS;4EACT,WAAU;4EACV,OAAO,EAAE;;8FAET,6LAAC,iJAAA,CAAA,aAAU;oFAAC,WAAU;;;;;;gFACrB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;oDAMV,8BACC,6LAAC;wDACC,MAAM;wDACN,UAAU;wDACV,OAAO;;;;;;oDAIV,aAAa,CAAC,8BACb,6LAAC;wDAAI,WAAU;kEACZ;;;;;;kEAIL,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,EAAE;;;;;;0EAEL,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;;4EAAG;4EACC,EAAE;4EAAW;0FAAE,6LAAC;0FAAQ,EAAE;;;;;;4EAAkB;4EAAG,EAAE;4EAAY;4EAC/D,EAAE;;;;;;;kFAEL,6LAAC;;4EAAG;4EACC,EAAE;4EAAW;0FAAE,6LAAC;0FAAQ,EAAE;;;;;;4EAAiB;4EAAG,EAAE;4EAAY;4EAC9D,EAAE;;;;;;;kFAEL,6LAAC;;4EAAG;4EACC,EAAE;4EAAW;0FAAE,6LAAC;0FAAQ,EAAE;;;;;;4EAA4B;4EAAI;4EAC3D,EAAE;4EAAY;4EAAK,EAAE;;;;;;;kFAEzB,6LAAC;;4EAAG;4EAAG,EAAE;;;;;;;kFACT,6LAAC;;4EAAG;4EAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;4CAMhB,iBAAiB,wBAChB,6LAAC,mJAAA,CAAA,iBAAc;gDACb,aAAa;gDACb,WAAW;gDACX,WAAW;;;;;;;;;;;;;;;;;gCAOpB,sBAAsB,yBACrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yJAAA,CAAA,uBAAoB;wCACnB,WAAW;wCACX,WAAW;wCACX,gBAAgB,CAAC;4CACf,IAAI,YAAY,CAAC,GAAG;gDAClB,YAAY,iBAAiB,CAAC;oDAAE;gDAAS;gDACzC,yDAAyD;gDACzD,IAAI,gBAAgB,WAAW;oDAC7B,YAAY,iBAAiB,CAAC;wDAAE,UAAU;4DAAC;4DAAmB;yDAAU;oDAAC;gDAC3E;4CACF;4CACA;wCACF;;;;;;;;;;;8CAKN,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDAET,EAAE;;;;;;sDAEL,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS,CAAC;gDACR,IAAI,sBAAsB,SAAS;oDACjC,EAAE,cAAc;oDAChB,MAAM,eAAe,SAAS,aAAa,CACzC;oDAEF,IAAI,cAAc;wDAChB,aAAa,aAAa,CACxB,IAAI,YAAY;oDAEpB;gDACF;4CACF;4CACA,UACE,aACC,iBAAiB,WAAW,CAAC,CAAC,gBAAgB,CAAC,CAAC,SAAS;sDAG3D,0BACC;;kEACE,6LAAC;wDAAK,WAAU;;;;;;oDACf,EAAE;oDAAU;;+DAGf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,6LAAC;gBACC,QAAQ;gBACR,WAAW;gBACX,UAAU,IAAM,oBAAoB;;;;;;;;AAI5C;IApgBM;;QAaY,iKAAA,CAAA,UAAO;QAWb,yMAAA,CAAA,kBAAe;QAsBL,yLAAA,CAAA,iBAAc;QAQT,iLAAA,CAAA,cAAW;;;MAtDhC", "debugId": null}}, {"offset": {"line": 6671, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/EditQuestionModal.tsx"], "sourcesContent": ["import { Question, QuestionSchema } from \"@/types/formBuilder\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { FieldValues, FormProvider, useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { InputTypeMap } from \"@/constants/inputType\";\r\nimport { needsOptions } from \"@/lib/needsOptions\";\r\nimport { DynamicOptions } from \"../form-builder/DynamicOptions\";\r\nimport { Switch } from \"../ui\";\r\nimport { ContextType, TemplateQuestion } from \"@/types\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { updateQuestion } from \"@/lib/api/form-builder\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst EditQuestionModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType,\r\n  question,\r\n  contextId,\r\n}: {\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n  contextType: ContextType;\r\n  question: Question | TemplateQuestion;\r\n  contextId: number;\r\n}) => {\r\n  const methods = useForm<z.infer<typeof QuestionSchema>>({});\r\n  const t = useTranslations();\r\n\r\n  const {\r\n    register,\r\n    formState: { errors, isSubmitted },\r\n    setValue,\r\n    handleSubmit,\r\n    reset,\r\n  } = methods;\r\n\r\n  // Type guard to check if question is a Question type\r\n  const isQuestion = (q: Question | TemplateQuestion): q is Question => {\r\n    return 'position' in q;\r\n  };\r\n\r\n  // Helper function to safely get property from question\r\n  const getQuestionProperty = <T,>(prop: string, defaultValue: T): T => {\r\n    return (question as any)[prop] ?? defaultValue;\r\n  };\r\n\r\n  // Initialize state with default values - will be updated in useEffect\r\n  const [isRequired, setIsRequired] = useState(false);\r\n  const [selectedInputType, setSelectedInputType] = useState<string>(\"\");\r\n\r\n  useEffect(() => {\r\n    const result = QuestionSchema.safeParse(question);\r\n    if (result.success) {\r\n      // Reset form with all the data\r\n      reset(result.data);\r\n      setSelectedInputType(result.data.inputType || \"\");\r\n\r\n      // Explicitly set questionOptions if they exist\r\n      if (result.data.questionOptions && result.data.questionOptions.length > 0) {\r\n        setValue(\"questionOptions\", result.data.questionOptions);\r\n      }\r\n    } else {\r\n      // Fallback for when schema parsing fails\r\n      console.warn(\"Schema parsing failed, using raw question data:\", result.error);\r\n\r\n      // Manually set form values\r\n      setValue(\"label\", getQuestionProperty('label', \"\"));\r\n      setValue(\"hint\", getQuestionProperty('hint', \"\"));\r\n      setValue(\"placeholder\", getQuestionProperty('placeholder', \"\"));\r\n      setValue(\"inputType\", getQuestionProperty('inputType', \"\"));\r\n      setSelectedInputType(getQuestionProperty('inputType', \"\"));\r\n\r\n      // Handle questionOptions explicitly - check if property exists\r\n      const questionOptions = getQuestionProperty('questionOptions', []);\r\n      if (Array.isArray(questionOptions) && questionOptions.length > 0) {\r\n        setValue(\"questionOptions\", questionOptions);\r\n      }\r\n    }\r\n\r\n    // Always update isRequired from the raw question data (outside schema parsing)\r\n    setIsRequired(getQuestionProperty('isRequired', false));\r\n  }, [question, reset, setValue]);\r\n\r\n  useEffect(() => {\r\n    register(\"inputType\", { required: \"Please select an input type\" });\r\n  }, [register]);\r\n\r\n  useEffect(() => {\r\n    setValue(\"inputType\", selectedInputType, { shouldValidate: isSubmitted });\r\n  }, [selectedInputType, setValue, isSubmitted]);\r\n\r\n  const queryClient = useQueryClient();\r\n  const dispatch = useDispatch();\r\n\r\n  const queryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\"]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\"]\r\n      : [\"questionBlockQuestions\"];\r\n\r\n  const handleClose = () => {\r\n    // Reset state to default values when closing\r\n    setIsRequired(false);\r\n    setSelectedInputType(\"\");\r\n    setShowModal(false);\r\n  };\r\n\r\n  const updateQuestionMutation = useMutation({\r\n    mutationFn: updateQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({\r\n        queryKey,\r\n        exact: false,\r\n      });\r\n      // Also invalidate form builder data for project contexts\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: [\"formBuilderData\", contextId] });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionsUpdated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      handleClose();\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionUpdateFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const onSubmit = async (data: FieldValues) => {\r\n    // Special handling for table input type\r\n    if (selectedInputType === \"table\" || \r\n        ('inputType' in question && question.inputType === \"table\")) {\r\n      // For table questions, we need to trigger the TableQuestionBuilder's submit\r\n      const tableBuilder = document.querySelector(\".table-question-builder\");\r\n      if (tableBuilder) {\r\n        tableBuilder.dispatchEvent(new CustomEvent(\"submitTable\"));\r\n        return; // Exit early as TableQuestionBuilder handles its own submission\r\n      }\r\n    }\r\n\r\n    const dataToSend = {\r\n      label: data.label,\r\n      isRequired,\r\n      hint: data.hint,\r\n      placeholder: data.placeholder,\r\n      inputType: selectedInputType || ('inputType' in question ? question.inputType : ''),\r\n      questionOptions: data.questionOptions,\r\n      // Preserve the original position if it exists (for Question type)\r\n      ...(('position' in question) && { position: question.position }),\r\n    };\r\n    \r\n    updateQuestionMutation.mutate({\r\n      id: question.id,\r\n      contextType,\r\n      dataToSend,\r\n      contextId,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={handleClose}\r\n      className=\"w-11/12 tablet:w-4/5 desktop:w-3/5\"\r\n    >\r\n      <h1 className=\"heading-text capitalize mb-4\">{t('editQuestion')}</h1>\r\n\r\n      {updateQuestionMutation.isPending && <LoadingOverlay />}\r\n      <FormProvider {...methods}>\r\n        <form\r\n          className=\"space-y-4 max-h-[500px] overflow-y-auto p-4\"\r\n          onSubmit={handleSubmit(onSubmit)}\r\n        >\r\n          <div className=\"label-input-group group\">\r\n            <input\r\n              {...register(\"label\", { required: t('questionNameRequired') })}\r\n              className=\"input-field\"\r\n              placeholder={t('enterQuestion')}\r\n            />\r\n            {errors.label && (\r\n              <p className=\"text-sm text-red-500\">{`${errors.label.message}`}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            <div className=\"label-input-group group\">\r\n              <label htmlFor=\"question-type\" className=\"label-text\">\r\n                {t('inputType')}\r\n              </label>\r\n              <input\r\n                id=\"question-type\"\r\n                className=\"input-field bg-gray-100 \"\r\n                value={\r\n                  selectedInputType && InputTypeMap[selectedInputType]\r\n                    ? InputTypeMap[selectedInputType]\r\n                    : \"N/A\"\r\n                }\r\n                disabled\r\n              />\r\n            </div>\r\n            <div className=\"flex items-end\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Switch\r\n                  id=\"required\"\r\n                  checked={isRequired}\r\n                  onCheckedChange={() => setIsRequired((prev) => !prev)}\r\n                  className=\"data-[state=checked]:bg-primary-500 data-[state=unchecked]:bg-neutral-100 border border-primary-500\"\r\n                />\r\n                <label htmlFor=\"required\" className=\"label-text\">\r\n                  {t('required')}\r\n                </label>\r\n              </div>\r\n            </div>\r\n            {errors.inputType && (\r\n              <p className=\"text-sm text-red-500\">{`${errors.inputType.message}`}</p>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"hint\" className=\"label-text\">\r\n             {t('helpText')}\r\n            </label>\r\n            <textarea\r\n              {...register(\"hint\")}\r\n              id=\"hint\"\r\n              placeholder={t('helpTextHint')}\r\n              className=\"input-field resize-none\"\r\n            />\r\n          </div>\r\n          <div className=\"label-input-group group\">\r\n            <label htmlFor=\"placeholder\" className=\"label-text\">\r\n              {t('placeholderText')}\r\n            </label>\r\n            <input\r\n              {...register(\"placeholder\")}\r\n              id=\"placeholder\"\r\n              placeholder={t('placeholderHint')}\r\n              className=\"input-field\"\r\n            />\r\n          </div>\r\n\r\n          {needsOptions(selectedInputType) && (\r\n            <DynamicOptions\r\n              contextType={contextType}\r\n              contextId={contextId}\r\n              currentQuestionId={question.id}\r\n              inputType={selectedInputType}\r\n              key={`${question.id}-${selectedInputType}`} // Add key to force re-render\r\n            />\r\n          )}\r\n          <div className=\"flex items-center justify-end space-x-4\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleClose}\r\n              className=\"btn-outline\"\r\n            >\r\n              {t('cancel')}\r\n            </button>\r\n            <button onClick={handleSubmit(onSubmit)} className=\"btn-primary\">\r\n              {t('saveEdit')}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </FormProvider>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { EditQuestionModal };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAEA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,SAAS,EAOV;;IACC,MAAM,UAAU,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkC,CAAC;IACzD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,EACJ,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,EAClC,QAAQ,EACR,YAAY,EACZ,KAAK,EACN,GAAG;IAEJ,qDAAqD;IACrD,MAAM,aAAa,CAAC;QAClB,OAAO,cAAc;IACvB;IAEA,uDAAuD;IACvD,MAAM,sBAAsB,CAAK,MAAc;QAC7C,OAAO,AAAC,QAAgB,CAAC,KAAK,IAAI;IACpC;IAEA,sEAAsE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,SAAS,uHAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,+BAA+B;gBAC/B,MAAM,OAAO,IAAI;gBACjB,qBAAqB,OAAO,IAAI,CAAC,SAAS,IAAI;gBAE9C,+CAA+C;gBAC/C,IAAI,OAAO,IAAI,CAAC,eAAe,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,GAAG;oBACzE,SAAS,mBAAmB,OAAO,IAAI,CAAC,eAAe;gBACzD;YACF,OAAO;gBACL,yCAAyC;gBACzC,QAAQ,IAAI,CAAC,mDAAmD,OAAO,KAAK;gBAE5E,2BAA2B;gBAC3B,SAAS,SAAS,oBAAoB,SAAS;gBAC/C,SAAS,QAAQ,oBAAoB,QAAQ;gBAC7C,SAAS,eAAe,oBAAoB,eAAe;gBAC3D,SAAS,aAAa,oBAAoB,aAAa;gBACvD,qBAAqB,oBAAoB,aAAa;gBAEtD,+DAA+D;gBAC/D,MAAM,kBAAkB,oBAAoB,mBAAmB,EAAE;gBACjE,IAAI,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,GAAG,GAAG;oBAChE,SAAS,mBAAmB;gBAC9B;YACF;YAEA,+EAA+E;YAC/E,cAAc,oBAAoB,cAAc;QAClD;sCAAG;QAAC;QAAU;QAAO;KAAS;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,SAAS,aAAa;gBAAE,UAAU;YAA8B;QAClE;sCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,SAAS,aAAa,mBAAmB;gBAAE,gBAAgB;YAAY;QACzE;sCAAG;QAAC;QAAmB;QAAU;KAAY;IAE7C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WACJ,gBAAgB,YACZ;QAAC;KAAY,GACb,gBAAgB,aAChB;QAAC;KAAoB,GACrB;QAAC;KAAyB;IAEhC,MAAM,cAAc;QAClB,6CAA6C;QAC7C,cAAc;QACd,qBAAqB;QACrB,aAAa;IACf;IAEA,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,YAAY,gIAAA,CAAA,iBAAc;QAC1B,SAAS;qEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAC5B;oBACA,OAAO;gBACT;gBACA,yDAAyD;gBACzD,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAmB;yBAAU;oBAAC;gBAC3E;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF;YACF;;QACA,OAAO;qEAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,wCAAwC;QACxC,IAAI,sBAAsB,WACrB,eAAe,YAAY,SAAS,SAAS,KAAK,SAAU;YAC/D,4EAA4E;YAC5E,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,IAAI,cAAc;gBAChB,aAAa,aAAa,CAAC,IAAI,YAAY;gBAC3C,QAAQ,gEAAgE;YAC1E;QACF;QAEA,MAAM,aAAa;YACjB,OAAO,KAAK,KAAK;YACjB;YACA,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,WAAW,qBAAqB,CAAC,eAAe,WAAW,SAAS,SAAS,GAAG,EAAE;YAClF,iBAAiB,KAAK,eAAe;YACrC,kEAAkE;YAClE,GAAI,AAAC,cAAc,YAAa;gBAAE,UAAU,SAAS,QAAQ;YAAC,CAAC;QACjE;QAEA,uBAAuB,MAAM,CAAC;YAC5B,IAAI,SAAS,EAAE;YACf;YACA;YACA;QACF;IACF;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAAgC,EAAE;;;;;;YAE/C,uBAAuB,SAAS,kBAAI,6LAAC,2IAAA,CAAA,iBAAc;;;;;0BACpD,6LAAC,iKAAA,CAAA,eAAY;gBAAE,GAAG,OAAO;0BACvB,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,aAAa;;sCAEvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACE,GAAG,SAAS,SAAS;wCAAE,UAAU,EAAE;oCAAwB,EAAE;oCAC9D,WAAU;oCACV,aAAa,EAAE;;;;;;gCAEhB,OAAO,KAAK,kBACX,6LAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,KAAK,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAGlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDACtC,EAAE;;;;;;sDAEL,6LAAC;4CACC,IAAG;4CACH,WAAU;4CACV,OACE,qBAAqB,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAChD,yHAAA,CAAA,eAAY,CAAC,kBAAkB,GAC/B;4CAEN,QAAQ;;;;;;;;;;;;8CAGZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS;gDACT,iBAAiB,IAAM,cAAc,CAAC,OAAS,CAAC;gDAChD,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DACjC,EAAE;;;;;;;;;;;;;;;;;gCAIR,OAAO,SAAS,kBACf,6LAAC;oCAAE,WAAU;8CAAwB,GAAG,OAAO,SAAS,CAAC,OAAO,EAAE;;;;;;;;;;;;sCAItE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAC9B,EAAE;;;;;;8CAEJ,6LAAC;oCACE,GAAG,SAAS,OAAO;oCACpB,IAAG;oCACH,aAAa,EAAE;oCACf,WAAU;;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CACpC,EAAE;;;;;;8CAEL,6LAAC;oCACE,GAAG,SAAS,cAAc;oCAC3B,IAAG;oCACH,aAAa,EAAE;oCACf,WAAU;;;;;;;;;;;;wBAIb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,oCACZ,6LAAC,mJAAA,CAAA,iBAAc;4BACb,aAAa;4BACb,WAAW;4BACX,mBAAmB,SAAS,EAAE;4BAC9B,WAAW;2BACN,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,mBAAmB;;;;;sCAG9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC;oCAAO,SAAS,aAAa;oCAAW,WAAU;8CAChD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GAtQM;;QAaY,iKAAA,CAAA,UAAO;QACb,yMAAA,CAAA,kBAAe;QAiEL,yLAAA,CAAA,iBAAc;QACjB,4JAAA,CAAA,cAAW;QAgBG,iLAAA,CAAA,cAAW;;;KAhGtC", "debugId": null}}, {"offset": {"line": 7120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/EditTableQuestionModal.tsx"], "sourcesContent": ["import { Question } from \"@/types/formBuilder\";\r\nimport React, { useEffect } from \"react\";\r\nimport Modal from \"./Modal\";\r\nimport { ContextType } from \"@/types\";\r\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\nimport { TableQuestionBuilder } from \"../form-builder/TableQuestionBuilder\";\r\nimport { fetchTableStructure, TableQuestion } from \"@/lib/api/table\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst EditTableQuestionModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType,\r\n  question,\r\n  contextId,\r\n}: {\r\n  showModal: boolean;\r\n  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;\r\n  contextType: ContextType;\r\n  question: Question;\r\n  contextId: number;\r\n}) => {\r\n  const queryClient = useQueryClient();\r\n  const dispatch = useDispatch();\r\n  const t = useTranslations();\r\n\r\n  // Fetch the table structure (columns and rows)\r\n  const {\r\n    data: rawTableData,\r\n    isLoading,\r\n    error,\r\n  } = useQuery<TableQuestion>({\r\n    queryKey: [\"tableQuestion\", question.id],\r\n    queryFn: async () => {\r\n      try {\r\n        const data = await fetchTableStructure(question.id);\r\n        return data;\r\n      } catch (err) {\r\n        console.error(\"Error fetching table data:\", err);\r\n        throw err;\r\n      }\r\n    },\r\n    enabled: showModal && question.id > 0 && question.inputType === \"table\",\r\n  });\r\n\r\n  // Process the table data to match the expected format for TableQuestionBuilder\r\n  const tableData = React.useMemo(() => {\r\n    if (!rawTableData) return null;\r\n\r\n    // Create a copy of the raw table data and ensure it matches the expected interface\r\n    const processedData = {\r\n      id: rawTableData.id,\r\n      label: rawTableData.label,\r\n      tableColumns: rawTableData.tableColumns.map((col) => ({\r\n        id: col.id,\r\n        columnName: col.columnName,\r\n        parentColumnId: col.parentColumnId,\r\n        childColumns:\r\n          col.childColumns?.map((child) => ({\r\n            id: child.id,\r\n            columnName: child.columnName,\r\n            parentColumnId: child.parentColumnId || col.id, // Ensure parentColumnId is set for child columns\r\n          })) || [],\r\n      })),\r\n      tableRows: rawTableData.tableRows.map((row) => ({\r\n        id: row.id,\r\n        rowsName: row.rowsName,\r\n      })),\r\n    };\r\n\r\n    return processedData;\r\n  }, [rawTableData]);\r\n\r\n  // Log when the modal is shown or hidden\r\n  useEffect(() => {\r\n    if (showModal) {\r\n    }\r\n  }, [showModal, question]);\r\n\r\n  const queryKey =\r\n    contextType === \"project\"\r\n      ? [\"questions\"]\r\n      : contextType === \"template\"\r\n        ? [\"templateQuestions\"]\r\n        : [\"questionBlockQuestions\"];\r\n\r\n  const handleTableUpdated = (tableId: number) => {\r\n\r\n    // Invalidate queries to refresh the data\r\n    queryClient.invalidateQueries({\r\n      queryKey,\r\n      exact: false,\r\n    });\r\n\r\n    queryClient.invalidateQueries({\r\n      queryKey: [\"tableQuestion\", question.id],\r\n      exact: true,\r\n    });\r\n\r\n    // Also invalidate form builder data for project contexts\r\n    if (contextType === \"project\") {\r\n      queryClient.invalidateQueries({ queryKey: [\"formBuilderData\", contextId] });\r\n    }\r\n\r\n    dispatch(\r\n      showNotification({\r\n        message: t('tableQuestionUpdated'),\r\n        type: \"success\",\r\n      })\r\n    );\r\n\r\n    // Add a small delay before closing the modal to ensure state is properly updated\r\n    setTimeout(() => {\r\n      setShowModal(false);\r\n    }, 100);\r\n  };\r\n\r\n  if (error) {\r\n    console.error(\"Error fetching table data:\", error);\r\n    dispatch(\r\n      showNotification({\r\n        message: t('tableDataLoadFailed'),\r\n        type: \"error\",\r\n      })\r\n    );\r\n  }\r\n\r\n  // Reference to the TableQuestionBuilder component\r\n  const tableBuilderRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  // Function to trigger the submit event on the TableQuestionBuilder\r\n  const handleSaveClick = () => {\r\n\r\n    // Try multiple approaches to find the table builder element\r\n    let tableBuilder = null;\r\n\r\n    // Approach 1: Direct reference through ref\r\n    if (tableBuilderRef.current) {\r\n      tableBuilder = tableBuilderRef.current;\r\n    }\r\n\r\n    // Approach 2: Find by class name in the document\r\n    if (!tableBuilder) {\r\n      const allTableBuilders = document.querySelectorAll(\r\n        \".table-question-builder\"\r\n      );\r\n\r\n      if (allTableBuilders.length > 0) {\r\n        tableBuilder = allTableBuilders[0];\r\n      }\r\n    }\r\n\r\n    // Approach 3: Find by class name in the container\r\n    if (!tableBuilder && tableBuilderRef.current) {\r\n      const containerTableBuilder = tableBuilderRef.current.querySelector(\r\n        \".table-question-builder\"\r\n      );\r\n      if (containerTableBuilder) {\r\n        tableBuilder = containerTableBuilder;\r\n      }\r\n    }\r\n\r\n    if (tableBuilder) {\r\n\r\n      // Create and dispatch the event\r\n      const submitEvent = new CustomEvent(\"submitTable\", {\r\n        bubbles: true, // Allow event to bubble up the DOM tree\r\n        cancelable: true, // Allow event to be canceled\r\n        detail: { timestamp: Date.now() }, // Add some detail to help with debugging\r\n      });\r\n\r\n      tableBuilder.dispatchEvent(submitEvent);\r\n    } else {\r\n      console.error(\r\n        \"Could not find any table builder element to dispatch event to\"\r\n      );\r\n\r\n      // As a last resort, try to find any element with a similar class\r\n      const anyElement = document.querySelector(\"[class*='table']\");\r\n      if (anyElement) {\r\n\r\n        const submitEvent = new CustomEvent(\"submitTable\", {\r\n          bubbles: true,\r\n          cancelable: true,\r\n          detail: { timestamp: Date.now(), isLastResort: true },\r\n        });\r\n\r\n        anyElement.dispatchEvent(submitEvent);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Function to handle confirmation before closing\r\n  const handleClose = () => {\r\n    // Show a confirmation dialog if there are unsaved changes\r\n    if (\r\n      window.confirm(\r\n        t('unsavedChangesCloseConfirm')\r\n      )\r\n    ) {\r\n      setShowModal(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={handleClose}\r\n      className=\"w-11/12 tablet:w-4/5 desktop:w-3/5\"\r\n      preventOutsideClick={true}\r\n    >\r\n      {isLoading && <LoadingOverlay />}\r\n\r\n      <div className=\"space-y-4\">\r\n        <h1 className=\"heading-text capitalize mb-4\">{t('editTableQuestion')}</h1>\r\n\r\n        {tableData ? (\r\n          <div\r\n            ref={tableBuilderRef}\r\n            className=\"table-question-builder-container\"\r\n          >\r\n            <TableQuestionBuilder\r\n              projectId={contextId}\r\n              isInModal={true}\r\n              isEditMode={true}\r\n              existingTableData={tableData as any}\r\n              onTableCreated={handleTableUpdated}\r\n            />\r\n\r\n            {/* Add Save button outside the TableQuestionBuilder */}\r\n            <div className=\"flex items-center justify-end space-x-4 mt-6\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => setShowModal(false)}\r\n                className=\"btn-outline\"\r\n              >\r\n                {t('cancel')}\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleSaveClick}\r\n                className=\"btn-primary\"\r\n              >\r\n                {t('saveChanges')}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : !isLoading ? (\r\n          <div className=\"p-4 text-center\">\r\n            <p className=\"text-red-500\">\r\n              {t('tableDataLoadErrorRetry')}\r\n            </p>\r\n          </div>\r\n        ) : null}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { EditTableQuestionModal };"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,yBAAyB,CAAC,EAC9B,SAAS,EACT,YAAY,EACZ,WAAW,EACX,QAAQ,EACR,SAAS,EAOV;;IACC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,+CAA+C;IAC/C,MAAM,EACJ,MAAM,YAAY,EAClB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAiB;QAC1B,UAAU;YAAC;YAAiB,SAAS,EAAE;SAAC;QACxC,OAAO;+CAAE;gBACP,IAAI;oBACF,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,EAAE;oBAClD,OAAO;gBACT,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,MAAM;gBACR;YACF;;QACA,SAAS,aAAa,SAAS,EAAE,GAAG,KAAK,SAAS,SAAS,KAAK;IAClE;IAEA,+EAA+E;IAC/E,MAAM,YAAY,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YAC9B,IAAI,CAAC,cAAc,OAAO;YAE1B,mFAAmF;YACnF,MAAM,gBAAgB;gBACpB,IAAI,aAAa,EAAE;gBACnB,OAAO,aAAa,KAAK;gBACzB,cAAc,aAAa,YAAY,CAAC,GAAG;iEAAC,CAAC,MAAQ,CAAC;4BACpD,IAAI,IAAI,EAAE;4BACV,YAAY,IAAI,UAAU;4BAC1B,gBAAgB,IAAI,cAAc;4BAClC,cACE,IAAI,YAAY,EAAE;6EAAI,CAAC,QAAU,CAAC;wCAChC,IAAI,MAAM,EAAE;wCACZ,YAAY,MAAM,UAAU;wCAC5B,gBAAgB,MAAM,cAAc,IAAI,IAAI,EAAE;oCAChD,CAAC;+EAAM,EAAE;wBACb,CAAC;;gBACD,WAAW,aAAa,SAAS,CAAC,GAAG;iEAAC,CAAC,MAAQ,CAAC;4BAC9C,IAAI,IAAI,EAAE;4BACV,UAAU,IAAI,QAAQ;wBACxB,CAAC;;YACH;YAEA,OAAO;QACT;oDAAG;QAAC;KAAa;IAEjB,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,WAAW,CACf;QACF;2CAAG;QAAC;QAAW;KAAS;IAExB,MAAM,WACJ,gBAAgB,YACZ;QAAC;KAAY,GACb,gBAAgB,aACd;QAAC;KAAoB,GACrB;QAAC;KAAyB;IAElC,MAAM,qBAAqB,CAAC;QAE1B,yCAAyC;QACzC,YAAY,iBAAiB,CAAC;YAC5B;YACA,OAAO;QACT;QAEA,YAAY,iBAAiB,CAAC;YAC5B,UAAU;gBAAC;gBAAiB,SAAS,EAAE;aAAC;YACxC,OAAO;QACT;QAEA,yDAAyD;QACzD,IAAI,gBAAgB,WAAW;YAC7B,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAmB;iBAAU;YAAC;QAC3E;QAEA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;YACf,SAAS,EAAE;YACX,MAAM;QACR;QAGF,iFAAiF;QACjF,WAAW;YACT,aAAa;QACf,GAAG;IACL;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;YACf,SAAS,EAAE;YACX,MAAM;QACR;IAEJ;IAEA,kDAAkD;IAClD,MAAM,kBAAkB,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAiB;IAErD,mEAAmE;IACnE,MAAM,kBAAkB;QAEtB,4DAA4D;QAC5D,IAAI,eAAe;QAEnB,2CAA2C;QAC3C,IAAI,gBAAgB,OAAO,EAAE;YAC3B,eAAe,gBAAgB,OAAO;QACxC;QAEA,iDAAiD;QACjD,IAAI,CAAC,cAAc;YACjB,MAAM,mBAAmB,SAAS,gBAAgB,CAChD;YAGF,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,eAAe,gBAAgB,CAAC,EAAE;YACpC;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,gBAAgB,gBAAgB,OAAO,EAAE;YAC5C,MAAM,wBAAwB,gBAAgB,OAAO,CAAC,aAAa,CACjE;YAEF,IAAI,uBAAuB;gBACzB,eAAe;YACjB;QACF;QAEA,IAAI,cAAc;YAEhB,gCAAgC;YAChC,MAAM,cAAc,IAAI,YAAY,eAAe;gBACjD,SAAS;gBACT,YAAY;gBACZ,QAAQ;oBAAE,WAAW,KAAK,GAAG;gBAAG;YAClC;YAEA,aAAa,aAAa,CAAC;QAC7B,OAAO;YACL,QAAQ,KAAK,CACX;YAGF,iEAAiE;YACjE,MAAM,aAAa,SAAS,aAAa,CAAC;YAC1C,IAAI,YAAY;gBAEd,MAAM,cAAc,IAAI,YAAY,eAAe;oBACjD,SAAS;oBACT,YAAY;oBACZ,QAAQ;wBAAE,WAAW,KAAK,GAAG;wBAAI,cAAc;oBAAK;gBACtD;gBAEA,WAAW,aAAa,CAAC;YAC3B;QACF;IACF;IAEA,iDAAiD;IACjD,MAAM,cAAc;QAClB,0DAA0D;QAC1D,IACE,OAAO,OAAO,CACZ,EAAE,gCAEJ;YACA,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;QACV,qBAAqB;;YAEpB,2BAAa,6LAAC,2IAAA,CAAA,iBAAc;;;;;0BAE7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgC,EAAE;;;;;;oBAE/C,0BACC,6LAAC;wBACC,KAAK;wBACL,WAAU;;0CAEV,6LAAC,yJAAA,CAAA,uBAAoB;gCACnB,WAAW;gCACX,WAAW;gCACX,YAAY;gCACZ,mBAAmB;gCACnB,gBAAgB;;;;;;0CAIlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDAET,EAAE;;;;;;kDAEL,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;+BAIP,CAAC,0BACH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;+BAGL;;;;;;;;;;;;;AAIZ;GAxPM;;QAagB,yLAAA,CAAA,iBAAc;QACjB,4JAAA,CAAA,cAAW;QAClB,yMAAA,CAAA,kBAAe;QAOrB,8KAAA,CAAA,WAAQ;;;KAtBR", "debugId": null}}, {"offset": {"line": 7435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/ConfirmationModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Modal from \"./Modal\";\r\n\r\nconst ConfirmationModal = ({\r\n  showModal,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  description,\r\n  confirmButtonText,\r\n  cancelButtonText,\r\n  confirmButtonClass,\r\n  children,\r\n}: {\r\n  showModal: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  description: React.ReactNode; // Accept ReactNode for flexible content\r\n  confirmButtonText: string;\r\n  cancelButtonText?: string;\r\n  confirmButtonClass?: string;\r\n  children?: React.ReactNode; // Additional content like warnings or icons\r\n}) => {\r\n  return (\r\n    <Modal\r\n      isOpen={showModal}\r\n      onClose={onClose}\r\n      className=\"p-6 rounded-md max-w-xl\"\r\n    >\r\n      <h2 className=\"text-lg font-semibold text-neutral-700\">{title}</h2>\r\n      <div className=\"text-neutral-700 mt-2\">{description}</div>\r\n      {children && <div className=\"mt-6 space-y-4\">{children}</div>}\r\n      <div className=\"flex justify-end gap-4 mt-6\">\r\n        <button className=\"btn-outline\" onClick={onClose} type=\"button\">\r\n          {cancelButtonText || \"Cancel\"}\r\n        </button>\r\n        <button\r\n          className={`font-medium rounded-md shadow-md text-neutral-100 flex items-center justify-center gap-2 px-4 py-2 active:scale-95 transition-all duration-300 ${confirmButtonClass}`}\r\n          onClick={onConfirm}\r\n          type=\"button\"\r\n        >\r\n          {confirmButtonText}\r\n        </button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport { ConfirmationModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,EAWT;IACC,qBACE,6LAAC,iIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAG,WAAU;0BAA0C;;;;;;0BACxD,6LAAC;gBAAI,WAAU;0BAAyB;;;;;;YACvC,0BAAY,6LAAC;gBAAI,WAAU;0BAAkB;;;;;;0BAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAO,WAAU;wBAAc,SAAS;wBAAS,MAAK;kCACpD,oBAAoB;;;;;;kCAEvB,6LAAC;wBACC,WAAW,CAAC,+IAA+I,EAAE,oBAAoB;wBACjL,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;;;AAKX;KA5CM", "debugId": null}}, {"offset": {"line": 7522, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/QuestionGroupModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { createQuestionGroup, updateQuestionGroup } from \"@/lib/api/question-groups\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { ContextType } from \"@/types\";\r\nimport { useTranslations } from \"next-intl\";\r\ninterface QuestionGroupModalProps {\r\n  showModal: boolean;\r\n  setShowModal: (show: boolean) => void;\r\n  contextType: ContextType; // Kept for future use if needed\r\n  contextId: number;\r\n  existingGroup?: {\r\n    id: number;\r\n    title: string;\r\n    order: number;\r\n  };\r\n  questions: Question[];\r\n  questionGroups?: Array<{\r\n    id: number;\r\n    title: string;\r\n  }>;\r\n}\r\n\r\nconst QuestionGroupModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  contextType,\r\n  contextId,\r\n  existingGroup,\r\n  questions,\r\n  questionGroups = [],\r\n}: QuestionGroupModalProps) => {\r\n  const [title, setTitle] = useState(\"\");\r\n  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);\r\n\r\n  const dispatch = useDispatch();\r\n  const queryClient = useQueryClient();\r\n\r\n  const t = useTranslations();\r\n\r\n  // Reset form when modal opens/closes or when editing a different group\r\n  useEffect(() => {\r\n    if (showModal) {\r\n      if (existingGroup) {\r\n        setTitle(existingGroup.title);\r\n\r\n        // Fetch questions that belong to this group\r\n        const questionsInGroup = questions.filter(q => q.questionGroupId === existingGroup.id);\r\n        setSelectedQuestionIds(questionsInGroup.map(q => q.id));\r\n      } else {\r\n        setTitle(\"\");\r\n        setSelectedQuestionIds([]);\r\n      }\r\n    }\r\n  }, [showModal, existingGroup, questions]);\r\n\r\n  // Make all questions available for selection, regardless of their group status\r\n  // This allows moving questions between groups\r\n  const availableQuestions = questions;\r\n\r\n  // Create group mutation\r\n  const createGroupMutation = useMutation({\r\n    mutationFn: createQuestionGroup,\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"questionGroups\", contextId] });\r\n      queryClient.invalidateQueries({ queryKey: [\"questions\", contextId] });\r\n      // Also invalidate form builder data for project contexts\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: [\"formBuilderData\", contextId] });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionGroupCreated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error creating question group:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionGroupCreationFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  // Update group mutation\r\n  const updateGroupMutation = useMutation({\r\n    mutationFn: updateQuestionGroup,\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"questionGroups\", contextId] });\r\n      queryClient.invalidateQueries({ queryKey: [\"questions\", contextId] });\r\n      // Also invalidate form builder data for project contexts\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: [\"formBuilderData\", contextId] });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionGroupUpdated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setShowModal(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(\"Error updating question group:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionGroupUpdateFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!title.trim()) {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupTitleRequired'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    if (existingGroup) {\r\n     \r\n\r\n      updateGroupMutation.mutate({\r\n        id: existingGroup.id,\r\n        title,\r\n        order: existingGroup.order,\r\n        selectedQuestionIds,\r\n      });\r\n    } else {\r\n     \r\n\r\n      createGroupMutation.mutate({\r\n        title,\r\n        order: questionGroups.length + 1, // Set order to be after existing groups\r\n        projectId: contextId,\r\n        selectedQuestionIds,\r\n      });\r\n    }\r\n  };\r\n\r\n  if (!showModal) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40\">\r\n      <div className=\"bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-auto p-4\">\r\n        <div className=\"flex justify-between items-center p-4 \">\r\n          <h2 className=\"text-xl font-semibold\">\r\n            {existingGroup ? t('editQuestionGroup') : t('createQuestionGroup')}\r\n          </h2>\r\n          <button\r\n            onClick={() => setShowModal(false)}\r\n            className=\"text-gray-500 hover:text-gray-700 cursor-pointer\"\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        <form onSubmit={handleSubmit} className=\"p-4\">\r\n          <div className=\"group label-input-group \">\r\n            <label htmlFor=\"title\">\r\n              {t('groupTitle')}\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"title\"\r\n              value={title}\r\n              onChange={(e) => setTitle(e.target.value)}\r\n              className=\" input-field w-full\"\r\n              placeholder={t('enterGroupTitle')}\r\n              required\r\n            />\r\n          </div>\r\n\r\n\r\n\r\n          <div className=\"mt-8 label-input-group\">\r\n            <label>\r\n              {t('selectQuestionsForGroup')}\r\n            </label>\r\n            <div className=\"border border-neutral-300 rounded-md p-2 max-h-60 overflow-y-auto\">\r\n              {availableQuestions.length > 0 ? (\r\n                availableQuestions.map((question) => {\r\n                  // Find the group this question belongs to (if any)\r\n                  const questionGroup = question.questionGroupId\r\n                    ? questionGroups.find(g => g.id === question.questionGroupId)\r\n                    : null;\r\n\r\n                  return (\r\n                    <div key={question.id} className=\"flex gap-2 items-center mb-3 p-2 border-b border-neutral-300\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id={`question-${question.id}`}\r\n                        checked={selectedQuestionIds.includes(question.id)}\r\n                        onChange={(e) => {\r\n                          if (e.target.checked) {\r\n                            setSelectedQuestionIds([...selectedQuestionIds, question.id]);\r\n                          } else {\r\n                            setSelectedQuestionIds(selectedQuestionIds.filter(id => id !== question.id));\r\n                          }\r\n                        }}\r\n                        className=\"mr-2 cursor-pointer w-5 h-5\"\r\n                      />\r\n                      <div>\r\n                        <label htmlFor={`question-${question.id}`} className=\"text-sm\">\r\n                          {question.label}\r\n                        </label>\r\n\r\n                        {/* Show which group the question belongs to */}\r\n                        {questionGroup && questionGroup.id !== (existingGroup?.id || -1) && (\r\n                          <div className=\"text-xs text-neutral-700 mt-1\">\r\n                            {t('currentlyInGroup')}: <span className=\"font-medium text-amber-600\">{questionGroup.title}</span>\r\n                            <span className=\"ml-1 text-neutral-700\">({t('willBeMovedToThisGroup')})</span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })\r\n              ) : (\r\n                <p className=\"text-gray-500 text-sm p-2\">\r\n                  {t('noAvailableQuestions')}\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex justify-end space-x-2 mt-4\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setShowModal(false)}\r\n              className=\"btn-outline\"\r\n            >\r\n              {t('cancel')}\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"px-4 py-2 btn-primary\"\r\n              disabled={createGroupMutation.isPending || updateGroupMutation.isPending}\r\n            >\r\n              {createGroupMutation.isPending || updateGroupMutation.isPending\r\n                ? t('saving')\r\n                : existingGroup\r\n                ? t('updateGroup')\r\n                : t('createGroup')}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { QuestionGroupModal };"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAGA;;;AAVA;;;;;;;;AA4BA,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,YAAY,EACZ,WAAW,EACX,SAAS,EACT,aAAa,EACb,SAAS,EACT,iBAAiB,EAAE,EACK;;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3E,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,uEAAuE;IACvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,WAAW;gBACb,IAAI,eAAe;oBACjB,SAAS,cAAc,KAAK;oBAE5B,4CAA4C;oBAC5C,MAAM,mBAAmB,UAAU,MAAM;yEAAC,CAAA,IAAK,EAAE,eAAe,KAAK,cAAc,EAAE;;oBACrF,uBAAuB,iBAAiB,GAAG;wDAAC,CAAA,IAAK,EAAE,EAAE;;gBACvD,OAAO;oBACL,SAAS;oBACT,uBAAuB,EAAE;gBAC3B;YACF;QACF;uCAAG;QAAC;QAAW;QAAe;KAAU;IAExC,+EAA+E;IAC/E,8CAA8C;IAC9C,MAAM,qBAAqB;IAE3B,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;mEAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB;qBAAU;gBAAC;gBACxE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAU;gBAAC;gBACnE,yDAAyD;gBACzD,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAmB;yBAAU;oBAAC;gBAC3E;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,aAAa;YACf;;QACA,OAAO;mEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;mEAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB;qBAAU;gBAAC;gBACxE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAU;gBAAC;gBACnE,yDAAyD;gBACzD,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAmB;yBAAU;oBAAC;gBAC3E;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,aAAa;YACf;;QACA,OAAO;mEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,EAAE;gBACX,MAAM;YACR;YAEF;QACF;QAEA,IAAI,eAAe;YAGjB,oBAAoB,MAAM,CAAC;gBACzB,IAAI,cAAc,EAAE;gBACpB;gBACA,OAAO,cAAc,KAAK;gBAC1B;YACF;QACF,OAAO;YAGL,oBAAoB,MAAM,CAAC;gBACzB;gBACA,OAAO,eAAe,MAAM,GAAG;gBAC/B,WAAW;gBACX;YACF;QACF;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,gBAAgB,EAAE,uBAAuB,EAAE;;;;;;sCAE9C,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;8CACZ,EAAE;;;;;;8CAEL,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAa,EAAE;oCACf,QAAQ;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CACE,EAAE;;;;;;8CAEL,6LAAC;oCAAI,WAAU;8CACZ,mBAAmB,MAAM,GAAG,IAC3B,mBAAmB,GAAG,CAAC,CAAC;wCACtB,mDAAmD;wCACnD,MAAM,gBAAgB,SAAS,eAAe,GAC1C,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,eAAe,IAC1D;wCAEJ,qBACE,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;oDACC,MAAK;oDACL,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oDAC7B,SAAS,oBAAoB,QAAQ,CAAC,SAAS,EAAE;oDACjD,UAAU,CAAC;wDACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4DACpB,uBAAuB;mEAAI;gEAAqB,SAAS,EAAE;6DAAC;wDAC9D,OAAO;4DACL,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,KAAM,OAAO,SAAS,EAAE;wDAC5E;oDACF;oDACA,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAM,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;4DAAE,WAAU;sEAClD,SAAS,KAAK;;;;;;wDAIhB,iBAAiB,cAAc,EAAE,KAAK,CAAC,eAAe,MAAM,CAAC,CAAC,mBAC7D,6LAAC;4DAAI,WAAU;;gEACZ,EAAE;gEAAoB;8EAAE,6LAAC;oEAAK,WAAU;8EAA8B,cAAc,KAAK;;;;;;8EAC1F,6LAAC;oEAAK,WAAU;;wEAAwB;wEAAE,EAAE;wEAA0B;;;;;;;;;;;;;;;;;;;;2CAvBpE,SAAS,EAAE;;;;;oCA6BzB,mBAEA,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;;;;;;sCAMX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,UAAU,oBAAoB,SAAS,IAAI,oBAAoB,SAAS;8CAEvE,oBAAoB,SAAS,IAAI,oBAAoB,SAAS,GAC3D,EAAE,YACF,gBACA,EAAE,iBACF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GA/OM;;QAYa,4JAAA,CAAA,cAAW;QACR,yLAAA,CAAA,iBAAc;QAExB,yMAAA,CAAA,kBAAe;QAuBG,iLAAA,CAAA,cAAW;QA6BX,iLAAA,CAAA,cAAW;;;KAnEnC", "debugId": null}}, {"offset": {"line": 7941, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/modals/DeleteQuestionGroupModal.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { X } from \"lucide-react\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface DeleteQuestionGroupModalProps {\r\n  showModal: boolean;\r\n  setShowModal: (show: boolean) => void;\r\n  onConfirmDelete: () => void;\r\n  onConfirmDeleteWithQuestions: () => void;\r\n  isDeleting: boolean;\r\n}\r\n\r\nconst DeleteQuestionGroupModal = ({\r\n  showModal,\r\n  setShowModal,\r\n  onConfirmDelete,\r\n  onConfirmDeleteWithQuestions,\r\n  isDeleting,\r\n}: DeleteQuestionGroupModalProps) => {\r\n  const t = useTranslations();\r\n  if (!showModal) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40\">\r\n      <div className=\"bg-white rounded-lg shadow-lg w-full max-w-md\">\r\n        <div className=\"flex justify-between items-center p-4 border-b\">\r\n          <h2 className=\"text-xl font-semibold\">{t('deleteQuestionGroup')}</h2>\r\n          <button\r\n            onClick={() => setShowModal(false)}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n            disabled={isDeleting}\r\n          >\r\n            <X size={20} />\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-4\">\r\n          <p className=\"mb-4\">\r\n            {t('deleteQuestionGroupPrompt')}\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            <button\r\n              onClick={onConfirmDelete}\r\n              className=\"w-full px-4 py-2 bg-amber-500 text-white rounded-md hover:bg-amber-600\"\r\n              disabled={isDeleting}\r\n            >\r\n              {isDeleting ? t('deleting') : t('deleteGroupOnly')}\r\n            </button>\r\n            <p className=\"text-sm text-gray-600 mb-4\">\r\n              {t('deleteGroupKeepQuestions')}\r\n            </p>\r\n\r\n            <button\r\n              onClick={onConfirmDeleteWithQuestions}\r\n              className=\"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600\"\r\n              disabled={isDeleting}\r\n            >\r\n              {isDeleting ? t('deleting') : t('deleteGroupQuestions')}\r\n            </button>\r\n            <p className=\"text-sm text-gray-600 mb-4\">\r\n              {t('deleteGroupAndQuestions')}\r\n            </p>\r\n\r\n            <button\r\n              onClick={() => setShowModal(false)}\r\n              className=\"w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n              disabled={isDeleting}\r\n            >\r\n              {t('cancel')}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { DeleteQuestionGroupModal };\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAcA,MAAM,2BAA2B,CAAC,EAChC,SAAS,EACT,YAAY,EACZ,eAAe,EACf,4BAA4B,EAC5B,UAAU,EACoB;;IAC9B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IACxB,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyB,EAAE;;;;;;sCACzC,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAGL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,UAAU;8CAET,aAAa,EAAE,cAAc,EAAE;;;;;;8CAElC,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAGL,6LAAC;oCACC,SAAS;oCACT,WAAU;oCACV,UAAU;8CAET,aAAa,EAAE,cAAc,EAAE;;;;;;8CAElC,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAGL,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,UAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GAhEM;;QAOM,yMAAA,CAAA,kBAAe;;;KAPrB", "debugId": null}}, {"offset": {"line": 8096, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-builder/LibraryQuestionsSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchQuestionBlockQuestions } from \"@/lib/api/form-builder\";\r\nimport { Question } from \"@/types/formBuilder\";\r\nimport { Search, X } from \"lucide-react\";\r\nimport Spinner from \"../general/Spinner\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\ninterface LibraryQuestionsSidebarProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onAddQuestions: (questions: Question[]) => void;\r\n}\r\n\r\nconst LibraryQuestionsSidebar: React.FC<LibraryQuestionsSidebarProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  onAddQuestions,\r\n}) => {\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([]);\r\n  const [expandDetails, setExpandDetails] = useState(true);\r\n\r\n  const t = useTranslations();\r\n\r\n  // Fetch question block questions\r\n  const {\r\n    data: libraryQuestions,\r\n    isLoading,\r\n    isError,\r\n  } = useQuery<Question[]>({\r\n    queryKey: [\"libraryQuestions\"],\r\n    queryFn: () => fetchQuestionBlockQuestions(),\r\n    enabled: isOpen, // Only fetch when sidebar is open\r\n  });\r\n\r\n  // Filter questions based on search term\r\n  const filteredQuestions = libraryQuestions\r\n    ? libraryQuestions.filter((question) =>\r\n        question.label.toLowerCase().includes(searchTerm.toLowerCase())\r\n      )\r\n    : [];\r\n\r\n  // Handle question selection\r\n  const toggleQuestionSelection = (question: Question) => {\r\n    if (selectedQuestions.some((q) => q.id === question.id)) {\r\n      setSelectedQuestions(selectedQuestions.filter((q) => q.id !== question.id));\r\n    } else {\r\n      setSelectedQuestions([...selectedQuestions, question]);\r\n    }\r\n  };\r\n\r\n  // Add selected questions to the form\r\n  const handleAddQuestions = () => {\r\n    onAddQuestions(selectedQuestions);\r\n    setSelectedQuestions([]);\r\n    onClose();\r\n  };\r\n\r\n  // Reset selections when sidebar closes\r\n  useEffect(() => {\r\n    if (!isOpen) {\r\n      setSelectedQuestions([]);\r\n      setSearchTerm(\"\");\r\n    }\r\n  }, [isOpen]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex justify-end\">\r\n      {/* Backdrop */}\r\n      <div\r\n        className=\"absolute inset-0 bg-neutral-900/50\"\r\n        onClick={onClose}\r\n      ></div>\r\n\r\n      {/* Sidebar */}\r\n      <div className=\"relative w-full max-w-md bg-neutral-50 h-full overflow-auto shadow-xl\">\r\n        <div className=\"p-4 border-b border-neutral-200\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <h2 className=\"text-xl font-bold\">{t('searchLibrary')}</h2>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"self-end cursor-pointer text-neutral-700 hover:text-neutral-900 active:scale-90 transition-all duration-300\"\r\n            >\r\n              <X size={20} />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Search input */}\r\n          <div className=\"relative mb-4\">\r\n            <input\r\n              type=\"text\"\r\n              placeholder={t('searchPlaceholder')}\r\n              className=\"input-field w-full p-2 pl-10\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n            />\r\n            <Search className=\"absolute left-3 top-2.5 \" size={18} />\r\n          </div>\r\n\r\n          {/* Tags filter (placeholder) */}\r\n          {/* <div className=\"mb-4\">\r\n            <button className=\"w-full p-2 text-left border border-gray-300 rounded-md flex justify-between items-center\">\r\n              <span className=\"text-gray-500\">Search Tags</span>\r\n              <span>▼</span>\r\n            </button>\r\n          </div> */}\r\n\r\n          {/* Collection filter (placeholder) */}\r\n          {/* <div className=\"mb-4\">\r\n            <button className=\"w-full p-2 text-left border border-gray-300 rounded-md flex justify-between items-center\">\r\n              <span className=\"text-gray-500\">Select Collection Name</span>\r\n              <span>▼</span>\r\n            </button>\r\n          </div> */}\r\n\r\n          {/* Results count and expand toggle */}\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <span>\r\n              {filteredQuestions.length} {t('asset')}{filteredQuestions.length !== 1 ? t('s') : \"\"} {t('found')}\r\n            </span>\r\n            <label className=\"flex items-center\">\r\n              <input\r\n                type=\"checkbox\"\r\n                checked={expandDetails}\r\n                onChange={() => setExpandDetails(!expandDetails)}\r\n                className=\"mr-2\"\r\n              />\r\n              {t('expandDetails')}\r\n            </label>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Question list */}\r\n        <div className=\"p-4\">\r\n          {isLoading ? (\r\n            <div className=\"flex justify-center p-8\">\r\n              <Spinner />\r\n            </div>\r\n          ) : isError ? (\r\n            <div className=\"text-red-500 p-4 text-center\">\r\n              {t('libraryLoadError')}\r\n            </div>\r\n          ) : filteredQuestions.length === 0 ? (\r\n            <div className=\"text-neutral-700 p-4 text-center\">\r\n              {t('noQuestionsFound')}\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-2\">\r\n              {filteredQuestions.map((question) => (\r\n                <div\r\n                  key={question.id}\r\n                  className=\"border border-neutral-500 rounded-md p-3\"\r\n                >\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={selectedQuestions.some((q) => q.id === question.id)}\r\n                      onChange={() => toggleQuestionSelection(question)}\r\n                      className=\"mr-3 h-5 w-5 cursor-pointer\"\r\n                    />\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"font-medium\">{question.label}</div>\r\n                      {expandDetails && (\r\n                        <div className=\"text-sm text-neutral-700 mt-1\">\r\n                          {t('type')}: {String(question.inputType)}\r\n                          {question.hint && <div>{t('hint')}: {question.hint}</div>}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Action buttons */}\r\n        <div className=\"border-t border-gray-200 p-4 sticky bottom-0 bg-neutral-50\">\r\n          <div className=\"flex justify-between\">\r\n            <button\r\n              onClick={onClose}\r\n              className=\"btn-outline\"\r\n            >\r\n              {t('cancel')}\r\n            </button>\r\n            <button\r\n              onClick={handleAddQuestions}\r\n              disabled={selectedQuestions.length === 0}\r\n              className={`px-4 py-2 rounded-md ${\r\n                selectedQuestions.length > 0\r\n                  ? \"btn-primary\"\r\n                  : \"bg-gray-200 text-gray-500 pointer-events-none\"\r\n              }`}\r\n            >\r\n              {t('addSelected')} ({selectedQuestions.length})\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LibraryQuestionsSidebar;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AACA;AACA;;;AARA;;;;;;;AAgBA,MAAM,0BAAkE,CAAC,EACvE,MAAM,EACN,OAAO,EACP,cAAc,EACf;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,iCAAiC;IACjC,MAAM,EACJ,MAAM,gBAAgB,EACtB,SAAS,EACT,OAAO,EACR,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACvB,UAAU;YAAC;SAAmB;QAC9B,OAAO;gDAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;;QACzC,SAAS;IACX;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,mBACtB,iBAAiB,MAAM,CAAC,CAAC,WACvB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAE9D,EAAE;IAEN,4BAA4B;IAC5B,MAAM,0BAA0B,CAAC;QAC/B,IAAI,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE,GAAG;YACvD,qBAAqB,kBAAkB,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;QAC3E,OAAO;YACL,qBAAqB;mBAAI;gBAAmB;aAAS;QACvD;IACF;IAEA,qCAAqC;IACrC,MAAM,qBAAqB;QACzB,eAAe;QACf,qBAAqB,EAAE;QACvB;IACF;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,CAAC,QAAQ;gBACX,qBAAqB,EAAE;gBACvB,cAAc;YAChB;QACF;4CAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqB,EAAE;;;;;;kDACrC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAKb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAa,EAAE;wCACf,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;kDAE/C,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;wCAA2B,MAAM;;;;;;;;;;;;0CAoBrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CACE,kBAAkB,MAAM;4CAAC;4CAAE,EAAE;4CAAU,kBAAkB,MAAM,KAAK,IAAI,EAAE,OAAO;4CAAG;4CAAE,EAAE;;;;;;;kDAE3F,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,IAAM,iBAAiB,CAAC;gDAClC,WAAU;;;;;;4CAEX,EAAE;;;;;;;;;;;;;;;;;;;kCAMT,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,UAAO;;;;;;;;;mCAER,wBACF,6LAAC;4BAAI,WAAU;sCACZ,EAAE;;;;;mCAEH,kBAAkB,MAAM,KAAK,kBAC/B,6LAAC;4BAAI,WAAU;sCACZ,EAAE;;;;;iDAGL,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;oCAEC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,SAAS,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,EAAE;gDAC3D,UAAU,IAAM,wBAAwB;gDACxC,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAe,SAAS,KAAK;;;;;;oDAC3C,+BACC,6LAAC;wDAAI,WAAU;;4DACZ,EAAE;4DAAQ;4DAAG,OAAO,SAAS,SAAS;4DACtC,SAAS,IAAI,kBAAI,6LAAC;;oEAAK,EAAE;oEAAQ;oEAAG,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;mCAfrD,SAAS,EAAE;;;;;;;;;;;;;;;kCA2B1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC;oCACC,SAAS;oCACT,UAAU,kBAAkB,MAAM,KAAK;oCACvC,WAAW,CAAC,qBAAqB,EAC/B,kBAAkB,MAAM,GAAG,IACvB,gBACA,iDACJ;;wCAED,EAAE;wCAAe;wCAAG,kBAAkB,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;GA9LM;;QASM,yMAAA,CAAA,kBAAe;QAOrB,8KAAA,CAAA,WAAQ;;;KAhBR;uCAgMS", "debugId": null}}, {"offset": {"line": 8466, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/components/form-builder/FormBuilder.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport {\r\n  Dnd<PERSON>ontext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent,\r\n} from \"@dnd-kit/core\";\r\nimport {\r\n  SortableContext,\r\n  verticalListSortingStrategy,\r\n  arrayMove,\r\n} from \"@dnd-kit/sortable\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { QuestionItem } from \"@/components/form-builder/QuestionItem\";\r\nimport { QuestionGroupItem } from \"@/components/form-builder/QuestionGroupItem\";\r\nimport {\r\n  Eye,\r\n  FileDown,\r\n  FileUp,\r\n  PlusCircle,\r\n  Settings,\r\n  BookOpen,\r\n  FolderPlus,\r\n} from \"lucide-react\";\r\nimport { AddQuestionModal } from \"../modals/AddQuestionModal\";\r\nimport { EditQuestionModal } from \"../modals/EditQuestionModal\";\r\nimport { EditTableQuestionModal } from \"../modals/EditTableQuestionModal\";\r\nimport { ConfirmationModal } from \"../modals/ConfirmationModal\";\r\nimport { QuestionGroupModal } from \"../modals/QuestionGroupModal\";\r\nimport { DeleteQuestionGroupModal } from \"../modals/DeleteQuestionGroupModal\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport {\r\n  deleteQuestion,\r\n  duplicateQuestion,\r\n  addQuestion,\r\n  updateQuestionPositions,\r\n  fetchFormBuilderData,\r\n  fetchTemplateQuestions,\r\n  fetchQuestionBlockQuestions,\r\n} from \"@/lib/api/form-builder\";\r\nimport {\r\n  fetchQuestionGroups,\r\n  createQuestionGroup,\r\n  updateQuestionGroup,\r\n  deleteQuestionGroup,\r\n  deleteQuestionAndGroup,\r\n  moveQuestionBetweenGroups,\r\n  moveGroupInsideGroup,\r\n  removeGroupFromParent,\r\n  updateGroupPositions,\r\n} from \"@/lib/api/question-groups\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { showNotification } from \"@/redux/slices/notificationSlice\";\r\nimport { ContextType } from \"@/types\";\r\nimport { LoadingOverlay } from \"../general/LoadingOverlay\";\r\nimport LibraryQuestionsSidebar from \"./LibraryQuestionsSidebar\";\r\nimport { ProjectPermissionFlags } from \"@/types\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nconst FormBuilder = ({\r\n  setIsPreviewMode,\r\n  contextType,\r\n  contextId,\r\n  permissions,\r\n}: {\r\n  setIsPreviewMode: React.Dispatch<React.SetStateAction<boolean>>;\r\n  contextType: ContextType;\r\n  contextId: number;\r\n  permissions: ProjectPermissionFlags;\r\n}) => {\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor, {\r\n      activationConstraint: {\r\n        distance: 8,\r\n      },\r\n    }),\r\n    useSensor(KeyboardSensor)\r\n  );\r\n\r\n  const t = useTranslations();\r\n\r\n  const editFormPermission = permissions.manageProject || permissions.editForm;\r\n\r\n  // Question modals state\r\n  const [showAddQuestionModal, setShowAddQuestionModal] =\r\n    useState<boolean>(false);\r\n  const [showEditQuestionModal, setShowEditQuestionModal] =\r\n    useState<boolean>(false);\r\n  const [showEditTableQuestionModal, setShowEditTableQuestionModal] =\r\n    useState<boolean>(false);\r\n  const [showLibrarySidebar, setShowLibrarySidebar] = useState<boolean>(false);\r\n  const [isAddingQuestions, setIsAddingQuestions] = useState<boolean>(false);\r\n  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);\r\n  const [showDeleteConfirmationModal, setShowDeleteConfirmationModal] =\r\n    useState<boolean>(false);\r\n\r\n  // Question group modals state\r\n  const [showAddGroupModal, setShowAddGroupModal] = useState<boolean>(false);\r\n  const [showEditGroupModal, setShowEditGroupModal] = useState<boolean>(false);\r\n  const [showDeleteGroupModal, setShowDeleteGroupModal] =\r\n    useState<boolean>(false);\r\n  const [currentGroupId, setCurrentGroupId] = useState<number | null>(null);\r\n\r\n  // Question selection state\r\n  const [selectedQuestionIds, setSelectedQuestionIds] = useState<number[]>([]);\r\n  const [isEditingGroupName, setIsEditingGroupName] = useState<number | null>(\r\n    null\r\n  );\r\n  const [editingGroupName, setEditingGroupName] = useState<string>(\"\");\r\n  const [selectionMode, setSelectionMode] = useState<boolean>(false);\r\n\r\n  // Subgroup creation state\r\n  const [showSubgroupNameModal, setShowSubgroupNameModal] = useState(false);\r\n  const [subgroupParentId, setSubgroupParentId] = useState<number | null>(null);\r\n  const [subgroupSelectedQuestions, setSubgroupSelectedQuestions] = useState<number[]>([]);\r\n  const [subgroupName, setSubgroupName] = useState(\"\");\r\n\r\n  // State for tracking when questions are being added\r\n  const [isProcessingGroup, setIsProcessingGroup] = useState(false);\r\n\r\n  const dispatch = useDispatch();\r\n  const queryClient = useQueryClient();\r\n\r\n  // Memoize query keys to prevent recreation on every render\r\n  const questionsQueryKey = React.useMemo(() => {\r\n    return contextType === \"project\"\r\n      ? [\"questions\", contextId]\r\n      : contextType === \"template\"\r\n      ? [\"templateQuestions\", contextId]\r\n      : [\"questionBlockQuestions\", contextId];\r\n  }, [contextType, contextId]);\r\n\r\n  const groupsQueryKey = React.useMemo(() => [\"questionGroups\", contextId], [contextId]);\r\n  const formBuilderDataQueryKey = React.useMemo(() => [\"formBuilderData\", contextId], [contextId]);\r\n\r\n  // Fetch form builder data (unified questions and groups) for projects\r\n  const { data: formBuilderData, isLoading: isLoadingFormData, isError: isFormDataError } = useQuery({\r\n    queryKey: formBuilderDataQueryKey,\r\n    queryFn: () => fetchFormBuilderData({ projectId: contextId }),\r\n    enabled: contextType === \"project\",\r\n  });\r\n\r\n  // Fallback API calls for non-project contexts\r\n  const { data: fallbackQuestions = [], isLoading: isLoadingFallback, isError: isFallbackError } = useQuery<Question[]>({\r\n    queryKey: questionsQueryKey,\r\n    queryFn: () => {\r\n      if (contextType === \"template\") {\r\n        return fetchTemplateQuestions({ templateId: contextId });\r\n      } else if (contextType === \"questionBlock\") {\r\n        return fetchQuestionBlockQuestions();\r\n      }\r\n      return [];\r\n    },\r\n    enabled: contextType !== \"project\",\r\n  });\r\n\r\n  // Extract questions and groups from the unified data\r\n  const questions: Question[] = React.useMemo(() => {\r\n    if (contextType === \"project\") {\r\n      if (!formBuilderData?.items) {\r\n        return [];\r\n      }\r\n\r\n      // Extract questions from the items array\r\n      const extractedQuestions: Question[] = [];\r\n\r\n      formBuilderData.items.forEach((item: any) => {\r\n        if (item.type === \"question\") {\r\n          extractedQuestions.push(item);\r\n        } else if (item.type === \"group\" && item.questions) {\r\n          // Add questions from groups\r\n          item.questions.forEach((question: any) => {\r\n            extractedQuestions.push(question);\r\n          });\r\n        }\r\n      });\r\n\r\n      return extractedQuestions;\r\n    } else {\r\n      // For non-project contexts, use fallback questions\r\n      return fallbackQuestions;\r\n    }\r\n  }, [formBuilderData, contextType, fallbackQuestions]);\r\n\r\n  // Extract question groups from the unified data\r\n  const questionGroups: QuestionGroup[] = React.useMemo(() => {\r\n    if (contextType !== \"project\" || !formBuilderData?.items) {\r\n      return [];\r\n    }\r\n\r\n    return formBuilderData.items\r\n      .filter((item: any) => item.type === \"group\")\r\n      .map((item: any) => ({\r\n        ...item,\r\n        question: item.questions || []\r\n      }));\r\n  }, [formBuilderData, contextType]);\r\n\r\n  // Fallback to separate API calls for non-project contexts\r\n  const { data: fallbackQuestionGroups = [] } = useQuery({\r\n    queryKey: groupsQueryKey,\r\n    queryFn: () => fetchQuestionGroups({ projectId: contextId }),\r\n    enabled: contextType === \"project\" && !formBuilderData, // Only as fallback\r\n  });\r\n\r\n  // Group questions by their group ID and sort by position within each group\r\n  const groupedQuestions = React.useMemo(() => {\r\n    return questionGroups.reduce(\r\n      (acc: Record<number, Question[]>, group: QuestionGroup) => {\r\n        acc[group.id] = questions\r\n          .filter((q) => q.questionGroupId === group.id)\r\n          .sort((a, b) => a.position - b.position);\r\n        return acc;\r\n      },\r\n      {} as Record<number, Question[]>\r\n    );\r\n  }, [questionGroups, questions]);\r\n\r\n  // Memoize ungrouped questions to prevent unnecessary recalculations\r\n  const ungroupedQuestions = React.useMemo(() => {\r\n    return questions.filter((q) => !q.questionGroupId);\r\n  }, [questions]);\r\n\r\n  // Build nested group structure\r\n  const buildNestedGroups = React.useCallback((groups: QuestionGroup[], questionsData: Question[]): QuestionGroup[] => {\r\n    const groupMap = new Map<number, QuestionGroup>();\r\n\r\n    // Create a map of all groups with their subGroups and questions initialized\r\n    groups.forEach(group => {\r\n      // Get questions for this group\r\n      const groupQuestions = questionsData\r\n        .filter((q) => q.questionGroupId === group.id)\r\n        .sort((a, b) => a.position - b.position);\r\n\r\n      groupMap.set(group.id, {\r\n        ...group,\r\n        subGroups: [],\r\n        question: groupQuestions\r\n      });\r\n    });\r\n\r\n    // Build the nested structure\r\n    const topLevelGroups: QuestionGroup[] = [];\r\n    groups.forEach(group => {\r\n      const groupWithSubGroups = groupMap.get(group.id)!;\r\n\r\n      if (group.parentGroupId) {\r\n        // This is a child group, add it to its parent's subGroups\r\n        const parentGroup = groupMap.get(group.parentGroupId);\r\n        if (parentGroup) {\r\n          parentGroup.subGroups = parentGroup.subGroups || [];\r\n          parentGroup.subGroups.push(groupWithSubGroups);\r\n        }\r\n      } else {\r\n        // This is a top-level group\r\n        topLevelGroups.push(groupWithSubGroups);\r\n      }\r\n    });\r\n\r\n    return topLevelGroups;\r\n  }, []);\r\n\r\n  // Rebuild nested groups whenever questions or questionGroups change\r\n  const nestedQuestionGroups = React.useMemo(() => {\r\n    return buildNestedGroups(questionGroups, questions);\r\n  }, [questionGroups, questions, buildNestedGroups]);\r\n\r\n  // Create a unified list of form items (groups and individual questions) for dynamic ordering\r\n  const createUnifiedFormItems = React.useCallback((\r\n    nestedGroups: QuestionGroup[],\r\n    ungrouped: Question[],\r\n    allQuestions: Question[],\r\n    context: ContextType\r\n  ) => {\r\n    const items: Array<{\r\n      type: 'group' | 'question';\r\n      data: QuestionGroup | Question;\r\n      order: number;\r\n      originalPosition?: number; // Track original position for better sorting\r\n    }> = [];\r\n\r\n    // Only add question groups for projects (not templates or question blocks)\r\n    // Only show top-level groups (groups without a parent) in the main list\r\n    if (context === \"project\") {\r\n      nestedGroups.forEach((group: QuestionGroup) => {\r\n        // For groups, find the minimum position of questions in the group\r\n        // This ensures the group appears where the first question was originally positioned\r\n        const groupQuestions = allQuestions\r\n          .filter(q => q.questionGroupId === group.id)\r\n          .sort((a, b) => a.position - b.position);\r\n        const minQuestionPosition = groupQuestions.length > 0\r\n          ? Math.min(...groupQuestions.map(q => q.position))\r\n          : group.order;\r\n\r\n        items.push({\r\n          type: 'group',\r\n          data: group,\r\n          order: minQuestionPosition, // Use the position of the first question in the group\r\n          originalPosition: minQuestionPosition\r\n        });\r\n      });\r\n    }\r\n\r\n    // Add ungrouped questions (or all questions if not in project context)\r\n    const questionsToAdd = context === \"project\" ? ungrouped : allQuestions;\r\n    questionsToAdd.forEach((question: Question) => {\r\n      items.push({\r\n        type: 'question',\r\n        data: question,\r\n        order: question.position,\r\n        originalPosition: question.position\r\n      });\r\n    });\r\n\r\n    // Sort by order/position, with a secondary sort by type to ensure consistent ordering\r\n    // when groups and questions have the same position\r\n    return items.sort((a, b) => {\r\n      if (a.order === b.order) {\r\n        // If positions are equal, prioritize based on original question positions\r\n        // This helps maintain the original flow when grouping questions\r\n        return (a.originalPosition || a.order) - (b.originalPosition || b.order);\r\n      }\r\n      return a.order - b.order;\r\n    });\r\n  }, []);\r\n\r\n  // Memoize unified form items to prevent unnecessary recalculations\r\n  const unifiedFormItems = React.useMemo(() => {\r\n    return createUnifiedFormItems(nestedQuestionGroups, ungroupedQuestions, questions, contextType);\r\n  }, [nestedQuestionGroups, ungroupedQuestions, questions, contextType, createUnifiedFormItems]);\r\n\r\n  // Force refresh of question groups if they're empty but we have questions with questionGroupId\r\n  // Use a ref to track if we've already triggered the invalidation to prevent infinite loops\r\n  const hasTriggeredGroupRefresh = React.useRef(false);\r\n\r\n  React.useEffect(() => {\r\n    const hasGroupedQuestions = questions.some(\r\n      (q) => q.questionGroupId !== null && q.questionGroupId !== undefined\r\n    );\r\n    const hasNoGroups = questionGroups.length === 0;\r\n\r\n    if (hasGroupedQuestions && hasNoGroups && contextType === \"project\" && !hasTriggeredGroupRefresh.current) {\r\n      hasTriggeredGroupRefresh.current = true;\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n    }\r\n\r\n    // Reset the flag when groups are loaded\r\n    if (questionGroups.length > 0) {\r\n      hasTriggeredGroupRefresh.current = false;\r\n    }\r\n  }, [questions, questionGroups, contextType, queryClient, groupsQueryKey]);\r\n\r\n  // Question mutations\r\n  const deleteQuestionMutation = useMutation({\r\n    mutationFn: deleteQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionDeleted'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionDeleteFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n    onSettled: () => {\r\n      setShowDeleteConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  const duplicateQuestionMutation = useMutation({\r\n    mutationFn: duplicateQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionDuplicated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionDuplicateFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n    onSettled: () => {\r\n      setShowDeleteConfirmationModal(false);\r\n    },\r\n  });\r\n\r\n  // Question group mutations\r\n  const deleteGroupMutation = useMutation({\r\n    mutationFn: deleteQuestionGroup,\r\n    onSuccess: (data, variables) => {\r\n\r\n      // Find the group that was deleted\r\n      const deletedGroup = questionGroups.find(\r\n        (g: QuestionGroup) => g.id === variables.id\r\n      );\r\n\r\n      if (deletedGroup) {\r\n        // Find questions that belonged to this group\r\n        const questionsInGroup = questions.filter(\r\n          (q) => q.questionGroupId === deletedGroup.id\r\n        );\r\n\r\n        if (questionsInGroup.length > 0) {\r\n          // Update the local state to mark these questions as ungrouped\r\n          const updatedQuestions = questions.map((q) =>\r\n            q.questionGroupId === deletedGroup.id\r\n              ? { ...q, questionGroupId: undefined }\r\n              : q\r\n          );\r\n\r\n          // Update the questions in the local state\r\n          queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n\r\n          // Remove the deleted group from the local state\r\n          const updatedGroups = questionGroups.filter(\r\n            (g: QuestionGroup) => g.id !== variables.id\r\n          );\r\n          queryClient.setQueryData(groupsQueryKey, updatedGroups);\r\n        }\r\n      }\r\n\r\n      // Then invalidate queries to get the latest data from the server\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupDeleted'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      setShowDeleteGroupModal(false);\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(t('groupDeleteError'), error);\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupDeleteFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  const deleteGroupWithQuestionsMutation = useMutation({\r\n    mutationFn: deleteQuestionAndGroup,\r\n    onSuccess: (data, variables) => {\r\n\r\n      // Find the group that was deleted\r\n      const deletedGroup = questionGroups.find(\r\n        (g: QuestionGroup) => g.id === variables.id\r\n      );\r\n\r\n      if (deletedGroup) {\r\n        // Remove the deleted group from the local state\r\n        const updatedGroups = questionGroups.filter(\r\n          (g: QuestionGroup) => g.id !== variables.id\r\n        );\r\n        queryClient.setQueryData(groupsQueryKey, updatedGroups);\r\n\r\n        // Remove questions that belonged to this group from the local state\r\n        const updatedQuestions = questions.filter(\r\n          (q) => q.questionGroupId !== deletedGroup.id\r\n        );\r\n        queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n      }\r\n\r\n      // Then invalidate queries to get the latest data from the server\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupAndQuestionsDeleted'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      setShowDeleteGroupModal(false);\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(t('groupAndQuestionsDeleteError'), error);\r\n      dispatch(\r\n        showNotification({\r\n          message:\r\n            t('groupAndQuestionsDeleteFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  // Note: We'll need a mutation for removing questions from groups in the future\r\n  // This functionality will be implemented when needed\r\n\r\n  // Question position update mutation\r\n  const updatePositionsMutation = useMutation({\r\n    mutationFn: updateQuestionPositions,\r\n    onMutate: async (variables) => {\r\n      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)\r\n      await queryClient.cancelQueries({ queryKey: questionsQueryKey });\r\n\r\n      // Snapshot the previous value\r\n      const previousQuestions = queryClient.getQueryData(questionsQueryKey);\r\n\r\n      // Optimistically update the local state\r\n      if (previousQuestions && variables.questionPositions) {\r\n        const updatedQuestions = (previousQuestions as Question[]).map((question) => {\r\n          const newPosition = variables.questionPositions.find(\r\n            (pos) => pos.id === question.id\r\n          );\r\n          return newPosition ? { ...question, position: newPosition.position } : question;\r\n        });\r\n\r\n        queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n      }\r\n\r\n      // Return a context object with the snapshotted value\r\n      return { previousQuestions };\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch to ensure we have the latest data from server\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: t('questionOrderUpdated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error: any, variables, context) => {\r\n      // If the mutation fails, use the context returned from onMutate to roll back\r\n      if (context?.previousQuestions) {\r\n        queryClient.setQueryData(questionsQueryKey, context.previousQuestions);\r\n      }\r\n\r\n      console.error(\"Failed to update question positions:\", error);\r\n      console.error(\"Error response:\", error.response?.data);\r\n      dispatch(\r\n        showNotification({\r\n          message: `${t('questionOrderUpdateFailed')}: ${error.response?.data?.message || error.message || t('tryAgain')}`,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch after error or success to ensure we have the correct data\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n    },\r\n  });\r\n\r\n  // Move question between groups mutation\r\n  const moveQuestionBetweenGroupsMutation = useMutation({\r\n    mutationFn: moveQuestionBetweenGroups,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Question moved successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Failed to move question between groups:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: `Failed to move question: ${\r\n            error.response?.data?.message || error.message || \"Please try again\"\r\n          }`,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  // Move group inside another group mutation\r\n  const moveGroupInsideGroupMutation = useMutation({\r\n    mutationFn: moveGroupInsideGroup,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Group moved successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Failed to move group:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: `Failed to move group: ${\r\n            error.response?.data?.message || error.message || \"Please try again\"\r\n          }`,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  // Update group positions mutation\r\n  const updateGroupPositionsMutation = useMutation({\r\n    mutationFn: updateGroupPositions,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      dispatch(\r\n        showNotification({\r\n          message: \"Group order updated successfully\",\r\n          type: \"success\",\r\n        })\r\n      );\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Failed to update group positions:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: `Failed to update group order: ${\r\n            error.response?.data?.message || error.message || \"Please try again\"\r\n          }`,\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n    onSettled: () => {\r\n      // Always refetch after error or success to ensure we have the correct data\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n    },\r\n  });\r\n\r\n  // Question handlers\r\n  const handleDelete = () => {\r\n    if (currentQuestion && currentQuestion.id) {\r\n      deleteQuestionMutation.mutate({\r\n        contextType,\r\n        id: currentQuestion?.id,\r\n        projectId: contextId,\r\n      });\r\n    }\r\n  };\r\n\r\n  // Enhanced drag-and-drop handlers\r\n  const handleMoveQuestionBetweenGroups = (\r\n    questionId: number,\r\n    fromGroupId: number | null,\r\n    toGroupId: number | null\r\n  ) => {\r\n    if (fromGroupId === toGroupId) return;\r\n\r\n    moveQuestionBetweenGroupsMutation.mutate({\r\n      questionId,\r\n      groupId: fromGroupId || 0, // Use 0 for ungrouped questions\r\n      newGroupId: toGroupId || 0, // Use 0 for ungrouped questions\r\n    });\r\n  };\r\n\r\n  const handleMoveGroupInsideGroup = (\r\n    childGroupId: number,\r\n    parentGroupId: number | null\r\n  ) => {\r\n    if (parentGroupId) {\r\n      moveGroupInsideGroupMutation.mutate({\r\n        childGroupId,\r\n        parentGroupId,\r\n      });\r\n    }\r\n  };\r\n\r\n  // Subgroup creation handlers\r\n  const handleCreateSubgroup = (parentGroupId: number, selectedQuestionIds: number[]) => {\r\n    setSubgroupParentId(parentGroupId);\r\n    setSubgroupSelectedQuestions(selectedQuestionIds);\r\n    setShowSubgroupNameModal(true);\r\n  };\r\n\r\n  const handleSubgroupNameSubmit = () => {\r\n    if (!subgroupName.trim() || !subgroupParentId || subgroupSelectedQuestions.length === 0) {\r\n      return;\r\n    }\r\n\r\n    // Create the subgroup\r\n    // Calculate a reasonable order value\r\n    const maxOrder = questionGroups.length > 0\r\n      ? Math.max(...questionGroups.map((g: QuestionGroup) => g.order))\r\n      : 0;\r\n\r\n    createGroupMutation.mutate({\r\n      title: subgroupName.trim(),\r\n      order: maxOrder + 1, // Use next available order\r\n      projectId: contextId,\r\n      selectedQuestionIds: subgroupSelectedQuestions,\r\n      parentGroupId: subgroupParentId,\r\n    });\r\n\r\n    // Reset state\r\n    setShowSubgroupNameModal(false);\r\n    setSubgroupName(\"\");\r\n    setSubgroupParentId(null);\r\n    setSubgroupSelectedQuestions([]);\r\n\r\n    // Clear selection\r\n    setSelectedQuestionIds(prev =>\r\n      prev.filter(id => !subgroupSelectedQuestions.includes(id))\r\n    );\r\n  };\r\n\r\n  const handleCancelSubgroup = () => {\r\n    setShowSubgroupNameModal(false);\r\n    setSubgroupName(\"\");\r\n    setSubgroupParentId(null);\r\n    setSubgroupSelectedQuestions([]);\r\n  };\r\n\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (!over || active.id === over.id) {\r\n      return;\r\n    }\r\n\r\n    // Only handle drag and drop in project context\r\n    if (contextType !== \"project\") {\r\n      return;\r\n    }\r\n\r\n    const activeData = active.data.current;\r\n    const overData = over.data.current;\r\n\r\n    // Handle question reordering within the same group\r\n    if (activeData?.type === 'question' && overData?.type === 'question') {\r\n      const activeQuestion = questions.find((q) => q.id === active.id);\r\n      const overQuestion = questions.find((q) => q.id === over.id);\r\n\r\n      if (!activeQuestion || !overQuestion) {\r\n        return;\r\n      }\r\n\r\n      // Handle reordering within the same group or ungrouped questions\r\n      const isSameGroup =\r\n        activeQuestion.questionGroupId === overQuestion.questionGroupId;\r\n\r\n      if (!isSameGroup) {\r\n        return;\r\n      }\r\n\r\n      // Get questions in the same context (same group or ungrouped)\r\n      const contextQuestions = questions\r\n        .filter((q) => q.questionGroupId === activeQuestion.questionGroupId)\r\n        .sort((a, b) => a.position - b.position);\r\n\r\n      const oldIndex = contextQuestions.findIndex((q) => q.id === active.id);\r\n      const newIndex = contextQuestions.findIndex((q) => q.id === over.id);\r\n\r\n      if (oldIndex === -1 || newIndex === -1) {\r\n        return;\r\n      }\r\n\r\n      // Reorder the questions array\r\n      const reorderedQuestions = arrayMove(contextQuestions, oldIndex, newIndex);\r\n\r\n      // Calculate new positions for all affected questions\r\n      const questionPositions = reorderedQuestions.map((question, index) => ({\r\n        id: Number(question.id), // Ensure it's a number\r\n        position: index + 1, // Start positions from 1\r\n      }));\r\n\r\n      // Optimistically update the local state immediately\r\n      const updatedQuestions = questions.map((question) => {\r\n        const newPosition = questionPositions.find((pos) => pos.id === question.id);\r\n        return newPosition ? { ...question, position: newPosition.position } : question;\r\n      });\r\n      queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n\r\n      // Update positions in the backend\r\n      updatePositionsMutation.mutate({\r\n        contextType,\r\n        contextId,\r\n        questionPositions,\r\n      });\r\n    }\r\n\r\n    // Handle moving questions between groups\r\n    if (activeData?.type === 'question' && overData?.type === 'group-drop') {\r\n      const questionId = Number(active.id);\r\n      const fromGroupId = activeData.questionGroupId || null;\r\n      const toGroupId = overData.groupId;\r\n\r\n      handleMoveQuestionBetweenGroups(questionId, fromGroupId, toGroupId);\r\n    }\r\n\r\n    // Handle moving groups inside other groups\r\n    if (activeData?.type === 'group' && overData?.type === 'group-drop') {\r\n      const childGroupId = activeData.groupId;\r\n      const parentGroupId = overData.groupId;\r\n\r\n      // Prevent dropping a group into itself or its descendants\r\n      if (childGroupId !== parentGroupId) {\r\n        handleMoveGroupInsideGroup(childGroupId, parentGroupId);\r\n      }\r\n    }\r\n\r\n    // Handle group reordering\r\n    if (activeData?.type === 'group' && overData?.type === 'group') {\r\n      // This would handle reordering groups at the same level\r\n      // Implementation depends on specific requirements\r\n    }\r\n  };\r\n\r\n  // Group handlers\r\n  const handleEditGroup = (groupId: number) => {\r\n    setCurrentGroupId(groupId);\r\n    setShowEditGroupModal(true);\r\n  };\r\n\r\n  const handleDeleteGroup = (groupId: number) => {\r\n    setCurrentGroupId(groupId);\r\n    setShowDeleteGroupModal(true);\r\n  };\r\n\r\n  const handleConfirmDeleteGroup = () => {\r\n    if (currentGroupId) {\r\n      setIsProcessingGroup(true);\r\n      deleteGroupMutation.mutate({ id: currentGroupId });\r\n    }\r\n  };\r\n\r\n  const handleConfirmDeleteGroupWithQuestions = () => {\r\n    if (currentGroupId) {\r\n      setIsProcessingGroup(true);\r\n      deleteGroupWithQuestionsMutation.mutate({ id: currentGroupId });\r\n    }\r\n  };\r\n\r\n  const handleAddQuestionToGroup = (groupId: number) => {\r\n    setCurrentGroupId(groupId);\r\n    setShowEditGroupModal(true);\r\n  };\r\n\r\n  // Handle toggling question selection\r\n  const toggleQuestionSelection = (questionId: number) => {\r\n    setSelectedQuestionIds((prev) =>\r\n      prev.includes(questionId)\r\n        ? prev.filter((id) => id !== questionId)\r\n        : [...prev, questionId]\r\n    );\r\n  };\r\n\r\n  // Create group mutation\r\n  const createGroupMutation = useMutation({\r\n    mutationFn: createQuestionGroup,\r\n    onSuccess: (data, variables) => {\r\n\r\n      // Update local state immediately for a smoother UI experience\r\n      // This will show the grouped questions before the server refetch completes\r\n      const newGroupId = data.data?.questionGroup?.id;\r\n\r\n      if (newGroupId && variables.selectedQuestionIds) {\r\n\r\n        // Update the questionGroupId for selected questions in the local state\r\n        const updatedQuestions = questions.map((q) =>\r\n          variables.selectedQuestionIds?.includes(q.id)\r\n            ? { ...q, questionGroupId: newGroupId }\r\n            : q\r\n        );\r\n\r\n        // Update the local state with the new questions\r\n        queryClient.setQueryData(questionsQueryKey, updatedQuestions);\r\n\r\n        // Also update the groups in the local state\r\n        const newGroup = {\r\n          id: newGroupId,\r\n          title: variables.title,\r\n          order: variables.order,\r\n          projectId: variables.projectId,\r\n          createdAt: new Date().toISOString(),\r\n          updatedAt: new Date().toISOString(),\r\n          question: updatedQuestions.filter(\r\n            (q) => q.questionGroupId === newGroupId\r\n          ),\r\n        };\r\n\r\n        queryClient.setQueryData(groupsQueryKey, [...questionGroups, newGroup]);\r\n      }\r\n\r\n      // Then invalidate queries to get the latest data from the server\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupCreated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n\r\n      // Clear selection after creating group\r\n      setSelectedQuestionIds([]);\r\n      setSelectionMode(false);\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: (error) => {\r\n      console.error(t('groupCreateError'), error);\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupCreateFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  // Handle creating a group from selected questions\r\n  const handleCreateGroupFromSelected = () => {\r\n    if (selectedQuestionIds.length === 0) {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('selectAtLeastOneQuestion'),\r\n          type: \"warning\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsProcessingGroup(true);\r\n\r\n    // Calculate the group order based on the minimum position of selected questions\r\n    // This ensures the group appears in the same position as the first selected question\r\n    const selectedQuestions = questions.filter(q => selectedQuestionIds.includes(q.id));\r\n    const minPosition = selectedQuestions.length > 0\r\n      ? Math.min(...selectedQuestions.map(q => q.position))\r\n      : questionGroups.length + 1;\r\n\r\n    // Create a new group with the selected questions\r\n    createGroupMutation.mutate({\r\n      title: t('newGroup'),\r\n      order: minPosition,\r\n      projectId: contextId,\r\n      selectedQuestionIds,\r\n    });\r\n  };\r\n\r\n  // Handle inline editing of group name\r\n  const startEditingGroupName = (groupId: number, currentName: string) => {\r\n    setIsEditingGroupName(groupId);\r\n    setEditingGroupName(currentName);\r\n  };\r\n\r\n  // Handle canceling the editing of group name\r\n  const cancelEditingGroupName = () => {\r\n    setIsEditingGroupName(null);\r\n    setEditingGroupName(\"\");\r\n  };\r\n\r\n  // Update group mutation\r\n  const updateGroupMutation = useMutation({\r\n    mutationFn: updateQuestionGroup,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: groupsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupNameUpdated'),\r\n          type: \"success\",\r\n        })\r\n      );\r\n      setIsEditingGroupName(null);\r\n      setEditingGroupName(\"\");\r\n      setIsProcessingGroup(false);\r\n    },\r\n    onError: () => {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupNameUpdateFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n      setIsProcessingGroup(false);\r\n    },\r\n  });\r\n\r\n  const saveGroupName = (groupId: number) => {\r\n    if (!editingGroupName.trim()) {\r\n      dispatch(\r\n        showNotification({\r\n          message: t('groupNameEmpty'),\r\n          type: \"warning\",\r\n        })\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsProcessingGroup(true);\r\n\r\n    const group = questionGroups.find((g: QuestionGroup) => g.id === groupId);\r\n    if (!group) return;\r\n\r\n    // Update the group name in the local state immediately for better UX\r\n    const updatedGroups = questionGroups.map((g: QuestionGroup) =>\r\n      g.id === groupId ? { ...g, title: editingGroupName } : g\r\n    );\r\n    queryClient.setQueryData(groupsQueryKey, updatedGroups);\r\n\r\n    // Then send the update to the server\r\n    updateGroupMutation.mutate({\r\n      id: groupId,\r\n      title: editingGroupName,\r\n      order: group.order,\r\n    });\r\n  };\r\n\r\n  // Mutation for adding a single question\r\n  const addQuestionMutation = useMutation({\r\n    mutationFn: addQuestion,\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n    },\r\n    onError: (error) => {\r\n      console.error(t('addQuestionError'), error);\r\n      dispatch(\r\n        showNotification({\r\n          message: t('addQuestionFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    },\r\n  });\r\n\r\n  // Function to handle adding questions from the library\r\n  const handleAddQuestionsFromLibrary = async (\r\n    selectedQuestions: Question[]\r\n  ) => {\r\n    if (selectedQuestions.length === 0) return;\r\n\r\n    setIsAddingQuestions(true);\r\n\r\n    try {\r\n      // Calculate the starting position for new questions\r\n      const maxPosition = questions.length > 0 ? Math.max(...questions.map(q => q.position)) : 0;\r\n\r\n      // Process each selected question sequentially\r\n      for (let i = 0; i < selectedQuestions.length; i++) {\r\n        const question = selectedQuestions[i];\r\n        const dataToSend = {\r\n          label: question.label,\r\n          isRequired: question.isRequired,\r\n          hint: question.hint || \"\",\r\n          placeholder: question.placeholder || \"\",\r\n          inputType: String(question.inputType), // Convert to string to ensure compatibility\r\n          questionOptions: question.questionOptions || [],\r\n        };\r\n\r\n        await addQuestion({\r\n          contextType,\r\n          contextId,\r\n          dataToSend,\r\n          position: maxPosition + i + 1, // Add questions at the end\r\n        });\r\n      }\r\n\r\n      // Refresh the questions list\r\n      queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n      if (contextType === \"project\") {\r\n        queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n      }\r\n\r\n      dispatch(\r\n        showNotification({\r\n          message: `${selectedQuestions.length} ${t('questionsAdded')}`,\r\n          type: \"success\",\r\n        })\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error adding questions:\", error);\r\n      dispatch(\r\n        showNotification({\r\n          message: t('addFromLibraryFailed'),\r\n          type: \"error\",\r\n        })\r\n      );\r\n    } finally {\r\n      setIsAddingQuestions(false);\r\n    }\r\n  };\r\n  // Show loading state\r\n  const isLoading = contextType === \"project\" ? isLoadingFormData : isLoadingFallback;\r\n  const isError = contextType === \"project\" ? isFormDataError : isFallbackError;\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"min-h-[60vh] flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-muted-foreground\">Loading form data...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (isError) {\r\n    return (\r\n      <div className=\"min-h-[60vh] flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <p className=\"text-red-500 mb-4\">{t('formLoadError')}</p>\r\n          <button\r\n            onClick={() => {\r\n              if (contextType === \"project\") {\r\n                queryClient.invalidateQueries({ queryKey: formBuilderDataQueryKey });\r\n              } else {\r\n                queryClient.invalidateQueries({ queryKey: questionsQueryKey });\r\n              }\r\n            }}\r\n            className=\"btn-primary\"\r\n          >\r\n            {t('retry')}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-[60vh] relative\">\r\n      {(deleteQuestionMutation.isPending ||\r\n        duplicateQuestionMutation.isPending ||\r\n        deleteGroupMutation.isPending ||\r\n        deleteGroupWithQuestionsMutation.isPending ||\r\n        updatePositionsMutation.isPending ||\r\n        isAddingQuestions) && <LoadingOverlay />}\r\n\r\n      {/* For adding new questions */}\r\n      <AddQuestionModal\r\n        showModal={showAddQuestionModal}\r\n        setShowModal={setShowAddQuestionModal}\r\n        contextType={contextType}\r\n        contextId={contextId}\r\n        position={questions.length > 0 ? Math.max(...questions.map(q => q.position)) + 1 : 1}\r\n      />\r\n\r\n      {/* for editing existing questions, it requires a question object as props */}\r\n      {currentQuestion && currentQuestion.inputType !== \"table\" && (\r\n        <EditQuestionModal\r\n          showModal={showEditQuestionModal}\r\n          setShowModal={setShowEditQuestionModal}\r\n          contextType={contextType}\r\n          question={currentQuestion}\r\n          contextId={contextId}\r\n        />\r\n      )}\r\n\r\n      {/* for editing table questions specifically */}\r\n      {currentQuestion && currentQuestion.inputType === \"table\" && (\r\n        <EditTableQuestionModal\r\n          showModal={showEditTableQuestionModal}\r\n          setShowModal={setShowEditTableQuestionModal}\r\n          contextType={contextType}\r\n          question={currentQuestion}\r\n          contextId={contextId}\r\n        />\r\n      )}\r\n\r\n      {/* For adding/editing question groups */}\r\n      <QuestionGroupModal\r\n        showModal={showAddGroupModal}\r\n        setShowModal={setShowAddGroupModal}\r\n        contextType={contextType}\r\n        contextId={contextId}\r\n        questions={questions}\r\n        questionGroups={questionGroups}\r\n      />\r\n\r\n      {/* For editing existing question groups */}\r\n      {currentGroupId && (\r\n        <QuestionGroupModal\r\n          showModal={showEditGroupModal}\r\n          setShowModal={setShowEditGroupModal}\r\n          contextType={contextType}\r\n          contextId={contextId}\r\n          existingGroup={questionGroups.find(\r\n            (g: QuestionGroup) => g.id === currentGroupId\r\n          )}\r\n          questions={questions}\r\n          questionGroups={questionGroups}\r\n        />\r\n      )}\r\n\r\n      {/* Delete question confirmation modal */}\r\n      <ConfirmationModal\r\n        showModal={showDeleteConfirmationModal}\r\n        onClose={() => setShowDeleteConfirmationModal(false)}\r\n        onConfirm={handleDelete}\r\n        title= {t('deleteQuestion')}\r\n        description= {t('confirmDeleteQuestion')}\r\n        confirmButtonText= {t('delete')}\r\n        cancelButtonText= {t('cancel')}\r\n        confirmButtonClass=\"btn-danger\"\r\n      />\r\n\r\n      {/* Delete group confirmation modal */}\r\n      <DeleteQuestionGroupModal\r\n        showModal={showDeleteGroupModal}\r\n        setShowModal={setShowDeleteGroupModal}\r\n        onConfirmDelete={handleConfirmDeleteGroup}\r\n        onConfirmDeleteWithQuestions={handleConfirmDeleteGroupWithQuestions}\r\n        isDeleting={\r\n          deleteGroupMutation.isPending ||\r\n          deleteGroupWithQuestionsMutation.isPending\r\n        }\r\n      />\r\n\r\n      {/* Library questions sidebar */}\r\n      <LibraryQuestionsSidebar\r\n        isOpen={showLibrarySidebar}\r\n        onClose={() => setShowLibrarySidebar(false)}\r\n        onAddQuestions={handleAddQuestionsFromLibrary}\r\n      />\r\n\r\n      {/* Header with actions */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <div className=\"flex items-center\">\r\n          <h1 className=\"heading-text mr-4\"> {t('formBuilder')} </h1>\r\n\r\n          {/* Create Group button */}\r\n          {selectedQuestionIds.length > 0 ? (\r\n            <button\r\n              className=\"btn-primary flex items-center gap-2\"\r\n              onClick={handleCreateGroupFromSelected}\r\n              disabled={isProcessingGroup}\r\n            >\r\n              <FolderPlus size={16} />\r\n              {t('createGroup')} ({selectedQuestionIds.length})\r\n            </button>\r\n          ) : (\r\n            <div className=\"flex gap-2\">\r\n              <button\r\n                className=\"btn-outline flex items-center gap-2\"\r\n                onClick={() => {\r\n                  // Toggle selection mode\r\n                  if (!selectionMode) {\r\n                    setSelectionMode(true);\r\n                  } else {\r\n                    setSelectionMode(false);\r\n                    setSelectedQuestionIds([]);\r\n                  }\r\n                }}\r\n              >\r\n                <FolderPlus size={16} />\r\n                {selectionMode ? t('cancelSelection') : t('selectQuestions')}\r\n              </button>\r\n\r\n              <button\r\n                className=\"btn-outline flex items-center gap-2\"\r\n                onClick={() => setShowAddGroupModal(true)}\r\n              >\r\n                <FolderPlus size={16} />\r\n                {t('createEmptyGroup')}\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => setIsPreviewMode(true)}\r\n            title={t('previewForm')}\r\n          >\r\n            <Eye size={16} />\r\n          </button>\r\n          <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => setShowLibrarySidebar(true)}\r\n            title={t('questionLibrary')}\r\n          >\r\n            <BookOpen size={16} />\r\n          </button>\r\n          {/* <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => {}}\r\n            title=\"Export Form\"\r\n          >\r\n            <FileDown size={16} />\r\n          </button>\r\n          <button\r\n            className=\"btn-outline p-2\"\r\n            onClick={() => {}}\r\n            title=\"Import Form\"\r\n          >\r\n            <FileUp size={16} />\r\n          </button>\r\n          <button className=\"btn-outline p-2\" title=\"Settings\">\r\n            <Settings size={16} />\r\n          </button> */}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content area */}\r\n      <div className=\"section shadow-none border border-neutral-400\">\r\n        <DndContext\r\n          sensors={sensors}\r\n          collisionDetection={closestCenter}\r\n          onDragEnd={handleDragEnd}\r\n        >\r\n          <SortableContext\r\n            items={[\r\n              ...questions.map((question) => question.id),\r\n              ...questionGroups.map((group: QuestionGroup) => `group-${group.id}`)\r\n            ]}\r\n            strategy={verticalListSortingStrategy}\r\n          >\r\n            <div className=\"space-y-4\">\r\n              {questions.length === 0 ? (\r\n                // No questions at all\r\n                <div className=\"text-center py-16 px-4\">\r\n                  <h3 className=\"heading-text text-muted-foreground\">\r\n                    {t('noQuestionsYet')}\r\n                  </h3>\r\n                  <p className=\"mt-1 text-sm sub-text\">\r\n                    {t('addFirstQuestion')}\r\n                  </p>\r\n                  <div className=\"p-4 flex justify-center\">\r\n                    <button\r\n                      onClick={() => setShowAddQuestionModal(true)}\r\n                      className=\"btn-primary\"\r\n                      disabled={!editFormPermission}\r\n                    >\r\n                      <PlusCircle size={16} />\r\n                      {t('addFirst')}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                // Render unified form items (groups and individual questions) in order\r\n                unifiedFormItems.map((item) => {\r\n                  if (item.type === 'group') {\r\n                    const group = item.data as QuestionGroup;\r\n                    // Use questions from the nested group structure instead of filtering again\r\n                    const groupQuestions = group.question || [];\r\n\r\n                    return (\r\n                      <div key={`group-${group.id}`} className=\"mb-4\">\r\n                        <QuestionGroupItem\r\n                          id={group.id}\r\n                          title={group.title}\r\n                          questions={groupQuestions}\r\n                          subGroups={group.subGroups}\r\n                          parentGroupId={group.parentGroupId}\r\n                          nestingLevel={0}\r\n                          onEditGroup={handleEditGroup}\r\n                          onDeleteGroup={handleDeleteGroup}\r\n                          onAddQuestionToGroup={handleAddQuestionToGroup}\r\n                          onEditQuestion={(question) => {\r\n                            setCurrentQuestion(question);\r\n                            if (question.inputType === \"table\") {\r\n                              setShowEditTableQuestionModal(true);\r\n                            } else {\r\n                              setShowEditQuestionModal(true);\r\n                            }\r\n                          }}\r\n                          onDeleteQuestion={(question) => {\r\n                            setCurrentQuestion(question);\r\n                            setShowDeleteConfirmationModal(true);\r\n                          }}\r\n                          onDuplicateQuestion={(question) => {\r\n                            setCurrentQuestion(question);\r\n                            duplicateQuestionMutation.mutate({\r\n                              id: question.id,\r\n                              contextType,\r\n                              contextId,\r\n                            });\r\n                          }}\r\n                          onReorderQuestions={(questionPositions) => {\r\n                            updatePositionsMutation.mutate({\r\n                              contextType,\r\n                              contextId,\r\n                              questionPositions,\r\n                            });\r\n                          }}\r\n                          onMoveQuestionBetweenGroups={handleMoveQuestionBetweenGroups}\r\n                          onMoveGroupInsideGroup={handleMoveGroupInsideGroup}\r\n                          isEditing={isEditingGroupName === group.id}\r\n                          onStartEditing={startEditingGroupName}\r\n                          onSaveGroupName={saveGroupName}\r\n                          onCancelEditing={cancelEditingGroupName}\r\n                          editingName={editingGroupName}\r\n                          onEditingNameChange={setEditingGroupName}\r\n                          selectionMode={selectionMode}\r\n                          isDraggable={true}\r\n                          selectedQuestionIds={selectedQuestionIds}\r\n                          onToggleQuestionSelect={toggleQuestionSelection}\r\n                          onCreateSubgroup={handleCreateSubgroup}\r\n                        />\r\n                      </div>\r\n                    );\r\n                  } else {\r\n                    const question = item.data as Question;\r\n                    return (\r\n                      <div key={`question-${question.id}`} className=\"mb-4\">\r\n                        <QuestionItem\r\n                          question={question}\r\n                          onEdit={() => {\r\n                            setCurrentQuestion(question);\r\n                            if (question.inputType === \"table\") {\r\n                              setShowEditTableQuestionModal(true);\r\n                            } else {\r\n                              setShowEditQuestionModal(true);\r\n                            }\r\n                          }}\r\n                          onDelete={() => {\r\n                            setCurrentQuestion(question);\r\n                            setShowDeleteConfirmationModal(true);\r\n                          }}\r\n                          onDuplicate={() => {\r\n                            setCurrentQuestion(question);\r\n                            duplicateQuestionMutation.mutate({\r\n                              id: question.id,\r\n                              contextType,\r\n                              contextId,\r\n                            });\r\n                          }}\r\n                          selectionMode={selectionMode}\r\n                          isSelected={selectedQuestionIds.includes(question.id)}\r\n                          onToggleSelect={() =>\r\n                            toggleQuestionSelection(question.id)\r\n                          }\r\n                        />\r\n                      </div>\r\n                    );\r\n                  }\r\n                })\r\n              )}\r\n            </div>\r\n          </SortableContext>\r\n        </DndContext>\r\n      </div>\r\n\r\n      {/* Subgroup name modal */}\r\n      {showSubgroupNameModal && (\r\n        <div className=\"fixed inset-0 bg-neutral-900/50 flex items-center justify-center z-40\">\r\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md mx-4\">\r\n            <h3 className=\"text-lg font-semibold mb-4\">Create Subgroup</h3>\r\n            <p className=\"text-sm text-neutral-600 mb-4\">\r\n              Creating a subgroup with {subgroupSelectedQuestions.length} selected question(s).\r\n            </p>\r\n            <div className=\"mb-4\">\r\n              <label htmlFor=\"subgroup-name\" className=\"block text-sm font-medium mb-2\">\r\n                Subgroup Name\r\n              </label>\r\n              <input\r\n                id=\"subgroup-name\"\r\n                type=\"text\"\r\n                value={subgroupName}\r\n                onChange={(e) => setSubgroupName(e.target.value)}\r\n                placeholder=\"Enter subgroup name\"\r\n                className=\"w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500\"\r\n                autoFocus\r\n                onKeyDown={(e) => {\r\n                  if (e.key === 'Enter') {\r\n                    handleSubgroupNameSubmit();\r\n                  } else if (e.key === 'Escape') {\r\n                    handleCancelSubgroup();\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n            <div className=\"flex justify-end gap-3\">\r\n              <button\r\n                onClick={handleCancelSubgroup}\r\n                className=\"px-4 py-2 text-neutral-600 hover:text-neutral-800 transition-colors\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleSubgroupNameSubmit}\r\n                disabled={!subgroupName.trim()}\r\n                className=\"px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n              >\r\n                Create Subgroup\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Bottom action bar */}\r\n      {questions.length > 0 && (\r\n        <div className=\"sticky bottom-0 p-4 flex justify-center\">\r\n          <button\r\n            className={`btn-primary  max-w-md flex items-center justify-center gap-2 ${\r\n              !editFormPermission && \"text-gray-400 cursor-not-allowed\"\r\n            }`}\r\n            onClick={() => setShowAddQuestionModal(true)}\r\n            disabled={!editFormPermission}\r\n          >\r\n            <PlusCircle size={16} />\r\n            {t('addQuestion')}\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { FormBuilder };"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAMA;AACA;AACA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AASA;AAWA;AACA;AAEA;AACA;AAEA;;;AA9DA;;;;;;;;;;;;;;;;;;;;;AAgEA,MAAM,cAAc,CAAC,EACnB,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,WAAW,EAMZ;;IACC,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,EAAE;QACvB,sBAAsB;YACpB,UAAU;QACZ;IACF,IACA,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc;IAG1B,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,MAAM,qBAAqB,YAAY,aAAa,IAAI,YAAY,QAAQ;IAE5E,wBAAwB;IACxB,MAAM,CAAC,sBAAsB,wBAAwB,GACnD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,uBAAuB,yBAAyB,GACrD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,4BAA4B,8BAA8B,GAC/D,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,6BAA6B,+BAA+B,GACjE,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEpB,8BAA8B;IAC9B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,sBAAsB,wBAAwB,GACnD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,2BAA2B;IAC3B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzD;IAEF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAE5D,0BAA0B;IAC1B,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,oDAAoD;IACpD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,2DAA2D;IAC3D,MAAM,oBAAoB,6JAAA,CAAA,UAAK,CAAC,OAAO;kDAAC;YACtC,OAAO,gBAAgB,YACnB;gBAAC;gBAAa;aAAU,GACxB,gBAAgB,aAChB;gBAAC;gBAAqB;aAAU,GAChC;gBAAC;gBAA0B;aAAU;QAC3C;iDAAG;QAAC;QAAa;KAAU;IAE3B,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,OAAO;+CAAC,IAAM;gBAAC;gBAAkB;aAAU;8CAAE;QAAC;KAAU;IACrF,MAAM,0BAA0B,6JAAA,CAAA,UAAK,CAAC,OAAO;wDAAC,IAAM;gBAAC;gBAAmB;aAAU;uDAAE;QAAC;KAAU;IAE/F,sEAAsE;IACtE,MAAM,EAAE,MAAM,eAAe,EAAE,WAAW,iBAAiB,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACjG,UAAU;QACV,OAAO;oCAAE,IAAM,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE;oBAAE,WAAW;gBAAU;;QAC3D,SAAS,gBAAgB;IAC3B;IAEA,8CAA8C;IAC9C,MAAM,EAAE,MAAM,oBAAoB,EAAE,EAAE,WAAW,iBAAiB,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAc;QACpH,UAAU;QACV,OAAO;oCAAE;gBACP,IAAI,gBAAgB,YAAY;oBAC9B,OAAO,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE;wBAAE,YAAY;oBAAU;gBACxD,OAAO,IAAI,gBAAgB,iBAAiB;oBAC1C,OAAO,CAAA,GAAA,gIAAA,CAAA,8BAA2B,AAAD;gBACnC;gBACA,OAAO,EAAE;YACX;;QACA,SAAS,gBAAgB;IAC3B;IAEA,qDAAqD;IACrD,MAAM,YAAwB,6JAAA,CAAA,UAAK,CAAC,OAAO;0CAAC;YAC1C,IAAI,gBAAgB,WAAW;gBAC7B,IAAI,CAAC,iBAAiB,OAAO;oBAC3B,OAAO,EAAE;gBACX;gBAEA,yCAAyC;gBACzC,MAAM,qBAAiC,EAAE;gBAEzC,gBAAgB,KAAK,CAAC,OAAO;sDAAC,CAAC;wBAC7B,IAAI,KAAK,IAAI,KAAK,YAAY;4BAC5B,mBAAmB,IAAI,CAAC;wBAC1B,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,SAAS,EAAE;4BAClD,4BAA4B;4BAC5B,KAAK,SAAS,CAAC,OAAO;kEAAC,CAAC;oCACtB,mBAAmB,IAAI,CAAC;gCAC1B;;wBACF;oBACF;;gBAEA,OAAO;YACT,OAAO;gBACL,mDAAmD;gBACnD,OAAO;YACT;QACF;yCAAG;QAAC;QAAiB;QAAa;KAAkB;IAEpD,gDAAgD;IAChD,MAAM,iBAAkC,6JAAA,CAAA,UAAK,CAAC,OAAO;+CAAC;YACpD,IAAI,gBAAgB,aAAa,CAAC,iBAAiB,OAAO;gBACxD,OAAO,EAAE;YACX;YAEA,OAAO,gBAAgB,KAAK,CACzB,MAAM;uDAAC,CAAC,OAAc,KAAK,IAAI,KAAK;sDACpC,GAAG;uDAAC,CAAC,OAAc,CAAC;wBACnB,GAAG,IAAI;wBACP,UAAU,KAAK,SAAS,IAAI,EAAE;oBAChC,CAAC;;QACL;8CAAG;QAAC;QAAiB;KAAY;IAEjC,0DAA0D;IAC1D,MAAM,EAAE,MAAM,yBAAyB,EAAE,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,UAAU;QACV,OAAO;oCAAE,IAAM,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAE,WAAW;gBAAU;;QAC1D,SAAS,gBAAgB,aAAa,CAAC;IACzC;IAEA,2EAA2E;IAC3E,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;iDAAC;YACrC,OAAO,eAAe,MAAM;yDAC1B,CAAC,KAAiC;oBAChC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,UACb,MAAM;iEAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE;gEAC5C,IAAI;iEAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;;oBACzC,OAAO;gBACT;wDACA,CAAC;QAEL;gDAAG;QAAC;QAAgB;KAAU;IAE9B,oEAAoE;IACpE,MAAM,qBAAqB,6JAAA,CAAA,UAAK,CAAC,OAAO;mDAAC;YACvC,OAAO,UAAU,MAAM;2DAAC,CAAC,IAAM,CAAC,EAAE,eAAe;;QACnD;kDAAG;QAAC;KAAU;IAEd,+BAA+B;IAC/B,MAAM,oBAAoB,6JAAA,CAAA,UAAK,CAAC,WAAW;sDAAC,CAAC,QAAyB;YACpE,MAAM,WAAW,IAAI;YAErB,4EAA4E;YAC5E,OAAO,OAAO;8DAAC,CAAA;oBACb,+BAA+B;oBAC/B,MAAM,iBAAiB,cACpB,MAAM;qFAAC,CAAC,IAAM,EAAE,eAAe,KAAK,MAAM,EAAE;oFAC5C,IAAI;qFAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;;oBAEzC,SAAS,GAAG,CAAC,MAAM,EAAE,EAAE;wBACrB,GAAG,KAAK;wBACR,WAAW,EAAE;wBACb,UAAU;oBACZ;gBACF;;YAEA,6BAA6B;YAC7B,MAAM,iBAAkC,EAAE;YAC1C,OAAO,OAAO;8DAAC,CAAA;oBACb,MAAM,qBAAqB,SAAS,GAAG,CAAC,MAAM,EAAE;oBAEhD,IAAI,MAAM,aAAa,EAAE;wBACvB,0DAA0D;wBAC1D,MAAM,cAAc,SAAS,GAAG,CAAC,MAAM,aAAa;wBACpD,IAAI,aAAa;4BACf,YAAY,SAAS,GAAG,YAAY,SAAS,IAAI,EAAE;4BACnD,YAAY,SAAS,CAAC,IAAI,CAAC;wBAC7B;oBACF,OAAO;wBACL,4BAA4B;wBAC5B,eAAe,IAAI,CAAC;oBACtB;gBACF;;YAEA,OAAO;QACT;qDAAG,EAAE;IAEL,oEAAoE;IACpE,MAAM,uBAAuB,6JAAA,CAAA,UAAK,CAAC,OAAO;qDAAC;YACzC,OAAO,kBAAkB,gBAAgB;QAC3C;oDAAG;QAAC;QAAgB;QAAW;KAAkB;IAEjD,6FAA6F;IAC7F,MAAM,yBAAyB,6JAAA,CAAA,UAAK,CAAC,WAAW;2DAAC,CAC/C,cACA,WACA,cACA;YAEA,MAAM,QAKD,EAAE;YAEP,2EAA2E;YAC3E,wEAAwE;YACxE,IAAI,YAAY,WAAW;gBACzB,aAAa,OAAO;uEAAC,CAAC;wBACpB,kEAAkE;wBAClE,oFAAoF;wBACpF,MAAM,iBAAiB,aACpB,MAAM;8FAAC,CAAA,IAAK,EAAE,eAAe,KAAK,MAAM,EAAE;6FAC1C,IAAI;8FAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;;wBACzC,MAAM,sBAAsB,eAAe,MAAM,GAAG,IAChD,KAAK,GAAG,IAAI,eAAe,GAAG;+EAAC,CAAA,IAAK,EAAE,QAAQ;iFAC9C,MAAM,KAAK;wBAEf,MAAM,IAAI,CAAC;4BACT,MAAM;4BACN,MAAM;4BACN,OAAO;4BACP,kBAAkB;wBACpB;oBACF;;YACF;YAEA,uEAAuE;YACvE,MAAM,iBAAiB,YAAY,YAAY,YAAY;YAC3D,eAAe,OAAO;mEAAC,CAAC;oBACtB,MAAM,IAAI,CAAC;wBACT,MAAM;wBACN,MAAM;wBACN,OAAO,SAAS,QAAQ;wBACxB,kBAAkB,SAAS,QAAQ;oBACrC;gBACF;;YAEA,sFAAsF;YACtF,mDAAmD;YACnD,OAAO,MAAM,IAAI;mEAAC,CAAC,GAAG;oBACpB,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE;wBACvB,0EAA0E;wBAC1E,gEAAgE;wBAChE,OAAO,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,gBAAgB,IAAI,EAAE,KAAK;oBACzE;oBACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B;;QACF;0DAAG,EAAE;IAEL,mEAAmE;IACnE,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;iDAAC;YACrC,OAAO,uBAAuB,sBAAsB,oBAAoB,WAAW;QACrF;gDAAG;QAAC;QAAsB;QAAoB;QAAW;QAAa;KAAuB;IAE7F,+FAA+F;IAC/F,2FAA2F;IAC3F,MAAM,2BAA2B,6JAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAE9C,6JAAA,CAAA,UAAK,CAAC,SAAS;iCAAC;YACd,MAAM,sBAAsB,UAAU,IAAI;6DACxC,CAAC,IAAM,EAAE,eAAe,KAAK,QAAQ,EAAE,eAAe,KAAK;;YAE7D,MAAM,cAAc,eAAe,MAAM,KAAK;YAE9C,IAAI,uBAAuB,eAAe,gBAAgB,aAAa,CAAC,yBAAyB,OAAO,EAAE;gBACxG,yBAAyB,OAAO,GAAG;gBACnC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;YAC3D;YAEA,wCAAwC;YACxC,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,yBAAyB,OAAO,GAAG;YACrC;QACF;gCAAG;QAAC;QAAW;QAAgB;QAAa;QAAa;KAAe;IAExE,qBAAqB;IACrB,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,YAAY,gIAAA,CAAA,iBAAc;QAC1B,SAAS;+DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;QACA,OAAO;+DAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;QACA,SAAS;+DAAE;gBACT,+BAA+B;YACjC;;IACF;IAEA,MAAM,4BAA4B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC5C,YAAY,gIAAA,CAAA,oBAAiB;QAC7B,SAAS;kEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;QACA,OAAO;kEAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;QACA,SAAS;kEAAE;gBACT,+BAA+B;YACjC;;IACF;IAEA,2BAA2B;IAC3B,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;4DAAE,CAAC,MAAM;gBAEhB,kCAAkC;gBAClC,MAAM,eAAe,eAAe,IAAI;iFACtC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;gBAG7C,IAAI,cAAc;oBAChB,6CAA6C;oBAC7C,MAAM,mBAAmB,UAAU,MAAM;yFACvC,CAAC,IAAM,EAAE,eAAe,KAAK,aAAa,EAAE;;oBAG9C,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBAC/B,8DAA8D;wBAC9D,MAAM,mBAAmB,UAAU,GAAG;6FAAC,CAAC,IACtC,EAAE,eAAe,KAAK,aAAa,EAAE,GACjC;oCAAE,GAAG,CAAC;oCAAE,iBAAiB;gCAAU,IACnC;;wBAGN,0CAA0C;wBAC1C,YAAY,YAAY,CAAC,mBAAmB;wBAE5C,gDAAgD;wBAChD,MAAM,gBAAgB,eAAe,MAAM;0FACzC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;wBAE7C,YAAY,YAAY,CAAC,gBAAgB;oBAC3C;gBACF;gBAEA,iEAAiE;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBAEA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAGF,wBAAwB;gBACxB,qBAAqB;YACvB;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,EAAE,qBAAqB;gBACrC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,MAAM,mCAAmC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACnD,YAAY,mIAAA,CAAA,yBAAsB;QAClC,SAAS;yEAAE,CAAC,MAAM;gBAEhB,kCAAkC;gBAClC,MAAM,eAAe,eAAe,IAAI;8FACtC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;gBAG7C,IAAI,cAAc;oBAChB,gDAAgD;oBAChD,MAAM,gBAAgB,eAAe,MAAM;mGACzC,CAAC,IAAqB,EAAE,EAAE,KAAK,UAAU,EAAE;;oBAE7C,YAAY,YAAY,CAAC,gBAAgB;oBAEzC,oEAAoE;oBACpE,MAAM,mBAAmB,UAAU,MAAM;sGACvC,CAAC,IAAM,EAAE,eAAe,KAAK,aAAa,EAAE;;oBAE9C,YAAY,YAAY,CAAC,mBAAmB;gBAC9C;gBAEA,iEAAiE;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBAEA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAGF,wBAAwB;gBACxB,qBAAqB;YACvB;;QACA,OAAO;yEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,EAAE,iCAAiC;gBACjD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SACE,EAAE;oBACJ,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,+EAA+E;IAC/E,qDAAqD;IAErD,oCAAoC;IACpC,MAAM,0BAA0B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,YAAY,gIAAA,CAAA,0BAAuB;QACnC,QAAQ;gEAAE,OAAO;gBACf,gFAAgF;gBAChF,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;gBAAkB;gBAE9D,8BAA8B;gBAC9B,MAAM,oBAAoB,YAAY,YAAY,CAAC;gBAEnD,wCAAwC;gBACxC,IAAI,qBAAqB,UAAU,iBAAiB,EAAE;oBACpD,MAAM,mBAAmB,AAAC,kBAAiC,GAAG;6FAAC,CAAC;4BAC9D,MAAM,cAAc,UAAU,iBAAiB,CAAC,IAAI;iHAClD,CAAC,MAAQ,IAAI,EAAE,KAAK,SAAS,EAAE;;4BAEjC,OAAO,cAAc;gCAAE,GAAG,QAAQ;gCAAE,UAAU,YAAY,QAAQ;4BAAC,IAAI;wBACzE;;oBAEA,YAAY,YAAY,CAAC,mBAAmB;gBAC9C;gBAEA,qDAAqD;gBACrD,OAAO;oBAAE;gBAAkB;YAC7B;;QACA,SAAS;gEAAE;gBACT,uEAAuE;gBACvE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;QACA,OAAO;gEAAE,CAAC,OAAY,WAAW;gBAC/B,6EAA6E;gBAC7E,IAAI,SAAS,mBAAmB;oBAC9B,YAAY,YAAY,CAAC,mBAAmB,QAAQ,iBAAiB;gBACvE;gBAEA,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,QAAQ,KAAK,CAAC,mBAAmB,MAAM,QAAQ,EAAE;gBACjD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,GAAG,EAAE,6BAA6B,EAAE,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,EAAE,aAAa;oBAChH,MAAM;gBACR;YAEJ;;QACA,SAAS;gEAAE;gBACT,2EAA2E;gBAC3E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;YAC9D;;IACF;IAEA,wCAAwC;IACxC,MAAM,oCAAoC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACpD,YAAY,mIAAA,CAAA,4BAAyB;QACrC,SAAS;0EAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,OAAO;0EAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,CAAC,yBAAyB,EACjC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,oBAClD;oBACF,MAAM;gBACR;YAEJ;;IACF;IAEA,2CAA2C;IAC3C,MAAM,+BAA+B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC/C,YAAY,mIAAA,CAAA,uBAAoB;QAChC,SAAS;qEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,OAAO;qEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,CAAC,sBAAsB,EAC9B,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,oBAClD;oBACF,MAAM;gBACR;YAEJ;;IACF;IAEA,kCAAkC;IAClC,MAAM,+BAA+B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QAC/C,YAAY,mIAAA,CAAA,uBAAoB;QAChC,SAAS;qEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS;oBACT,MAAM;gBACR;YAEJ;;QACA,OAAO;qEAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,CAAC,8BAA8B,EACtC,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,oBAClD;oBACF,MAAM;gBACR;YAEJ;;QACA,SAAS;qEAAE;gBACT,2EAA2E;gBAC3E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;YAC9D;;IACF;IAEA,oBAAoB;IACpB,MAAM,eAAe;QACnB,IAAI,mBAAmB,gBAAgB,EAAE,EAAE;YACzC,uBAAuB,MAAM,CAAC;gBAC5B;gBACA,IAAI,iBAAiB;gBACrB,WAAW;YACb;QACF;IACF;IAEA,kCAAkC;IAClC,MAAM,kCAAkC,CACtC,YACA,aACA;QAEA,IAAI,gBAAgB,WAAW;QAE/B,kCAAkC,MAAM,CAAC;YACvC;YACA,SAAS,eAAe;YACxB,YAAY,aAAa;QAC3B;IACF;IAEA,MAAM,6BAA6B,CACjC,cACA;QAEA,IAAI,eAAe;YACjB,6BAA6B,MAAM,CAAC;gBAClC;gBACA;YACF;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,uBAAuB,CAAC,eAAuB;QACnD,oBAAoB;QACpB,6BAA6B;QAC7B,yBAAyB;IAC3B;IAEA,MAAM,2BAA2B;QAC/B,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,oBAAoB,0BAA0B,MAAM,KAAK,GAAG;YACvF;QACF;QAEA,sBAAsB;QACtB,qCAAqC;QACrC,MAAM,WAAW,eAAe,MAAM,GAAG,IACrC,KAAK,GAAG,IAAI,eAAe,GAAG,CAAC,CAAC,IAAqB,EAAE,KAAK,KAC5D;QAEJ,oBAAoB,MAAM,CAAC;YACzB,OAAO,aAAa,IAAI;YACxB,OAAO,WAAW;YAClB,WAAW;YACX,qBAAqB;YACrB,eAAe;QACjB;QAEA,cAAc;QACd,yBAAyB;QACzB,gBAAgB;QAChB,oBAAoB;QACpB,6BAA6B,EAAE;QAE/B,kBAAkB;QAClB,uBAAuB,CAAA,OACrB,KAAK,MAAM,CAAC,CAAA,KAAM,CAAC,0BAA0B,QAAQ,CAAC;IAE1D;IAEA,MAAM,uBAAuB;QAC3B,yBAAyB;QACzB,gBAAgB;QAChB,oBAAoB;QACpB,6BAA6B,EAAE;IACjC;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,QAAQ,OAAO,EAAE,KAAK,KAAK,EAAE,EAAE;YAClC;QACF;QAEA,+CAA+C;QAC/C,IAAI,gBAAgB,WAAW;YAC7B;QACF;QAEA,MAAM,aAAa,OAAO,IAAI,CAAC,OAAO;QACtC,MAAM,WAAW,KAAK,IAAI,CAAC,OAAO;QAElC,mDAAmD;QACnD,IAAI,YAAY,SAAS,cAAc,UAAU,SAAS,YAAY;YACpE,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE;YAC/D,MAAM,eAAe,UAAU,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;YAE3D,IAAI,CAAC,kBAAkB,CAAC,cAAc;gBACpC;YACF;YAEA,iEAAiE;YACjE,MAAM,cACJ,eAAe,eAAe,KAAK,aAAa,eAAe;YAEjE,IAAI,CAAC,aAAa;gBAChB;YACF;YAEA,8DAA8D;YAC9D,MAAM,mBAAmB,UACtB,MAAM,CAAC,CAAC,IAAM,EAAE,eAAe,KAAK,eAAe,eAAe,EAClE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAEzC,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE;YACrE,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;YAEnE,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;gBACtC;YACF;YAEA,8BAA8B;YAC9B,MAAM,qBAAqB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,UAAU;YAEjE,qDAAqD;YACrD,MAAM,oBAAoB,mBAAmB,GAAG,CAAC,CAAC,UAAU,QAAU,CAAC;oBACrE,IAAI,OAAO,SAAS,EAAE;oBACtB,UAAU,QAAQ;gBACpB,CAAC;YAED,oDAAoD;YACpD,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAC;gBACtC,MAAM,cAAc,kBAAkB,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,SAAS,EAAE;gBAC1E,OAAO,cAAc;oBAAE,GAAG,QAAQ;oBAAE,UAAU,YAAY,QAAQ;gBAAC,IAAI;YACzE;YACA,YAAY,YAAY,CAAC,mBAAmB;YAE5C,kCAAkC;YAClC,wBAAwB,MAAM,CAAC;gBAC7B;gBACA;gBACA;YACF;QACF;QAEA,yCAAyC;QACzC,IAAI,YAAY,SAAS,cAAc,UAAU,SAAS,cAAc;YACtE,MAAM,aAAa,OAAO,OAAO,EAAE;YACnC,MAAM,cAAc,WAAW,eAAe,IAAI;YAClD,MAAM,YAAY,SAAS,OAAO;YAElC,gCAAgC,YAAY,aAAa;QAC3D;QAEA,2CAA2C;QAC3C,IAAI,YAAY,SAAS,WAAW,UAAU,SAAS,cAAc;YACnE,MAAM,eAAe,WAAW,OAAO;YACvC,MAAM,gBAAgB,SAAS,OAAO;YAEtC,0DAA0D;YAC1D,IAAI,iBAAiB,eAAe;gBAClC,2BAA2B,cAAc;YAC3C;QACF;QAEA,0BAA0B;QAC1B,IAAI,YAAY,SAAS,WAAW,UAAU,SAAS,SAAS;QAC9D,wDAAwD;QACxD,kDAAkD;QACpD;IACF;IAEA,iBAAiB;IACjB,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,wBAAwB;IAC1B;IAEA,MAAM,2BAA2B;QAC/B,IAAI,gBAAgB;YAClB,qBAAqB;YACrB,oBAAoB,MAAM,CAAC;gBAAE,IAAI;YAAe;QAClD;IACF;IAEA,MAAM,wCAAwC;QAC5C,IAAI,gBAAgB;YAClB,qBAAqB;YACrB,iCAAiC,MAAM,CAAC;gBAAE,IAAI;YAAe;QAC/D;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,kBAAkB;QAClB,sBAAsB;IACxB;IAEA,qCAAqC;IACrC,MAAM,0BAA0B,CAAC;QAC/B,uBAAuB,CAAC,OACtB,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO,cAC3B;mBAAI;gBAAM;aAAW;IAE7B;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;4DAAE,CAAC,MAAM;gBAEhB,8DAA8D;gBAC9D,2EAA2E;gBAC3E,MAAM,aAAa,KAAK,IAAI,EAAE,eAAe;gBAE7C,IAAI,cAAc,UAAU,mBAAmB,EAAE;oBAE/C,uEAAuE;oBACvE,MAAM,mBAAmB,UAAU,GAAG;yFAAC,CAAC,IACtC,UAAU,mBAAmB,EAAE,SAAS,EAAE,EAAE,IACxC;gCAAE,GAAG,CAAC;gCAAE,iBAAiB;4BAAW,IACpC;;oBAGN,gDAAgD;oBAChD,YAAY,YAAY,CAAC,mBAAmB;oBAE5C,4CAA4C;oBAC5C,MAAM,WAAW;wBACf,IAAI;wBACJ,OAAO,UAAU,KAAK;wBACtB,OAAO,UAAU,KAAK;wBACtB,WAAW,UAAU,SAAS;wBAC9B,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;wBACjC,UAAU,iBAAiB,MAAM;4EAC/B,CAAC,IAAM,EAAE,eAAe,KAAK;;oBAEjC;oBAEA,YAAY,YAAY,CAAC,gBAAgB;2BAAI;wBAAgB;qBAAS;gBACxE;gBAEA,iEAAiE;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;gBAC5D,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBAEA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAGF,uCAAuC;gBACvC,uBAAuB,EAAE;gBACzB,iBAAiB;gBACjB,qBAAqB;YACvB;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,EAAE,qBAAqB;gBACrC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,kDAAkD;IAClD,MAAM,gCAAgC;QACpC,IAAI,oBAAoB,MAAM,KAAK,GAAG;YACpC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,EAAE;gBACX,MAAM;YACR;YAEF;QACF;QAEA,qBAAqB;QAErB,gFAAgF;QAChF,qFAAqF;QACrF,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,IAAK,oBAAoB,QAAQ,CAAC,EAAE,EAAE;QACjF,MAAM,cAAc,kBAAkB,MAAM,GAAG,IAC3C,KAAK,GAAG,IAAI,kBAAkB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KACjD,eAAe,MAAM,GAAG;QAE5B,iDAAiD;QACjD,oBAAoB,MAAM,CAAC;YACzB,OAAO,EAAE;YACT,OAAO;YACP,WAAW;YACX;QACF;IACF;IAEA,sCAAsC;IACtC,MAAM,wBAAwB,CAAC,SAAiB;QAC9C,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,6CAA6C;IAC7C,MAAM,yBAAyB;QAC7B,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,mIAAA,CAAA,sBAAmB;QAC/B,SAAS;4DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAe;gBACzD,IAAI,gBAAgB,WAAW;oBAC7B,YAAY,iBAAiB,CAAC;wBAAE,UAAU;oBAAwB;gBACpE;gBACA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,sBAAsB;gBACtB,oBAAoB;gBACpB,qBAAqB;YACvB;;QACA,OAAO;4DAAE;gBACP,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;gBAEF,qBAAqB;YACvB;;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,iBAAiB,IAAI,IAAI;YAC5B,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,EAAE;gBACX,MAAM;YACR;YAEF;QACF;QAEA,qBAAqB;QAErB,MAAM,QAAQ,eAAe,IAAI,CAAC,CAAC,IAAqB,EAAE,EAAE,KAAK;QACjE,IAAI,CAAC,OAAO;QAEZ,qEAAqE;QACrE,MAAM,gBAAgB,eAAe,GAAG,CAAC,CAAC,IACxC,EAAE,EAAE,KAAK,UAAU;gBAAE,GAAG,CAAC;gBAAE,OAAO;YAAiB,IAAI;QAEzD,YAAY,YAAY,CAAC,gBAAgB;QAEzC,qCAAqC;QACrC,oBAAoB,MAAM,CAAC;YACzB,IAAI;YACJ,OAAO;YACP,OAAO,MAAM,KAAK;QACpB;IACF;IAEA,wCAAwC;IACxC,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,gIAAA,CAAA,cAAW;QACvB,SAAS;4DAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAkB;YAC9D;;QACA,OAAO;4DAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,EAAE,qBAAqB;gBACrC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;oBACf,SAAS,EAAE;oBACX,MAAM;gBACR;YAEJ;;IACF;IAEA,uDAAuD;IACvD,MAAM,gCAAgC,OACpC;QAEA,IAAI,kBAAkB,MAAM,KAAK,GAAG;QAEpC,qBAAqB;QAErB,IAAI;YACF,oDAAoD;YACpD,MAAM,cAAc,UAAU,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;YAEzF,8CAA8C;YAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,IAAK;gBACjD,MAAM,WAAW,iBAAiB,CAAC,EAAE;gBACrC,MAAM,aAAa;oBACjB,OAAO,SAAS,KAAK;oBACrB,YAAY,SAAS,UAAU;oBAC/B,MAAM,SAAS,IAAI,IAAI;oBACvB,aAAa,SAAS,WAAW,IAAI;oBACrC,WAAW,OAAO,SAAS,SAAS;oBACpC,iBAAiB,SAAS,eAAe,IAAI,EAAE;gBACjD;gBAEA,MAAM,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD,EAAE;oBAChB;oBACA;oBACA;oBACA,UAAU,cAAc,IAAI;gBAC9B;YACF;YAEA,6BAA6B;YAC7B,YAAY,iBAAiB,CAAC;gBAAE,UAAU;YAAkB;YAC5D,IAAI,gBAAgB,WAAW;gBAC7B,YAAY,iBAAiB,CAAC;oBAAE,UAAU;gBAAwB;YACpE;YAEA,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,GAAG,kBAAkB,MAAM,CAAC,CAAC,EAAE,EAAE,mBAAmB;gBAC7D,MAAM;YACR;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SACE,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,SAAS,EAAE;gBACX,MAAM;YACR;QAEJ,SAAU;YACR,qBAAqB;QACvB;IACF;IACA,qBAAqB;IACrB,MAAM,YAAY,gBAAgB,YAAY,oBAAoB;IAClE,MAAM,UAAU,gBAAgB,YAAY,kBAAkB;IAE9D,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,mBAAmB;IACnB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB,EAAE;;;;;;kCACpC,6LAAC;wBACC,SAAS;4BACP,IAAI,gBAAgB,WAAW;gCAC7B,YAAY,iBAAiB,CAAC;oCAAE,UAAU;gCAAwB;4BACpE,OAAO;gCACL,YAAY,iBAAiB,CAAC;oCAAE,UAAU;gCAAkB;4BAC9D;wBACF;wBACA,WAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,CAAC,uBAAuB,SAAS,IAChC,0BAA0B,SAAS,IACnC,oBAAoB,SAAS,IAC7B,iCAAiC,SAAS,IAC1C,wBAAwB,SAAS,IACjC,iBAAiB,mBAAK,6LAAC,2IAAA,CAAA,iBAAc;;;;;0BAGvC,6LAAC,4IAAA,CAAA,mBAAgB;gBACf,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,WAAW;gBACX,UAAU,UAAU,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,IAAI;;;;;;YAIpF,mBAAmB,gBAAgB,SAAS,KAAK,yBAChD,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,UAAU;gBACV,WAAW;;;;;;YAKd,mBAAmB,gBAAgB,SAAS,KAAK,yBAChD,6LAAC,kJAAA,CAAA,yBAAsB;gBACrB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,UAAU;gBACV,WAAW;;;;;;0BAKf,6LAAC,8IAAA,CAAA,qBAAkB;gBACjB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,gBAAgB;;;;;;YAIjB,gCACC,6LAAC,8IAAA,CAAA,qBAAkB;gBACjB,WAAW;gBACX,cAAc;gBACd,aAAa;gBACb,WAAW;gBACX,eAAe,eAAe,IAAI,CAChC,CAAC,IAAqB,EAAE,EAAE,KAAK;gBAEjC,WAAW;gBACX,gBAAgB;;;;;;0BAKpB,6LAAC,6IAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,SAAS,IAAM,+BAA+B;gBAC9C,WAAW;gBACX,OAAQ,EAAE;gBACV,aAAc,EAAE;gBAChB,mBAAoB,EAAE;gBACtB,kBAAmB,EAAE;gBACrB,oBAAmB;;;;;;0BAIrB,6LAAC,oJAAA,CAAA,2BAAwB;gBACvB,WAAW;gBACX,cAAc;gBACd,iBAAiB;gBACjB,8BAA8B;gBAC9B,YACE,oBAAoB,SAAS,IAC7B,iCAAiC,SAAS;;;;;;0BAK9C,6LAAC,4JAAA,CAAA,UAAuB;gBACtB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,gBAAgB;;;;;;0BAIlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoB;oCAAE,EAAE;oCAAe;;;;;;;4BAGpD,oBAAoB,MAAM,GAAG,kBAC5B,6LAAC;gCACC,WAAU;gCACV,SAAS;gCACT,UAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCACjB,EAAE;oCAAe;oCAAG,oBAAoB,MAAM;oCAAC;;;;;;qDAGlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS;4CACP,wBAAwB;4CACxB,IAAI,CAAC,eAAe;gDAClB,iBAAiB;4CACnB,OAAO;gDACL,iBAAiB;gDACjB,uBAAuB,EAAE;4CAC3B;wCACF;;0DAEA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;4CACjB,gBAAgB,EAAE,qBAAqB,EAAE;;;;;;;kDAG5C,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB;;0DAEpC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,MAAM;;;;;;4CACjB,EAAE;;;;;;;;;;;;;;;;;;;kCAMX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,iBAAiB;gCAChC,OAAO,EAAE;0CAET,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,MAAM;;;;;;;;;;;0CAEb,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,sBAAsB;gCACrC,OAAO,EAAE;0CAET,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAuBtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8JAAA,CAAA,aAAU;oBACT,SAAS;oBACT,oBAAoB,8JAAA,CAAA,gBAAa;oBACjC,WAAW;8BAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;wBACd,OAAO;+BACF,UAAU,GAAG,CAAC,CAAC,WAAa,SAAS,EAAE;+BACvC,eAAe,GAAG,CAAC,CAAC,QAAyB,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;yBACpE;wBACD,UAAU,sKAAA,CAAA,8BAA2B;kCAErC,cAAA,6LAAC;4BAAI,WAAU;sCACZ,UAAU,MAAM,KAAK,IACpB,sBAAsB;0CACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,wBAAwB;4CACvC,WAAU;4CACV,UAAU,CAAC;;8DAEX,6LAAC,qNAAA,CAAA,aAAU;oDAAC,MAAM;;;;;;gDACjB,EAAE;;;;;;;;;;;;;;;;;uCAKT,uEAAuE;4BACvE,iBAAiB,GAAG,CAAC,CAAC;gCACpB,IAAI,KAAK,IAAI,KAAK,SAAS;oCACzB,MAAM,QAAQ,KAAK,IAAI;oCACvB,2EAA2E;oCAC3E,MAAM,iBAAiB,MAAM,QAAQ,IAAI,EAAE;oCAE3C,qBACE,6LAAC;wCAA8B,WAAU;kDACvC,cAAA,6LAAC,sJAAA,CAAA,oBAAiB;4CAChB,IAAI,MAAM,EAAE;4CACZ,OAAO,MAAM,KAAK;4CAClB,WAAW;4CACX,WAAW,MAAM,SAAS;4CAC1B,eAAe,MAAM,aAAa;4CAClC,cAAc;4CACd,aAAa;4CACb,eAAe;4CACf,sBAAsB;4CACtB,gBAAgB,CAAC;gDACf,mBAAmB;gDACnB,IAAI,SAAS,SAAS,KAAK,SAAS;oDAClC,8BAA8B;gDAChC,OAAO;oDACL,yBAAyB;gDAC3B;4CACF;4CACA,kBAAkB,CAAC;gDACjB,mBAAmB;gDACnB,+BAA+B;4CACjC;4CACA,qBAAqB,CAAC;gDACpB,mBAAmB;gDACnB,0BAA0B,MAAM,CAAC;oDAC/B,IAAI,SAAS,EAAE;oDACf;oDACA;gDACF;4CACF;4CACA,oBAAoB,CAAC;gDACnB,wBAAwB,MAAM,CAAC;oDAC7B;oDACA;oDACA;gDACF;4CACF;4CACA,6BAA6B;4CAC7B,wBAAwB;4CACxB,WAAW,uBAAuB,MAAM,EAAE;4CAC1C,gBAAgB;4CAChB,iBAAiB;4CACjB,iBAAiB;4CACjB,aAAa;4CACb,qBAAqB;4CACrB,eAAe;4CACf,aAAa;4CACb,qBAAqB;4CACrB,wBAAwB;4CACxB,kBAAkB;;;;;;uCAlDZ,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;gCAsDjC,OAAO;oCACL,MAAM,WAAW,KAAK,IAAI;oCAC1B,qBACE,6LAAC;wCAAoC,WAAU;kDAC7C,cAAA,6LAAC,iJAAA,CAAA,eAAY;4CACX,UAAU;4CACV,QAAQ;gDACN,mBAAmB;gDACnB,IAAI,SAAS,SAAS,KAAK,SAAS;oDAClC,8BAA8B;gDAChC,OAAO;oDACL,yBAAyB;gDAC3B;4CACF;4CACA,UAAU;gDACR,mBAAmB;gDACnB,+BAA+B;4CACjC;4CACA,aAAa;gDACX,mBAAmB;gDACnB,0BAA0B,MAAM,CAAC;oDAC/B,IAAI,SAAS,EAAE;oDACf;oDACA;gDACF;4CACF;4CACA,eAAe;4CACf,YAAY,oBAAoB,QAAQ,CAAC,SAAS,EAAE;4CACpD,gBAAgB,IACd,wBAAwB,SAAS,EAAE;;;;;;uCA1B/B,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;;;;;gCA+BvC;4BACF;;;;;;;;;;;;;;;;;;;;;YAQT,uCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;;gCAAgC;gCACjB,0BAA0B,MAAM;gCAAC;;;;;;;sCAE7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAgB,WAAU;8CAAiC;;;;;;8CAG1E,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,aAAY;oCACZ,WAAU;oCACV,SAAS;oCACT,WAAW,CAAC;wCACV,IAAI,EAAE,GAAG,KAAK,SAAS;4CACrB;wCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4CAC7B;wCACF;oCACF;;;;;;;;;;;;sCAGJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU,CAAC,aAAa,IAAI;oCAC5B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,UAAU,MAAM,GAAG,mBAClB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAC,6DAA6D,EACvE,CAAC,sBAAsB,oCACvB;oBACF,SAAS,IAAM,wBAAwB;oBACvC,UAAU,CAAC;;sCAEX,6LAAC,qNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;wBACjB,EAAE;;;;;;;;;;;;;;;;;;AAMf;GAl9CM;;QAWY,8JAAA,CAAA,aAAU;QAShB,yMAAA,CAAA,kBAAe;QAyCR,4JAAA,CAAA,cAAW;QACR,yLAAA,CAAA,iBAAc;QAewD,8KAAA,CAAA,WAAQ;QAOD,8KAAA,CAAA,WAAQ;QAwD3D,8KAAA,CAAA,WAAQ;QA0JvB,iLAAA,CAAA,cAAW;QA2BR,iLAAA,CAAA,cAAW;QA4BjB,iLAAA,CAAA,cAAW;QA+DE,iLAAA,CAAA,cAAW;QAyDpB,iLAAA,CAAA,cAAW;QA2DD,iLAAA,CAAA,cAAW;QA6BhB,iLAAA,CAAA,cAAW;QA4BX,iLAAA,CAAA,cAAW;QAsPpB,iLAAA,CAAA,cAAW;QA8GX,iLAAA,CAAA,cAAW;QA2DX,iLAAA,CAAA,cAAW;;;KAx+BnC", "debugId": null}}, {"offset": {"line": 10282, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Kobo-project/data-analysis-tool-frontend/app/%5Blocale%5D/%28main%29/project/%5BhashedId%5D/form-builder/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FormPreview } from \"@/components/form-preview\";\r\nimport { useState } from \"react\";\r\nimport { Question, QuestionGroup } from \"@/types/formBuilder\";\r\nimport { useQuery } from \"@tanstack/react-query\";\r\nimport { fetchQuestions } from \"@/lib/api/form-builder\";\r\nimport { fetchQuestionGroups } from \"@/lib/api/question-groups\";\r\nimport Spinner from \"@/components/general/Spinner\";\r\nimport { useParams } from \"next/navigation\";\r\nimport { decode } from \"@/lib/encodeDecode\";\r\nimport { FormBuilder } from \"@/components/form-builder/FormBuilder\";\r\nimport { useProjectPermissions } from \"@/hooks/useProjectPermissions\";\r\nimport { fetchProjectById } from \"@/lib/api/projects\";\r\nimport { Project } from \"@/types\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nexport default function FormBuilderPage() {\r\n  const [isPreviewMode, setIsPreviewMode] = useState(false);\r\n\r\n  const { hashedId } = useParams();\r\n  const { user } = useAuth();\r\n  const hashedIdString = hashedId as string;\r\n\r\n  const projectId = Number(decode(hashedIdString));\r\n  const t = useTranslations();\r\n\r\n  // Remove the separate questions query since FormBuilder now handles data loading internally\r\n\r\n  // Fetch question groups for the project\r\n  const { data: questionGroups = [] } = useQuery<QuestionGroup[]>({\r\n    queryKey: [\"questionGroups\", projectId],\r\n    queryFn: () => fetchQuestionGroups({ projectId: projectId! }),\r\n    enabled: !!projectId,\r\n  });\r\n\r\n  const {\r\n    data: projectData,\r\n    isLoading: projectLoading,\r\n    isError: projectError,\r\n  } = useQuery<Project>({\r\n    queryKey: [\"projects\", user?.id, projectId],\r\n    queryFn: () => fetchProjectById({ projectId: projectId! }),\r\n    enabled: !!projectId && !!user?.id,\r\n  });\r\n\r\n  const permissions = useProjectPermissions({\r\n    projectData,\r\n    user,\r\n  });\r\n\r\n  if (!hashedId || projectId === null) {\r\n    return (\r\n      <div className=\"error-message\">\r\n        <h1 className=\"text-red-500\">{t('invalidProjectId')}</h1>\r\n        <p className=\"text-neutral-700\">\r\n          {t('invalidProjectUrl')}\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Loading and error handling is now managed within FormBuilder component\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      {isPreviewMode ? (\r\n        <FormPreview\r\n          contextType=\"project\"\r\n          contextId={projectId}\r\n          onClose={() => setIsPreviewMode(false)}\r\n          hashedId={hashedIdString} // Pass hashedId\r\n        />\r\n      ) : (\r\n        <FormBuilder\r\n          setIsPreviewMode={setIsPreviewMode}\r\n          contextType=\"project\"\r\n          contextId={projectId}\r\n          permissions={permissions}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAhBA;;;;;;;;;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,iBAAiB;IAEvB,MAAM,YAAY,OAAO,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD;IAExB,4FAA4F;IAE5F,wCAAwC;IACxC,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAmB;QAC9D,UAAU;YAAC;YAAkB;SAAU;QACvC,OAAO;wCAAE,IAAM,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QAC3D,SAAS,CAAC,CAAC;IACb;IAEA,MAAM,EACJ,MAAM,WAAW,EACjB,WAAW,cAAc,EACzB,SAAS,YAAY,EACtB,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAW;QACpB,UAAU;YAAC;YAAY,MAAM;YAAI;SAAU;QAC3C,OAAO;wCAAE,IAAM,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE;oBAAE,WAAW;gBAAW;;QACxD,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC,MAAM;IAClC;IAEA,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE;QACxC;QACA;IACF;IAEA,IAAI,CAAC,YAAY,cAAc,MAAM;QACnC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAgB,EAAE;;;;;;8BAChC,6LAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;;;;;;;IAIX;IAEA,yEAAyE;IAEzE,qBACE,6LAAC;QAAI,WAAU;kBACZ,8BACC,6LAAC,oJAAA,CAAA,cAAW;YACV,aAAY;YACZ,WAAW;YACX,SAAS,IAAM,iBAAiB;YAChC,UAAU;;;;;iCAGZ,6LAAC,gJAAA,CAAA,cAAW;YACV,kBAAkB;YAClB,aAAY;YACZ,WAAW;YACX,aAAa;;;;;;;;;;;AAKvB;GAlEwB;;QAGD,qIAAA,CAAA,YAAS;QACb,oHAAA,CAAA,UAAO;QAId,yMAAA,CAAA,kBAAe;QAKa,8KAAA,CAAA,WAAQ;QAU1C,8KAAA,CAAA,WAAQ;QAMQ,iIAAA,CAAA,wBAAqB;;;KA7BnB", "debugId": null}}]}