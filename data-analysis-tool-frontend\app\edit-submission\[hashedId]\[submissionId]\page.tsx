"use client";

import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import axios from "@/lib/axios";
import { decode } from "@/lib/encodeDecode";
import { Submission } from "@/app/[locale]/(main)/project/[hashedId]/data/table/columns";
import Spinner from "@/components/general/Spinner";
import { EditForm } from "@/components/form-preview/editForm";

const fetchSubmissions = async (projectId: number, submissionId: number) => {
  const { data } = await axios.get(`/form-submissions/${projectId}`);
  const submissions = data.data.formSubmissions as Submission[];
  const submission = submissions.find((s) => s.id === submissionId);
  if (!submission) {
    throw new Error("Submission not found");
  }
  return submission;
};

export default function EditSubmissionPage() {
  const { hashedId, submissionId } = useParams();
  const projectId = decode(hashedId as string);
  const parsedSubmissionId = Number(submissionId);

  // Validate projectId and submissionId
  if (projectId === null || isNaN(parsedSubmissionId)) {
    return <div>Error: Invalid project or submission ID.</div>;
  }

  // Remove separate questions fetching since EditForm now handles data loading internally

  const {
    data: submission,
    isLoading: submissionLoading,
    isError: submissionError,
    refetch: refetchSubmission,
  } = useQuery<Submission>({
    queryKey: ["submission", projectId, parsedSubmissionId],
    queryFn: () => fetchSubmissions(projectId, parsedSubmissionId),
    enabled: !!projectId && !!parsedSubmissionId,
  });

  if (submissionLoading) {
    return <Spinner />;
  }

  if (submissionError || !submission) {
    return (
      <p className="text-sm text-red-500">
        Error loading submission. Please try again.
      </p>
    );
  }

  const handleSave = () => {
    // Notify table to refetch if there's an opener window
    if (window.opener) {
      window.opener.postMessage({ type: "REFETCH_SUBMISSIONS" }, "*");
    }
    // Refetch the submission data to update the UI
    refetchSubmission();
  };

  return (
    <div className="min-h-screen w-full bg-neutral-100 dark:bg-gray-900 flex flex-col items-center p-4 md:p-6">
      <EditForm
        submission={submission}
        projectId={projectId}
        submissionId={parsedSubmissionId}
        onSave={handleSave}
      />
    </div>
  );
}